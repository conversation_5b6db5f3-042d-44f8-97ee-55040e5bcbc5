{"common": {"welcome": "Willkommen bei Swiss Knowledge Hub", "signIn": "Anmelden", "users": "<PERSON><PERSON><PERSON>", "selectAll": "Alle auswählen", "deselectAll": "Alle abwählen", "add": "Hinzufügen", "adding": "Hinzufügen...", "assign": "<PERSON><PERSON><PERSON><PERSON>", "assigning": "<PERSON><PERSON><PERSON><PERSON>...", "signInNow": "Jetzt Anmelden", "signOut": "Abmelden", "register": "Registrieren", "forgotPassword": "Passwort vergessen", "profile": "Profil", "settings": "Einstellungen", "dashboard": "Dashboard", "organizations": "Organisationen", "createOrganization": "Organisation erstellen", "organizationSettings": "Organisationseinstellungen", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invitations": "Einladungen", "email": "E-Mail", "password": "Passwort", "workspaces": "Arbeitsbereiche", "workspace": "Arbeitsplatz", "name": "Name", "description": "Beschreibung", "save": "Speichern", "cancel": "Abbrechen", "uploading": "Wird hochgeladen...", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "creating": "Wird erstellt...", "loading": "Wird geladen...", "move": "Bewegen", "moving": "Bewegen...", "submit": "<PERSON><PERSON><PERSON><PERSON>", "invite": "Einladen", "remove": "Entfernen", "rename": "Umbenennen", "edit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "german": "De<PERSON>ch", "firstName": "<PERSON><PERSON><PERSON>", "contentImportedAsHtml": "Der Inhalt wird als HTML importiert.", "lastName": "Nachname", "saving": "Wird gespeichert...", "saveChanges": "Änderungen speichern", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "returnToSignIn": "Zurück zur Anmeldung", "emailSent": "E-Mail gesendet", "checkEmail": "Überprüfen Sie Ihre E-Mail", "tryAnotherEmail": "Andere E-Mail-Adresse versuchen", "notifications": "Benachrichtigungen", "sharedThreads": "<PERSON><PERSON><PERSON> Threads", "privateShared": "Privat geteilt", "publicShared": "<PERSON><PERSON><PERSON><PERSON> geteilt", "unread": "<PERSON><PERSON><PERSON><PERSON>", "comments": "Kommentare", "mentions": "Erwähnungen", "reply": "Antworten", "resolve": "<PERSON><PERSON><PERSON>", "reopen": "<PERSON><PERSON><PERSON>", "yourOrganizations": "Ihre Organisationen", "current": "Aktuell", "optional": "optional", "back": "Zurück", "you": "<PERSON><PERSON>", "actions": "Aktionen", "user": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Löschen", "update": "Aktualisieren", "filter": "Filtern", "filters": "Filter", "sort": "<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "previous": "Zurück", "select": "Auswählen", "noPermission": "Sie haben keine Berechtigung, diese Aktion auszuführen", "accessDenied": "<PERSON><PERSON><PERSON> verweigert", "noPermissionToView": "Sie haben keine Berechtigung, diesen Inhalt anzuzeigen", "fetch": "Abrufen", "fetching": "Wird abgerufen...", "preview": "Vorschau", "title": "Titel", "content": "Inhalt", "warning": "<PERSON><PERSON><PERSON>", "urlRequired": "URL ist erforderlich", "titleAndContentRequired": "Titel und Inhalt sind erforderlich", "all": "Alle", "selectUser": "Benutzer auswählen", "removing": "Wird entfernt...", "clear": "Z<PERSON>ücksetzen", "searchPlaceholder": "Suchen...", "role": "<PERSON><PERSON>", "pageInformation": "Seiteninformationen", "pageName": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Erstellt am", "updatedAt": "Aktualisiert am", "dismiss": "Verwerfen", "confirm": "Bestätigen", "member": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Wird gel<PERSON>t...", "view": "Anzeigen", "noDescription": "<PERSON><PERSON>", "error": "<PERSON><PERSON>", "success": "Erfolg", "syncing": "Synchronisierung läuft...", "noUsers": "<PERSON><PERSON> gefunden", "inheritance": "Vererbung", "restriction": "Einschränkung", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cascadingRemoval": "Kaskadierende Entfernung", "indexingStatus": {"pending": "<PERSON><PERSON><PERSON><PERSON>", "processing": "Verarbeitung", "processingVideo": "Video wird verarbeitet", "indexed": "Indiziert", "failed": "Fehlgeschlagen", "startIndexing": "Indizierung starten", "retryIndexing": "Indizierung wiederholen"}}, "role": {"member": "<PERSON><PERSON><PERSON><PERSON>", "editor": "<PERSON><PERSON><PERSON>", "admin": "Administrator", "memberDesc": "<PERSON><PERSON> anzeigen", "editorDesc": "Anzeigen und bearbeiten", "adminDesc": "Vollzugriff"}, "auth": {"loginTitle": "Melden Sie sich in Ihrem Konto an", "registerTitle": "<PERSON><PERSON><PERSON><PERSON> ein neues Konto", "emailPlaceholder": "Geben Sie Ihre E-Mail-Adresse ein", "passwordPlaceholder": "Geben Sie Ihr Passwort ein", "confirmPassword": "Passwort bestätigen", "dontHaveAccount": "Haben <PERSON> noch kein Konto?", "alreadyHaveAccount": "Haben <PERSON> bereits ein Konto?", "signUp": "Registrieren", "resetPassword": "Passwort zurücksetzen", "resetPasswordDescription": "Geben Sie Ihre E-Mail-Adresse ein, um einen Link zum Zurücksetzen des Passworts zu erhalten", "resetEmailSent": "Überprüfen Sie Ihre E-Mail für Anweisungen zum Zurücksetzen", "resetEmailMessage": "Wir haben eine E-Mail an {email} mit Anweisungen zum Zurücksetzen Ihres Passworts gesendet. Bitte überprüfen Sie Ihren Posteingang.", "sendResetInstructions": "Anweisungen zum Zurücksetzen senden", "accountInformation": "Kontoinformationen", "agreeToTerms": "Sie müssen den Nutzungsbedingungen zustimmen.", "passwordsDoNotMatch": "Passwörter stimmen nicht überein", "passwordMinLength": "Passwort muss mindestens 8 Zeichen lang sein.", "nameMinLength": "Name muss mindestens 2 <PERSON><PERSON><PERSON> lang sein.", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein.", "organizationNameMinLength": "Organisationsname muss mindestens 2 Zeichen lang sein.", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "signingIn": "Anmeldung läuft...", "signInSuccessful": "Anmeldung erfolgreich!", "registrationFailed": "Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "resettingPassword": "Passwort wird zurückgesetzt...", "passwordResetSuccess": "Passwort erfolgreich zurückgesetzt!", "iAgreeToThe": "Ich stimme den", "termsOfService": "Nutzungsbedingungen", "and": "und", "privacyPolicy": "Datenschutzrichtlinien", "continue": "Fortfahren", "completeSignUp": "Registrierung abschließen", "newPassword": "Neues Passwort", "createNewPassword": "<PERSON><PERSON><PERSON><PERSON> ein neues Passwort", "confirmNewPassword": "Neues Passwort bestätigen", "confirmNewPasswordPlaceholder": "Bestätigen Sie Ihr neues Passwort", "setNewPasswordBeforeContinuing": "<PERSON>e müssen ein neues Passwort festlegen, bevor <PERSON> fortfahren können.", "verifyEmailBeforeSignIn": "Bitte bestätigen Sie Ihre E-Mail-Adresse, bevor <PERSON> sich anmelden", "signInFailedCheckCredentials": "Anmeldung fehlgeschlagen. Bitte überprüfen Sie Ihre Anmeldedaten.", "signInFailedTryAgain": "Anmeldung fehlgeschlagen. Bitte versuchen Sie es erneut.", "registrationError": "Registrierungsfehler: {error}"}, "organization": {"createOrganization": "Organisation erstellen", "createDescription": "Fügen Sie eine neue Organisation hinzu, um mit Ihrem Team zusammenzuarbeiten.", "namePlaceholder": "Acme GmbH", "descriptionOptional": "Beschreibung (Optional)", "descriptionPlaceholder": "Kurze Beschreibung Ihrer Organisation", "urlOptional": "Website-URL (Optional)", "urlPlaceholder": "https://beispiel.ch", "creating": "Wird erstellt...", "createSuccess": "Organisation erfolgreich erstellt", "createFailed": "Fehler beim Erstellen der Organisation", "switching": "Organisation wird gewechselt...", "switchSuccess": "Organisation erfolgreich gewechselt", "switchFailed": "Fehler beim Wechseln der Organisation", "notFound": "Organisation nicht gefunden", "searchPlaceholder": "Organisation suchen...", "noOrganizationsFound": "Keine Organisation gefunden.", "general": "Allgemein", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invitations": "Einladungen", "organizationInfo": "Organisationsinformationen", "updateDetails": "Aktualisieren Sie Ihre Organisationsdetails und -profil.", "nameLabel": "Name", "nameDescription": "Der Name Ihrer Organisation.", "slugLabel": "Slug", "slugPlaceholder": "acme", "slugDescription": "Die URL-freundliche Kennung für Ihre Organisation.", "descriptionLabel": "Beschreibung", "descriptionHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, was Ihre Organisation macht.", "urlLabel": "Website-URL", "urlDescription": "Die Website-Adresse Ihrer Organisation.", "saving": "Wird gespeichert...", "saveChanges": "Änderungen speichern", "teamMembers": "Teammitglieder", "manageTeamMembers": "Verwalten Sie Ihre Teammitglieder und deren Rollen.", "inviteUser": "<PERSON><PERSON><PERSON> e<PERSON>n", "pendingInvitations": "Ausstehende Einladungen", "managePendingInvitations": "Verwalten Sie Einladungen an neue Teammitglieder.", "noInvitations": "<PERSON>ine ausstehenden Einladungen", "whenInviteMembers": "<PERSON>n Si<PERSON> Mi<PERSON>gliede<PERSON> e<PERSON>, werden sie hier angezeigt.", "creatingOrganization": "Organisation wird erstellt...", "tellUsAboutOrganization": "Erzählen Sie uns über Ihre Organisation", "nameMinLength": "Der Organisationsname muss mindestens 2 Zeichen lang sein.", "slugMinLength": "Der Slug muss mindestens 2 Zeichen lang sein.", "validUrl": "<PERSON>te geben Si<PERSON> eine gültige URL ein", "settings": "Organisationseinstellungen", "settingsDescription": "Verwalten Sie Ihre Organisationseinstellungen und -präferenzen.", "loading": "Organisation wird geladen...", "selectFromSidebar": "<PERSON><PERSON> diese Seite nicht geladen wird, wählen Si<PERSON> bitte eine Organisation aus dem Dropdown-Menü in der Seitenleiste aus.", "updateFailed": "Fehler beim Aktualisieren der Organisation", "updating": "Unternehmen wird aktualisiert...", "updateSuccess": "Unternehmen erfolgreich aktualisiert", "slugFormat": "Slug darf nur Kleinbuchstaben, Zahlen und Bindestriche enthalten."}, "socialAccount": {"disconnectConfirm": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> Ihr {platform}-Konto trennen möchten?", "disconnectWarning": "Durch das Trennen Ihres Kontos wird der Zugriff auf die {platform}-Authentifizierung widerrufen.", "connectAccount": "Konto verbinden", "disconnectAccount": "<PERSON><PERSON> trennen", "connected": "Verbunden", "disconnected": "Getrennt"}, "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrator", "member": "<PERSON><PERSON><PERSON><PERSON>", "makeAdmin": "Zum Administrator machen", "makeMember": "<PERSON><PERSON> machen", "removeMember": "<PERSON><PERSON><PERSON><PERSON> entfernen", "confirmRemove": "<PERSON>d <PERSON> sic<PERSON>, dass <PERSON> {name} aus dieser Organisation entfernen möchten? Der Zugriff auf alle Ressourcen geht verloren.", "role": "<PERSON><PERSON>", "organizationRole": "Organisationsrolle", "workspaceRole": "Arbeitsbereichsrolle", "organizationRoleTooltip": "Die Rolle des Benutzers innerhalb der gesamten Organisation. Dies bestimmt ihre globalen Berechtigungen für alle Arbeitsbereiche.", "workspaceRoleTooltip": "Die spezifische Rolle des Benutzers in diesem Arbeitsbereich. Dies bestimmt ihre Berechtigungen nur für diesen bestimmten Arbeitsbereich.", "joined": "Beigetreten", "currentUser": "Aktueller Benutzer", "removeTeamMember": "<PERSON><PERSON><PERSON><PERSON> ent<PERSON>", "roleUpdatedToSuccess": "<PERSON><PERSON> erfolgreich auf {role} aktualisiert", "updateRoleFailed": "Fehler beim Aktualisieren der Rolle", "memberRemovedSuccess": "<PERSON>t<PERSON><PERSON> erfolg<PERSON>ich entfernt", "removeMemberFailed": "Fehler beim Entfernen des Mitglieds", "title": "<PERSON><PERSON>", "rolesDescription": "Verwalten Sie integrierte und benutzerdefinierte Rollen zur Kontrolle von Benutzerberechtigungen in Ihrer Organisation", "builtInRoles": "Integrierte Rollen", "builtInRolesDescription": "Standardrollen mit vordefinierten Berechtigungen, die nicht geändert werden können", "builtInRolesTooltip": "Dies sind systemdefinierte Rollen mit festen Berechtigungen. Sie können sie nicht bearbeiten oder löschen.", "builtIn": "Integrier<PERSON>", "customRoles": "Benutzerdefinierte Rollen", "customRolesDescription": "<PERSON><PERSON><PERSON><PERSON> und verwalten Sie benutzerdefinierte Rollen mit spezifischen Berechtigungen", "customRolesTooltip": "<PERSON><PERSON><PERSON><PERSON> Sie benutzerdefinierte Rollen mit spezifischen Berechtigungen, die auf die Bedürfnisse Ihrer Organisation zugeschnitten sind.", "createRole": "<PERSON><PERSON> erstellen", "editRole": "<PERSON><PERSON> bearbeiten", "basicInfo": "Grundlegende Informationen", "assignWorkspaces": "Arbeitsbereiche zuweisen", "selectWorkspaces": "Arbeitsbereiche auswählen", "roleCreatedSuccess": "Rolle erfolgreich erstellt", "roleCreateFailed": "Fehler beim Erstellen der Rolle", "roleUpdatedSuccess": "Rolle erfolgreich aktualisiert", "roleUpdateFailed": "Fehler beim Aktualisieren der Rolle", "roleNotFound": "Rolle nicht gefunden", "createRoleDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue benutzerdefinierte Rolle mit spezifischen Berechtigungen", "editRoleDescription": "Bearbeiten Sie diese benutzerdefinierte Rolle und ihre Berechtigungen", "deleteRoleTitle": "Rolle löschen", "deleteRoleDescription": "Diese Aktion kann nicht rückgängig gemacht werden. Die Rolle wird dauerhaft gelöscht.", "noRoles": "Noch keine benutzerdefinierten Rollen erstellt", "noRolesDescription": "<PERSON><PERSON><PERSON><PERSON> Sie Ihre erste benutzerdefinierte Rolle, um spezifische Berechtigungen für Ihre Teammitglieder zu definieren", "noDescription": "Keine Beschreibung angegeben", "noRolesFound": "<PERSON><PERSON> benutzerdefinierten Rollen gefunden", "deleteRole": "Rolle löschen", "permission": "Berechtigung", "permissionsCount": "Berechtigungen", "permissions": "Berechtigungen", "customRole": "Benutzerdefinierte Rolle", "selectCustomRole": "Benutzerdefinierte Rolle auswählen", "selectRole": "Rolle auswählen", "ownerOnly": "Nur Eigentümer können benutzerdefinierte Rollen verwalten", "name": "Name", "description": "Beschreibung", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtInRoleNames": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrator", "member": "<PERSON><PERSON><PERSON><PERSON>"}, "builtInRoleDescriptions": {"owner": "Vollzugriff auf alle Organisationsfunktionen und -einstellungen", "admin": "<PERSON>er<PERSON><PERSON> von Arbeitsbereichen, Benutzern und den meisten Organisationseinstellungen", "member": "Grundlegender Zugriff auf zugewiesene Arbeitsbereiche"}, "editRolePermissions": "Berechtigungen für {role} bearbeiten", "editRoleTitle": "{role}-<PERSON><PERSON>", "roleConfiguration": "{role}-Rollenkonfiguration", "selectPermissionsFor": "<PERSON><PERSON><PERSON>en Sie die Berechtigungen aus, die Benutzer mit der {role}-<PERSON><PERSON> haben sollen.", "permissionsTitle": "Berechtigungen", "permissionsGroup": "{resource} Berechtigungen", "ownerRoleCannotBeEdited": "Die Eigentümer-Rolle kann nicht bearbeitet werden", "roleUpdatedSuccessfully": "{role}-Rolle erfolgreich aktualisiert", "failedToUpdateRole": "Fehler beim Aktualisieren der Rolle", "savingChanges": "Änderungen werden gespeichert...", "saveChanges": "Änderungen speichern", "tooltipMessages": {"ownersHaveAllPermissions": "Eigentümer haben standardmäßig alle Berechtigungen", "loadingPermissionDetails": "Berechtigungsdetails werden geladen...", "permissionDetailsUnavailable": "Berechtigungsdetails nicht verfügbar", "roleHasNoPermissions": "{role}-<PERSON><PERSON> hat keine Berechtigungen zugewiesen", "roleHasAllPermissions": "{role}-<PERSON><PERSON> hat alle {count} Berechtigungen", "permissionsIncluding": "{count} Berechtigungen einschließlich {permissions}{more}"}, "permissionSummary": {"allPermissions": "Alle Berechtigungen", "loadingPermissions": "Berechtigungen werden geladen...", "failedToLoad": "<PERSON><PERSON> beim <PERSON>", "noPermissionsAssigned": "<PERSON>ine Berechtigungen zugewiesen"}, "resources": {"WORKSPACE": "Arbeitsbereich", "PAGE": "Seite", "FOLDER": "<PERSON><PERSON><PERSON>", "FILE": "<PERSON><PERSON>"}, "actions": {"CREATE": "<PERSON><PERSON><PERSON><PERSON>", "READ": "<PERSON><PERSON>", "UPDATE": "Aktualisieren", "DELETE": "Löschen"}, "hierarchy": {"info": "Hierarchie-Info", "title": "Berechtigungshierarchie: Arbeitsbereich > Seite > Ordner > Dateien", "inheritance": "Vererbung: Berechtigungen höherer Ebenen werden nach unten vererbt", "restriction": "Einschränkung: <PERSON><PERSON> Arbeitsbereich-Zugriff = Ke<PERSON> Zugriff auf dessen Inhalte", "required": "Erforderlich: Zugriff auf übergeordnete Entitäten ist obligatorisch", "cascadingRemoval": "Kaskadierende Entfernung: Das Entfernen des Zugriffs auf jeder Ebene entfernt den Zugriff auf alle verschachtelten Elemente", "workspaceRemovalNote": "Das Entfernen des Arbeitsbereich-Zugriffs entfernt automatisch den Zugriff auf alle Seiten, Ordner und <PERSON>ien da<PERSON>."}, "tooltips": {"hierarchyInfo": "Berechtigungen werden vom Arbeitsbereich zu Seite zu Ordner zu Dateien vererbt", "missingParentPermissions": "Fehlende übergeordnete Berechtigungen", "missingParentPermission": "Fehlende übergeordnete Berechtigung"}, "errorMessages": {"noRoleSelectedForDeletion": "Keine Rolle zum Löschen ausgewählt", "roleDeletedSuccessfully": "Rolle erfolgreich <PERSON>", "failedToDeleteRole": "Fehler beim Löschen der Rolle", "failedToLoadRolesAndPermissions": "<PERSON><PERSON> beim Laden von Rollen und Berechtigungen", "parentPermissionRequired": "<PERSON><PERSON> müssen zu<PERSON>t {action}-Zugriff auf {parentResource} gewähren", "permissionGrantedWithCascade": "{action}-Zugriff auf {resource} und alle verschachtelten Elemente gewährt", "permissionRemovedWithCascade": "{action}-<PERSON><PERSON><PERSON> von {resource} und allen verschachtelten Elementen entfernt", "cannotGrantWithoutParent": "{action}-Zugriff auf {resource} kann nicht ohne {action}-Zugriff auf {parentResource} gewährt werden", "allPermissionsGrantedWithCascade": "Alle {resource}-Berechtigungen gewährt und auf verschachtelte Elemente übertragen", "allPermissionsRemovedWithCascade": "Alle {resource}-Berechtigungen und verschachtelte Elemente entfernt"}}, "changelog": {"title": "Was ist neu", "newFeatures": "Neue Funktionen", "improvements": "Verbesserungen", "bugFixes": "Fehlerbehebungen", "announcement": "Ankündigung", "release": "Veröffentlichung", "hotfix": "Hotfix", "maintenance": "Wartung", "priority": {"low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch", "critical": "<PERSON><PERSON><PERSON>"}, "type": {"release": "Veröffentlichung", "hotfix": "Hotfix", "maintenance": "Wartung", "announcement": "Ankündigung"}, "viewChangelog": "Changelog anzeigen (Neue Updates verfügbar)", "markAsRead": "Als gelesen markieren", "dismissAll": "Alle verwerfen", "noChangelogs": "<PERSON><PERSON>dates", "noChangelogsDescription": "Sie sind auf dem neuesten Stand! Schauen Sie später nach neuen Funktionen und Verbesserungen.", "publishedOn": "Veröffentlicht am", "version": "Version", "commit": "Commit", "deployment": "Bereitstellung", "page": {"title": "Änderungsprotokoll", "subtitle": "Bleiben Sie auf dem Laufenden über neue Funktionen, Verbesserungen und Fehlerbehebungen", "search": "Suchen...", "filterByCategory": "<PERSON><PERSON> Kategorie filtern", "allPublications": "Alle Veröffentlichungen", "newFeature": "Neue Funktion", "bugFix": "Fehlerbehebung", "improvement": "Verbesserung", "newFeatureLabel": "NEUE FUNKTION", "bugFixLabel": "FEHLERBEHEBUNG", "improvementLabel": "VERBESSERUNG", "announcementLabel": "ANKÜNDIGUNG", "today": "<PERSON><PERSON>", "dayAgo": "vor 1 Tag", "daysAgo": "vor {{count}} <PERSON>en", "weekAgo": "vor 1 Woche", "weeksAgo": "vor {{count}} <PERSON><PERSON><PERSON>", "monthAgo": "vor 1 Monat", "monthsAgo": "vor {{count}} <PERSON><PERSON>", "yearAgo": "vor 1 Jahr", "yearsAgo": "vor {{count}} <PERSON><PERSON><PERSON>", "author": "von {{author}}", "systemAuthor": "System", "commitLabel": "Commit:", "deploymentLabel": "Bereitstellung:", "somethingWentWrong": "Etwas ist schiefgelaufen", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "noMatchingUpdates": "<PERSON><PERSON> pass<PERSON>en Updates gefunden", "noUpdatesAvailable": "<PERSON><PERSON> Updates ve<PERSON><PERSON><PERSON><PERSON>", "tryAdjustingFilters": "Versuchen Sie, Ihre Such- oder Filterkriterien anzupassen.", "checkBackLater": "Schauen Sie später nach neuen Updates und Ankündigungen.", "previous": "Zurück", "next": "<PERSON><PERSON>", "pageOf": "Seite {{current}} von {{total}}"}}, "vectordb": {"providerConfiguration": "Anbieter-Konfiguration", "provider": "Vektor-Datenbankanbieter", "selectProvider": "Wählen Sie einen Anbieter", "mongodbVector": "MongoDB Vektor", "chooseProvider": "Wählen Sie Ihren Vektor-Datenbankanbieter.", "connectionString": "Verbindungszeichenfolge", "connectionStringPlaceholder": "************************************:port", "connectionStringDescription": "Ihre MongoDB-Verbindungszeichenfolge einschließlich Anmeldeinformationen.", "databaseName": "Datenbankname", "databaseNamePlaceholder": "ihr_datenbankname", "databaseNameDescription": "Der Name Ihrer MongoDB-Datenbank.", "collectionName": "Collecton", "collectionNamePlaceholder": "ihr_collection_name", "collectionNameDescription": "Der Name Ihrer MongoDB-Sammlung für die Vektorspeicherung.", "saveChanges": "Änderungen speichern", "updateSuccess": "VectorDB-Einstellungen erfolgreich aktualisiert", "updateError": "Fehler beim Aktualisieren der VektorDB-Einstellungen", "providerRequired": "An<PERSON><PERSON> ist erforderlich", "connectionStringRequired": "Verbindungszeichenfolge ist erforderlich", "databaseNameRequired": "Datenbankname ist erforderlich", "collectionNameRequired": "Collection ist erforderlich"}, "workspace": {"page": "Seite", "deleteFile": "<PERSON><PERSON>", "deleteFolder": "Ordner löschen", "deleteWorkspace": "Arbeitsbereich löschen", "deleteWorkspaceConfirm": "Sind <PERSON> sicher, dass Sie diesen Arbeitsbereich löschen möchten?", "deleteWorkspaceWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird den Arbeitsbereich \"{name}\" und alle seine Seiten, <PERSON><PERSON><PERSON>, Dokumente dauerhaft löschen.", "deleteWorkspaceTypeConfirm": "<PERSON>te geben Si<PERSON> \"{name}\" ein, um die Löschung zu bestätigen:", "deleteWorkspacePermanently": "Dauerhaft löschen", "deletePageConfirm": "Sind <PERSON> sicher, dass Sie diese Seite löschen möchten?", "deletePageWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird die Seite \"{name}\" und alle zugehörigen Inhalte dauerhaft löschen.", "deleteFolderConfirm": "Sind <PERSON> sicher, dass Sie diesen Ordner löschen möchten?", "deleteFolderWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird den Ordner \"{name}\" und alle enthaltenen Dateien und Unterordner ({count} Elemente) dauerhaft löschen.", "deleteFileConfirm": "Sind <PERSON> sicher, dass Sie diese Datei löschen möchten?", "deleteFileWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird die Datei \"{name}\" und ihren vektorisierten Inhalt dauerhaft löschen.", "confirmDeletion": "Löschung bestätigen", "typeToConfirm": "Zur Bestätigung eingeben", "deletingWorkspace": "Arbeitsbereich wird gelöscht...", "deletingPage": "Seite wird gelöscht...", "deletingFolder": "Ordner wird gelöscht...", "deletingFile": "Datei wird gelöscht...", "workspaceDeletedSuccess": "Arbeitsbereich erfolgreich gelöscht", "failedToDeleteWorkspace": "Fehler beim Löschen des Arbeitsbereichs", "dangerZone": "Gefahrenbereich", "dangerZoneDescription": "Unumkehrbare und destruktive Aktionen.", "pages": "Seiten", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newFolderName": "<PERSON><PERSON><PERSON> Ordnername", "workspace": "Arbeitsbereich", "contentImportedAsHtml": "Der Inhalt wird als HTML importiert.", "workspaceMembers": "Arbeitsbereich-Mitglieder", "inviteUser": "<PERSON><PERSON><PERSON> e<PERSON>n", "inviteUserSubtitle": "Laden Sie einen Benutzer ein, Ihrem Arbeitsbereich beizutreten", "addMember": "<PERSON><PERSON><PERSON><PERSON> hinz<PERSON>", "allPages": "Alle Seiten", "openPage": "Seite öffnen", "sendingInvitation": "Einladung wird gesendet...", "invitationSentSuccess": "Einladung erfolgreich gesendet", "failedToSendInvitation": "Fehler beim Senden der Einladung", "removingMember": "Mitglied wird entfernt...", "memberRemovedSuccess": "<PERSON>t<PERSON><PERSON> erfolg<PERSON>ich entfernt", "failedToRemoveMember": "Fehler beim Entfernen des Mitglieds. Bitte versuchen Sie es erneut.", "userWorkspaceManagement": "Benutzer-Arbeitsbereich-Verwaltung", "userWorkspaceManagementDescription": "Verwalten Sie Benutzerzugriff und Berechtigungen für Arbeitsbereiche in Ihrer Organisation", "userAssignments": "Benutzerzuweisungen", "userAssignmentsDescription": "Anzeigen und verwalten, wie Benutzer Arbeitsbereichen mit ihren jeweiligen Rollen zugewiesen werden", "addUserToWorkspace": "Benutzer zum Arbeitsbereich hinzufügen", "addUserToWorkspaceDescription": "<PERSON>ählen Sie einen Benutzer und einen Arbeitsbereich aus, um ihn mit einer bestimmten Rolle hinzuzufügen", "removeUserFromWorkspace": "Benutzer aus Arbeitsbereich entfernen", "removeUserFromWorkspaceDescription": "Sind <PERSON> sicher, dass Si<PERSON> diesen Benutzer aus dem Arbeitsbereich entfernen möchten? Der Benutzer verliert den Zugriff auf alle Ressourcen in diesem Arbeitsbereich.", "userAddedToWorkspace": "Benutzer erfolgreich zum Arbeitsbereich hinzugefügt", "errorAddingUser": "Fehler beim Hinzufügen des Benutzers zum Arbeitsbereich", "userRemovedFromWorkspace": "Benutzer erfolgreich aus dem Arbeitsbereich entfernt", "errorRemovingUser": "Fehler beim Entfernen des Benutzers aus dem Arbeitsbereich", "noWorkspaceMembersFound": "<PERSON><PERSON> Arbeitsbereich-Mitglieder gefunden, die Ihren Filtern entsprechen", "selectWorkspace": "Arbeitsbereich auswählen", "create": "Neuen Arbeitsbereich erstellen", "details": "Arbeitsbereich-Details", "detailsDescription": "Füllen Sie die Informationen unten aus, um einen neuen Arbeitsbereich zu erstellen.", "namePlaceholder": "Arbeitsbereich-Namen e<PERSON>ben", "nameRequired": "Arbeitsbereich-Name ist erford<PERSON>lich", "description": "Beschreibung", "descriptionPlaceholder": "Kurze Beschreibung des Inhalts", "initials": "Arbeitsbereich-Initialen (2 Buchstaben)", "initialsPlaceholder": "AB", "initialsRequired": "Zwei Buchstaben-Initialen sind erforderlich", "initialsDescription": "Diese werden als Symbol des Arbeitsbereichs angezeigt.", "creating": "Wird erstellt...", "createWorkspace": "Arbeitsbereich erstellen", "createSuccess": "Arbeitsbereich erfolgreich erstellt!", "createFailed": "Fehler beim Erstellen des Arbeitsbereichs. Bitte versuchen Sie es erneut.", "noWorkspacesYet": "Noch keine Arbeitsbereiche", "createFirstWorkspace": "<PERSON><PERSON><PERSON><PERSON> Sie Ihren ersten Arbeitsbereich, um Ihre Projekte zu organisieren und mit Ihrem Team zusammenzuarbeiten.", "newPage": "Neue Seite", "searchPages": "Seiten durchsuchen...", "created": "<PERSON><PERSON><PERSON><PERSON>", "noPagesFound": "<PERSON>ine Seiten gefunden. Erstellen Sie eine neue Seite, um zu beginnen.", "createNewPage": "Neue Seite erstellen", "renamePage": "Seite umbenennen", "enterNewPageName": "<PERSON><PERSON><PERSON> Si<PERSON> einen neuen Namen für Ihre Seite ein.", "enterPageName": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen für Ihre neue Seite ein.", "pageName": "<PERSON><PERSON><PERSON><PERSON>", "enterPageNamePlaceholder": "Seitenna<PERSON> e<PERSON>ben", "syncFolder": "Ordner synchronisieren", "syncSharePointFolder": "SharePoint-Ordner synchronisieren", "syncWithSharePoint": "Mit SharePoint synchronisieren", "syncWithGoogleDrive": "Mit Google Drive synchronisieren", "removeSync": "Synchronisierung entfernen", "revokeAndDelete": "Widerrufen und Löschen", "revokeAndDeleteConfirm": "Sind <PERSON> sicher, dass Sie die Synchronisation widerrufen und alle synchronisierten Inhalte löschen möchten?", "revokeAndDeleteWarning": "Dies wird alle Ordner und Dateien, die von {provider} importier<PERSON> wurden, dauerhaft löschen. Manuell hochgeladene Inhalte bleiben erhalten.", "revokeAndDeleteSuccess": "Synchronisation widerrufen und synchronisierte Inhalte erfolgreich gelöscht", "failedToRevokeAndDelete": "Fehler beim Widerrufen der Synchronisation und Löschen der Inhalte", "revokingAndDeleting": "Synchronisation wird widerrufen und Inhalte werden gelöscht...", "newFileName": "Neuer Dateiname", "createAndSync": "<PERSON><PERSON>ellen und synchronisieren", "createNewFolder": "Neuen Ordner erstellen", "folderName": "Ordnername", "close": "Schließen", "creatingAndSyncingFolder": "Ordner wird erstellt und synchronisiert...", "folderCreatedAndSyncedSuccess": "Ordner erfolgreich erstellt und synchronisiert", "errorCreatingFolder": "Fehler beim Erstellen des Ordners", "syncingFolder": "Ordner wird synchronisiert...", "folderSyncedSuccess": "Ordner erfolgreich synchronisiert", "errorSyncingFolder": "Fehler beim Synchronisieren des Ordners", "connectAccountInSettings": "Bitte verbinden Sie Ihr Konto in den Integrationseinstellungen", "removingSync": "Synchronisierung wird entfernt...", "syncRemovedSuccess": "Synchronisierung erfolgreich entfernt", "failedToRemoveSync": "Fehler beim Entfernen der Synchronisierung", "checkingSyncStatus": "Synchronisierungsstatus wird überprüft...", "filesAndFoldersInSync": "Alle Dateien und Ordner sind mit {provider} synchronisiert", "synchronizedFiles": "{count} <PERSON><PERSON> mit {provider} synchronisiert", "folderSyncedSuccessfully": "{provider}-Ordner erfolgreich synchronisiert", "failedToSyncFolder": "Fehler beim Synchronisieren des Ordners", "googleDrive": "Google Drive", "sharePoint": "SharePoint", "oneDrive": "SharePoint", "googleDriveAndOneDrive": "Google Drive & SharePoint", "sync": "Synchroni<PERSON><PERSON>", "pageCreatedSuccess": "Seite erfolgreich erstellt!", "failedToCreatePage": "Fehler beim Erstellen der Seite. Bitte versuchen Sie es erneut.", "pageDeletedSuccess": "Seite erfolgreich gelöscht!", "failedToDeletePage": "Fehler beim Löschen der Seite. Bitte versuchen Sie es erneut.", "loading": "Wird geladen...", "addRemoveMember": "Mitglied hinzufügen/entfernen", "syncedWithCloud": "Diese Seite ist mit {provider} synchronisiert und befindet sich im schreibgeschützten Modus. Um Änderungen vorzunehmen, entfernen Sie die Synchronisierung im Seitenbereich.", "new": "<PERSON>eu", "newFolder": "<PERSON><PERSON><PERSON> Ordner", "upload": "Hochladen", "uploadFiles": "<PERSON><PERSON>", "uploadFilesDescription": "<PERSON><PERSON><PERSON><PERSON> Sie Dateien von Ihrem Gerät aus, um sie auf diese Se<PERSON> hochzuladen.", "searchFilesAndFolders": "Dateien und Ordner durchsuchen...", "listView": "Listenansicht", "gridView": "Rasteransicht", "tileView": "Kachelansicht", "allItems": "Alle Elemente", "folderCount": "{count} <PERSON><PERSON><PERSON>", "foldersCount": "{count} <PERSON><PERSON><PERSON>", "fileCount": "{count} Datei", "filesCount": "{count} <PERSON><PERSON>", "name": "Name", "modified": "G<PERSON><PERSON>ndert", "type": "<PERSON><PERSON>", "size": "Größe", "actions": "Aktionen", "folder": "<PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON>", "rename": "Umbenennen", "share": "Teilen", "delete": "Löschen", "readOnly": "(Schreibgeschützt)", "noItemsFound": "<PERSON>ine Elemente gefunden. Erstellen Sie einen Ordner oder laden Si<PERSON> Dateien hoch, um zu beginnen.", "folders": "<PERSON><PERSON><PERSON>", "files": "<PERSON><PERSON>", "openFolder": "Ordner öffnen", "viewFile": "<PERSON><PERSON> anzeigen", "renameFolder": "Ordner umbenennen", "renameFile": "<PERSON><PERSON> umben<PERSON>n", "enterFolderName": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen für Ihren Ordner ein.", "enterFileName": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen für Ihre Datei ein.", "createFolder": "Ordner er<PERSON>llen", "enterFolderNamePlaceholder": "Ordnernamen eingeben", "enterFileNamePlaceholder": "Dateinamen eingeben", "loadingFolder": "Ordner wird geladen...", "folderLoadingError": "<PERSON><PERSON> diese Meldung bestehen bleibt, existiert der Ordner möglicherweise nicht oder es gibt ein Problem beim Laden der Daten.", "returnToWorkspace": "Zurück zum Arbeitsbereich", "searchInFolder": "In diesem Ordner suchen...", "uploadToFolder": "<PERSON><PERSON><PERSON>en Sie Dateien von Ihrem Gerät aus, um sie in diesen Ordner hochzuladen.", "loadingFile": "Wird geladen...", "back": "Zurück", "lastModified": "Zuletzt geändert: {date}", "more": "<PERSON><PERSON>", "downloadFile": "<PERSON><PERSON>", "downloadDocument": "<PERSON><PERSON><PERSON> herunt<PERSON>n", "viewRawMarkdown": "Markdown-<PERSON>ll<PERSON> anzeigen", "downloadTextFile": "<PERSON><PERSON><PERSON>", "downloadSpreadsheet": "<PERSON><PERSON><PERSON>", "downloadPresentation": "Präsentation herunt<PERSON><PERSON>n", "downloadJsonFile": "JSON-<PERSON><PERSON>", "downloadHtmlFile": "HTML-<PERSON><PERSON>", "downloadCsvFile": "CSV-<PERSON><PERSON>", "loadingDocumentContent": "Dokumentinhalt wird geladen...", "loadingMarkdownContent": "Markdown-Inhalt wird geladen...", "loadingTextContent": "Textinhalt wird geladen...", "loadingSpreadsheetContent": "Ta<PERSON><PERSON><PERSON><PERSON>t wird geladen...", "loadingPresentationContent": "Präsentationsinhalt wird geladen...", "loadingJsonContent": "JSON-Inhalt wird geladen...", "loadingHtmlContent": "HTML-Inhalt wird geladen...", "loadingCsvContent": "CSV-Inhalt wird geladen...", "documentNotAvailable": "Dokument nicht verfügbar", "documentCouldNotBeLoaded": "Das Dokument konnte nicht geladen werden", "textFileNotAvailable": "Textdatei nicht verfügbar", "spreadsheetNotAvailable": "Tabelle nicht verfügbar", "presentationNotAvailable": "Präsentation nicht verfügbar", "jsonFileNotAvailable": "JSON-<PERSON><PERSON> nicht verfügbar", "htmlFileNotAvailable": "HTML-<PERSON><PERSON> nicht verfügbar", "csvFileNotAvailable": "CSV-<PERSON><PERSON> nicht verfügbar", "fileCouldNotBeLoaded": "Die Datei konnte nicht geladen werden", "fileTypeCannotBePreviewedTitle": "<PERSON><PERSON> kann nicht angezeigt werden", "fileTypeCannotBePreviewedDesc": "Das Dateiformat {extension} wird für die Vorschau nicht unterstützt.", "openInNewTab": "In neuem Tab <PERSON>", "redirectingToWorkspace": "Weiterleitung zur Arbeitsbereichsseite...", "loadingWorkspaceMessage": "<PERSON>te warten Si<PERSON>, während wir Ihren Arbeitsbereich laden.", "addRemoveMembers": "Mitglieder hinzufügen/entfernen", "viewDetails": "Details anzeigen", "appName": "Swiss Knowledge Hub", "workspaceDetails": "Arbeitsbereich-Details", "manageWorkspaceDetails": "Verwalten Sie Ihre Arbeitsbereich-Details", "updateSuccess": "Arbeitsbereich erfolgreich aktualisiert", "updateFailed": "Fehler beim Aktualisieren des Arbeitsbereichs", "errorFetchingDetails": "Fehler beim Abrufen der Arbeitsbereich-Details", "workspaceNotFound": "Arbeitsbereich nicht gefunden", "noAccess": "<PERSON><PERSON>", "vectorizationStatusTooltip": "Der Vektorisierungsstatus gibt an, ob die Datei erfolgreich für die KI-Suche indiziert wurde.", "importFromUrl": "Von URL importieren", "importFromUrlDescription": "<PERSON>ügen Sie eine URL ein, um Inhalte von einer Webseite zu importieren", "urlImportSuccess": "Inhalt erfolgreich importiert", "urlImportAndVectorizationStarted": "Inhalt importiert und Vektorisierung gestartet", "urlImportSuccessButVectorizationFailed": "Inhalt erfolgreich importiert, aber Vektorisierung fehlgeschlagen", "urlImportError": "Fehler beim Importieren des Inhalts", "urlAlreadyImported": "Diese URL wurde bereits in diesen Arbeitsbereich importiert", "extractImages": "Bilder <PERSON><PERSON><PERSON>n", "contentCleaningLevel": "Inhalt-Bereinigungsstufe", "cleaningLevelBasic": "<PERSON><PERSON><PERSON> (alle Inhalte behalten)", "cleaningLevelMedium": "Mittel (kurze Absätze entfernen)", "cleaningLevelAggressive": "Aggressiv (alle nicht wesentlichen Inhalte entfernen)", "crawlDepth": "Crawl-Tiefe", "crawlDepth1": "1 (<PERSON><PERSON>)", "crawlDepth2": "2 (Hauptseite + verlinkte Seiten)", "crawlDepth3": "3 (<PERSON><PERSON><PERSON>)", "sitemapUrls": "Zusätzliche URLs gefunden", "selectUrlsToImport": "Wählen Sie aus, welche URLs Sie importieren möchten:", "batchImport": "{count} ausgewählte URLs importieren", "batchImportSuccess": "{{count}} Seiten erfolgreich importiert", "batchImportPartialFailure": "{count} URLs konnten nicht importiert werden. Einige wurden möglicherweise durch robots.txt-Beschränkungen blockiert.", "batchImportFailure": "Alle URLs konnten nicht importiert werden", "batchImportError": "Fehler beim Batch-Import", "noUrlsSelected": "Bitte wählen Sie mindestens eine URL zum Importieren aus", "extractedImages": "Extrahierte Bilder", "useBackgroundProcessing": "Hintergrundverarbeitung verwenden", "backgroundProcessingDescription": "URL-Import im Hintergrund verarbeiten, um Wartezeiten zu vermeiden", "importStatus": "Import-Status", "statusPending": "<PERSON><PERSON><PERSON><PERSON>", "statusProcessing": "Wird verarbei<PERSON>t", "statusCompleted": "Abgeschlossen", "statusFailed": "Fehlgeschlagen", "retryImport": "I<PERSON>rt wieder<PERSON>n", "urlImportStarted": "URL-Import im Hintergrund gestartet", "batchImportAndVectorizationSuccess": "{count} URLs importiert und Vektorisierung gestartet", "importFormat": "Import-Format", "contentImportedAsMarkdown": "Inhalt wird als Markdown importiert", "batchImportProgress": "Batch-Import-Fortschritt", "fetchingAndCrawling": "Wird abgerufen und durchsucht..."}, "page": {"members": "Seitenmitglieder", "addMember": "<PERSON><PERSON><PERSON><PERSON> hinz<PERSON>", "addFirstMember": "Fügen Sie das erste Mitglied hinzu, um zu beginnen", "noMembers": "<PERSON>ch keine Seitenmitglieder", "memberAdded": "<PERSON><PERSON><PERSON><PERSON> er<PERSON><PERSON>g<PERSON><PERSON> hinz<PERSON>fügt", "memberRemoved": "<PERSON>t<PERSON><PERSON> erfolg<PERSON>ich entfernt", "memberRoleUpdated": "Mitgliedsrolle erfolgreich aktualisiert", "failedToAddMember": "Fehler beim Hinzufügen des Mitglieds", "failedToRemoveMember": "Fehler beim Entfernen des Mitglieds", "failedToUpdateRole": "Fehler beim Aktualisieren der Mitgliedsrolle", "memberRole": "Mitgliedsrolle", "editorRole": "Bearbeiterrolle", "adminRole": "<PERSON><PERSON><PERSON>", "memberDesc": "<PERSON><PERSON> anzeigen", "editorDesc": "Anzeigen und bearbeiten", "adminDesc": "Vollzugriff", "makeAdmin": "Zum Administrator machen", "makeEditor": "<PERSON><PERSON> Bearbeiter machen", "makeMember": "<PERSON><PERSON> machen", "removeMember": "<PERSON><PERSON><PERSON><PERSON> entfernen", "membershipSource": "<PERSON><PERSON>", "manualSource": "<PERSON><PERSON>", "sharePointSource": "SharePoint", "workspaceSource": "Arbeitsbereich", "sharePointPermission": "SharePoint-Berechtigung", "lastChecked": "Zuletzt überprüft", "neverChecked": "<PERSON><PERSON>", "memberManagement": "Mitgliederverwaltung", "manageMembersDescription": "<PERSON><PERSON><PERSON><PERSON>, wer auf diese Seite zugreifen kann und deren Berechtigungsebenen"}, "settings": {"organization": {"title": "Organisation", "description": "Verwalten Sie das Profil und die Einstellungen Ihrer Organisation."}, "members": {"title": "Teammitglieder", "description": "Verwalten Sie die Teammitglieder Ihrer Organisation."}, "roles": {"title": "<PERSON><PERSON>", "description": "Verwalten Sie benutzerdefinierte Rollen und Berechtigungen"}, "userWorkspaceManagement": {"title": "Benutzer-Arbeitsbereich-Verwaltung", "description": "Verwalten Sie den Benutzerzugriff auf Arbeitsbereiche"}, "groups": {"title": "Gruppen", "description": "Verwalten Sie Gruppen und Arbeitsbereichszugriff"}, "embedded": {"title": "Embedding <PERSON><PERSON><PERSON>", "description": "Konfigurieren Sie Ihre Einstellungen für Embedding Anbieter"}, "llm": {"title": "LLM-Anbieter", "description": "Konfigurieren Sie Ihre LLM-Anbietereinstellungen"}, "llmScope": {"title": "LLM-Be<PERSON>ich", "description": "Steuern Sie den Datenzugriff und die Fähigkeiten des KI-Assistenten", "saveSettings": "Einstellungen speichern"}, "mcp": {"title": "MCP-Server", "description": "Model Context Protocol Server verwalten", "active": "Aktiv", "inactive": "Inaktiv", "toggleStatus": "Server-<PERSON> umschalten"}, "vectordb": {"title": "VectorDB-Anbieter", "description": "Konfigurieren Sie Ihre VectorDB-Anbietereinstellungen"}, "integrations": {"comingSoon": "Demnächst verfügbar", "selectSharePointSite": "SharePoint-Site auswählen", "selectSharePointFolder": "SharePoint-Ordner auswählen", "title": "Speicherintegrationen", "description": "Konfigurieren Sie Ihre Speicherintegrationen"}, "navigationGroups": {"organizationManagement": "Organisationsverwaltung", "userAccessControl": "Benutzer- und Zugriffskontrolle", "aiData": "KI & Daten", "integrations": "Integrationen"}}, "embedded": {"providerConfiguration": "Anbieter-Konfiguration", "provider": "<PERSON><PERSON><PERSON>", "selectProvider": "Wählen Sie einen Anbieter", "apiKey": "API-Schlüssel", "modelName": "<PERSON><PERSON><PERSON>", "deploymentName": "Deployment-Name", "endpoint": "API-Endpunkt", "enterApiKey": "**********", "enterModelName": "embed-v-4-0", "enterDeploymentName": "******", "enterEndpoint": "https://openai.azure.com/openai/deployments/******/chat/completions", "saving": "Wird gespeichert...", "saveChanges": "Änderungen speichern", "updateSuccess": "Embedding Einstellungen erfolgreich aktualisiert", "updateFailed": "Fehler beim Aktualisieren der Embedding Einstellungen"}, "inviteMember": {"inviteMember": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "inviteTeamMember": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "inviteDescription": "Laden Sie ein neues Mitglied ein, Ihrer Organisation beizutreten.", "emailAddress": "E-Mail-Adresse", "emailPlaceholder": "<EMAIL>", "selectRolePlaceholder": "Wählen Sie eine Rolle", "sending": "Wird gesendet...", "sendInvitation": "Einladung senden", "invitationSent": "Einladung an {email} gesendet", "invitationFailed": "Fehler beim Senden der Einladung. Bitte versuchen Sie es erneut.", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein.", "selectRole": "Bitte wählen Sie eine Rolle."}, "memberList": {"teamMembers": "Teammitglieder", "manageTeamMembers": "Verwalten Sie Ihre Teammitglieder", "noTeamMembers": "<PERSON>ch keine Teammitglieder", "removeTeamMember": "<PERSON><PERSON><PERSON><PERSON> ent<PERSON>", "confirmRemove": "<PERSON>d <PERSON><PERSON> sicher, dass <PERSON> {name} aus Ihrem Mandanten entfernen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "removing": "Wird entfernt...", "roleChangeSuccess": "<PERSON> Rolle von {name} wurde zu {role} ge<PERSON><PERSON>t", "roleChangeFailed": "Fehler beim Aktualisieren der Mitgliedsrolle", "memberRemoveSuccess": "{name} wurde aus dem Mandanten entfernt", "memberRemoveFailed": "Fehler beim Entfernen des Mitglieds"}, "chatHistory": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noHistoryYet": "<PERSON><PERSON> kein Cha<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "startNewChatPrompt": "Starten Sie einen neuen Chat, um Ihre Konversation zu beginnen", "startNewChat": "Neuen Chat starten", "renameSuccess": "Chat erfolgreich umbenannt", "renameFailed": "Fehler beim Umbenennen des Chats", "deleteSuccess": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "Fehler beim Löschen des Chats"}, "theme": {"toggleTheme": "Darstellung umschalten", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System"}, "groups": {"title": "Gruppen", "groups": "Gruppen", "groupsDescription": "Organisieren Sie Benutzer in Gruppen und weisen Sie Rollen für eine optimierte Verwaltung des Arbeitsbereichszugriffs zu", "groupsHelp": "Gruppen ermöglichen es Ihnen, die Berechtigungen mehrerer Benutzer gleichzeitig zu verwalten, indem Sie der Gruppe Rollen zuweisen", "selectGroupToManage": "Gruppe zum Verwalten auswählen", "selectGroupDescription": "Wählen Sie eine Gruppe aus der Liste unten aus, um ihre Mitglieder und Berechtigungen anzuzeigen und zu verwalten", "createFirstGroupDescription": "Gruppen helfen Ihnen, <PERSON><PERSON><PERSON> zu organisieren und ihren Arbeitsbereichszugriff effizient zu verwalten. Erstellen Sie Ihre erste Gruppe, um zu beginnen.", "selectGroupPrompt": "Gruppe auswählen", "noDescription": "Keine Beschreibung angegeben", "noGroupsFound": "Keine Gruppen gefunden", "role": "<PERSON><PERSON>", "searchGroups": "Gruppen durchsuchen...", "group": "Gruppe", "selectGroup": "Gruppe auswählen", "noGroup": "<PERSON><PERSON> Grup<PERSON>", "createGroup": "Gruppe er<PERSON>llen", "createNewGroup": "Neue Gruppe erstellen", "createGroupDescription": "<PERSON><PERSON><PERSON>n Si<PERSON> eine neue Gruppe, um den Zugriff auf Arbeitsbereiche zu verwalten", "groupName": "Gruppenname", "enterGroupName": "Gruppenname e<PERSON>ben", "description": "Beschreibung", "enterDescription": "Beschreibung eingeben (optional)", "groupCreated": "Gruppe erfolgreich erstellt", "groupCreateFailed": "Fehler beim Erstellen der Gruppe", "noGroupsYet": "Noch keine Gruppen", "createFirstGroup": "<PERSON>rstellen Sie Ihre erste Gruppe, um den Zugriff auf Arbeitsbereiche zu verwalten", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "workspaces": "Arbeitsbereiche", "groupMembers": "Gruppenmitglieder", "addUser": "Benutzer hinzufügen", "noMembers": "Noch keine Mitglieder in dieser Gruppe", "noMembersYet": "<PERSON>ch keine Mitglieder", "addMembersDescription": "<PERSON>ügen Sie Mitglieder zu dieser Gruppe hinzu, um ihnen Zugriff auf alle zugewiesenen Arbeitsbereiche zu geben", "assignedWorkspaces": "Zugewiesene Arbeitsbereiche", "assignWorkspace": "Arbeitsbereich zuweisen", "noWorkspaces": "Noch keine Arbeitsbereiche dieser Gruppe zugewiesen", "noWorkspacesYet": "Noch keine Arbeitsbereiche", "assignWorkspacesDescription": "Weisen Sie Arbeitsbereiche zu, um allen Mitgliedern Zugriff zu gewähren", "addUserToGroup": "Benutzer zur Gruppe hinzufügen", "addUserToGroupDescription": "Fügen Sie einen Benutzer zu dieser Gruppe hinzu. Sie erhalten Zugriff auf alle Arbeitsbereiche, die dieser Gruppe zugewiesen sind.", "userAddedToGroup": "Benutzer erfolgreich zur Gruppe hinzugefügt", "failedToAddUserToGroup": "Fehler beim Hinzufügen des Benutzers zur Gruppe", "removeUserConfirm": "Benutzer aus Gruppe entfernen", "removeUserDescription": "Sind <PERSON><PERSON> sic<PERSON>, dass Sie diesen Benutzer aus der Gruppe entfernen möchten? Sie verlieren den Zugriff auf alle Arbeitsbereiche, die dieser Gruppe zugewiesen sind, es sei denn, sie haben direkten Zugriff.", "userRemovedFromGroup": "Benutzer erfolgreich aus Gruppe entfernt", "failedToRemoveUser": "Fehler beim Entfernen des Benutzers aus der Gruppe", "assignWorkspaceToGroup": "Arbeitsbereich der Gruppe zuweisen", "assignWorkspaceDescription": "<PERSON><PERSON> Si<PERSON> dieser Gruppe einen Arbeitsbereich zu. Alle Mitglieder der Gruppe erhalten Zugriff auf diesen Arbeitsbereich.", "selectWorkspace": "Arbeitsbereich auswählen", "noWorkspacesFound": "Keine Arbeitsbereiche gefunden", "workspaceAssignedToGroup": "Arbeitsbereich erfolgreich der Gruppe zugewiesen", "failedToAssignWorkspace": "Fehler beim Zuweisen des Arbeitsbereichs zur Gruppe", "removeWorkspaceConfirm": "Arbeitsbereich aus Gruppe entfernen", "removeWorkspaceDescription": "Sind <PERSON><PERSON> sic<PERSON>, dass Sie diesen Arbeitsbereich aus der Gruppe entfernen möchten? Gruppenmitglieder verlieren den Zugriff auf diesen Arbeitsbereich, es sei denn, sie haben direkten Zugriff.", "workspaceRemovedFromGroup": "Arbeitsbereich erfolgreich aus Gruppe entfernt", "failedToRemoveWorkspace": "Fehler beim Entfernen des Arbeitsbereichs aus der Gruppe", "loadingGroups": "Gruppen werden geladen...", "failedToFetchGroups": "Fehler beim Abrufen der Gruppen", "manageAccessDescription": "<PERSON><PERSON><PERSON><PERSON> und verwalten Sie Gruppen, um den Zugriff auf Arbeitsbereiche zu kontrollieren", "workspaceAccess": "Rollenzugriff", "assignRole": "<PERSON><PERSON>", "assignRoleToGroup": "Rolle der Gruppe zuweisen", "assignRoleDescription": "<PERSON>sen Sie dieser Gruppe eine Rolle zu. Alle Mitglieder der Gruppe erben die in dieser Rolle definierten Berechtigungen.", "selectRole": "Rolle auswählen", "noRoleAssigned": "<PERSON><PERSON> Rolle zugewiesen", "roleDescription": "<PERSON>sen Si<PERSON> dieser Gruppe eine Rolle zu, um festzulegen, welche Arbeitsbereichsberechtigungen Gruppenmitglieder haben werden", "roleAssignedToGroup": "Rolle erfolgreich der Gruppe zugewiesen", "failedToAssignRole": "Fehler beim Zuweisen der Rolle zur Gruppe", "removeRoleConfirm": "Rolle aus Gruppe entfernen", "removeRoleDescription": "Sind <PERSON> sic<PERSON>, dass Sie diese Rolle aus der Gruppe entfernen möchten? Gruppenmitglieder verlieren die durch diese Rolle gewährten Berechtigungen.", "roleRemovedFromGroup": "Rolle erfolgreich aus Gruppe entfernt", "failedToRemoveRole": "Fehler beim Entfernen der Rolle aus der Gruppe", "selectUserWithCustomRole": "Benutzer mit benutzerdefinierter Rolle auswählen", "noUsersWithCustomRoles": "<PERSON><PERSON> mit benutzerdefinierten Rollen gefunden", "tryDifferentSearch": "Versuchen Sie einen anderen Suchbegriff oder erstellen Sie eine neue Gruppe", "groupDeletedSuccessfully": "Gruppe erfolgreich <PERSON>", "failedToDeleteGroup": "Fehler beim Löschen der Gruppe", "deleteGroupConfirm": "Gruppe löschen", "deleteGroupDescription": "Sind <PERSON><PERSON> sicher, dass Si<PERSON> '{groupName}' löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden und entfernt alle Gruppenmitglieder und Rollenzuweisungen.", "loadingGroup": "Gruppe wird geladen...", "failedToFetchGroup": "Fehler beim Abrufen der Gruppendetails", "groupNotFound": "Gruppe nicht gefunden", "groupNotFoundDescription": "Die gesuchte Gruppe existiert nicht oder Sie haben keine Berechtigung, sie anzuzeigen.", "backToGroups": "Zurück zu Gruppen"}, "memberSidebar": {"newChat": "<PERSON><PERSON><PERSON>", "chatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "Gruppe", "groups": "Gruppen", "createGroup": "Gruppe er<PERSON>llen", "editGroup": "Gruppe bearbeiten", "deleteGroup": "Gruppe löschen", "confirmDelete": "Sind <PERSON> sicher, dass Sie diese Gruppe löschen möchten?", "groupDeleted": "Gruppe erfolgreich <PERSON>", "groupDeleteFailed": "Fehler beim Löschen der Gruppe", "createNewChatGroup": "Neuen Chat-Gruppe erstellen", "createGroupDescription": "<PERSON><PERSON><PERSON>n Si<PERSON> eine neue Chat-Gruppe, um Ihre Chats zu organisieren.", "groupName": "Gruppenname", "enterGroupName": "Gruppennamen eingeben", "groupCreated": "Gruppe erfolgreich erstellt", "groupCreateFailed": "Fehler beim Erstellen der Gruppe"}, "sidebar": {"dashboard": "Dashboard", "executiveOverview": "Executive Übersicht", "myHub": "<PERSON><PERSON>", "askAi": "KI fragen", "chatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "support": "Support", "upgrade": "Upgrade", "managePages": "Seiten verwalten", "addWorkspace": "+Hinzufügen", "help": "Dokumentation", "toggleSidebar": "Seitenleiste umschalten", "privacyPolicy": "Datenschutzrichtlinie", "sharedThreads": "<PERSON><PERSON><PERSON> Threads", "mcpServers": "MCP Server"}, "chat": {"like": "Diese Antwort gefällt mir", "dislike": "Diese Antwort gefällt mir nicht", "regenerate": "Antwort neu generieren", "sources": "<PERSON><PERSON>", "viewSource": "<PERSON><PERSON> anzeigen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openInNewTab": "<PERSON><PERSON><PERSON>", "filesAttached": "<PERSON><PERSON> an<PERSON>", "moreFiles": "weitere Dateien", "files": "<PERSON><PERSON>", "filesCount": "<PERSON><PERSON>", "shown": "ange<PERSON><PERSON>t", "loadingPdf": "PDF wird geladen...", "fileSizeBytes": "0 Bytes", "fileSizeUnits": {"bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB"}, "fileTypes": {"audioFile": "Audio-Datei", "audioDescription": "Diese Audio-Datei kann für Transkription und Analyse verarbeitet werden.", "wordDocument": "Word-Dokument", "wordDescription": "Textinhalt wird aus diesem Dokument extrahiert und analysiert.", "spreadsheet": "Tabellenkalkulation", "spreadsheetDescription": "Daten werden einschließlich Struktur, Spalten und Inhalt analysiert.", "presentation": "Präsentation", "presentationDescription": "Folieninhalt und -struktur werden analysiert.", "csvData": "CSV-Daten", "csvDescription": "Tabellendaten werden für Erkenntnisse geparst und analysiert.", "textFile": "Textdatei", "textDescription": "Vollständiger Textinhalt wird extrahiert und analysiert."}, "copyMessage": "Nachricht kopieren", "copiedToClipboard": "In die Zwischenablage kopiert", "feedbackLike": "Vielen Dank für Ihr positives Feedback!", "feedbackDislike": "Vielen Dank für Ihr Feedback. Wir werden daran arbeiten, uns zu verbessern.", "feedbackError": "Fehler beim Speichern des Feedbacks. Bitte versuchen Sie es erneut.", "regenerating": "Antwort wird neu generiert...", "regenerateError": "Fehler beim Neugenerieren der Antwort. Bitte versuchen Sie es erneut.", "fileProcessingError": "Fehler beim Verarbeiten der Datei.", "uploadingFiles": "<PERSON><PERSON> ho<PERSON>laden...", "uploadSuccess": "<PERSON><PERSON> erfolgreich hochgeladen", "originalResponse": "Ursprüngliche Antwort", "regeneratedResponse": "Neu generierte Antwort", "regeneratedFrom": "<PERSON>eu generiert vom Original", "compareResponses": "Vergleichen", "regenerationHistory": "Antwortvergleich", "previousResponse": "Vorherige Antwort", "nextResponse": "Nächste Antwort", "viewRegeneratedResponses": "Regenerierte Antworten anzeigen", "viewOriginalResponse": "Ursprüngliche Antwort anzeigen", "showLatestResponse": "Neueste Antwort anzeigen", "showOriginalResponse": "Ursprüngliche Antwort anzeigen", "openDocument": "<PERSON><PERSON><PERSON>", "page": "Seite", "documentPreview": "Dokumentvorschau", "edit": "Nachricht bearbeiten", "editMessage": "<PERSON><PERSON> Na<PERSON> bearbeiten", "editLastFiveOnly": "<PERSON><PERSON> können nur die letzten 5 Nachrichten bearbeiten", "editing": "<PERSON><PERSON><PERSON>t wird bearbeitet...", "editSuccess": "Nachricht erfolgreich bearbeitet", "editFailed": "Fehler beim Bearbeiten der Nachricht", "editError": "Fehler beim Bearbeiten der Nachricht. Bitte versuchen Sie es erneut.", "saveEdit": "Bearbeitung speichern", "cancelEdit": "Bearbeitung abbrechen", "originalMessage": "Ursprüngliche Nachricht", "editedMessage": "Bearbeitete Nachricht", "editedFrom": "Bearbeitet vom Original", "editHistory": "Bearbeitungsverlauf", "previousEdit": "Vorherige Bearbeitung", "nextEdit": "Nächste Bearbeitung", "viewEditedVersions": "Bearbeitete Versionen anzeigen", "viewOriginalMessage": "Ursprüngliche Nachricht anzeigen", "showLatestEdit": "Neueste Bearbeitung anzeigen", "showOriginalMessage": "Ursprüngliche Nachricht anzeigen", "generatingResponse": "Neue Antwort wird generiert...", "editTriggeredRegeneration": "Bearbeitung löste neue KI-Antwort aus", "conversationThread": "Gesprächsverlauf", "originalConversation": "Ursprüngliches Gespräch", "editedConversation": "Bearbeitetes Gespräch", "citationsAccordion": "Zitate", "relevance": "<PERSON><PERSON><PERSON><PERSON>", "askAi": "KI fragen", "enterMessage": "Nachricht eingeben", "errorProcessingRequest": "Entschuldigung, bei der Verarbeitung Ihrer Anfrage ist ein Fehler aufgetreten.", "renaming": "Chat wird umbenannt...", "renameSuccess": "Chat erfolgreich umbenannt", "renameFailed": "Fehler beim Umbenennen des Chats", "moving": "Chat wird verschoben...", "moveSuccess": "Chat erfolgreich verschoben", "moveFailed": "Fehler beim Verschieben des Chats", "removing": "Chat wird entfernt...", "removeSuccess": "<PERSON>t er<PERSON><PERSON>g<PERSON><PERSON> entfernt", "removeFailed": "Fehler beim Entfernen des Chats", "moveChatDescription": "W<PERSON>hlen Sie eine Gruppe, in die Si<PERSON> diesen Chat verschieben möchten.", "selectGroup": "Wählen Sie eine Gruppe.", "moveToGroup": "In Gruppe verschieben", "dragToMove": "<PERSON><PERSON><PERSON>, um in eine andere Gruppe zu verschieben", "dropHere": "<PERSON><PERSON> <PERSON><PERSON>, um zur Gruppe hinzuzufügen", "dropToUngroup": "<PERSON><PERSON> <PERSON><PERSON>, um aus der Gruppe zu entfernen", "downloadDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Titel", "messages": "Nachrichten", "lastActivity": "Letzte Aktivität", "export": "Exportieren", "deleteChat": "<PERSON><PERSON> l<PERSON>", "deleteChatConfirmation": "Sind <PERSON> sicher, dass Sie diesen Chat löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden. Alle Nachrichten und Daten werden dauerhaft entfernt.", "deleting": "Wird gel<PERSON>t...", "untitledChat": "Unbenannter Chat", "ungroupSuccess": "Chat erfolgreich aus der Gruppe entfernt", "includeWebResults": "Websuche", "webSearchTooltip": "Ergebnisse aus dem öffentlichen Web einbeziehen, um Antworten zu verbessern", "enterMessageWithImages": "Fragen Sie zu Ihren Bildern oder fügen Sie eine Nachricht hinzu...", "webSearchLimitExceeded": "Tägliches Limit für Websuchen überschritten. Bitte versuchen Sie es morgen erneut.", "webSearchError": "Fehler bei der Websuche", "webSearchErrorDescription": "Bei der Websuche ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "tryAgainTomorrow": "Bitte versuchen Sie es morgen erneut.", "documentSources": "Dokumente", "webSources": "Web", "thinking": "Denke nach...", "uploadImage": "Bild hochladen", "invalidImageFormat": "Ungültiges Bildformat. Bitte verwenden Sie JPG, PNG oder WebP.", "imageTooLarge": "Die Bildgröße sollte weniger als 10MB betragen.", "tooManyImages": "Maximal 5 Bilder erlaubt.", "imageProcessingError": "Fehler beim Verarbeiten des Bildes.", "dropImagesHere": "Bilder hier <PERSON>gen", "removeImage": "Bild entfernen", "imageUploadHelp": "Bilder per Drag & Drop oder Klick hochladen (JPG, PNG, WebP, max. 10MB)", "attachedImages": "Angehängte Bilder", "imagesAttached": "Bilder an<PERSON><PERSON><PERSON><PERSON><PERSON>", "imagesCount": "Bilder", "moreImages": "weitere Bilder", "addFilesAndImages": "Dateien und Bilder hinzufügen", "dropFilesHere": "<PERSON><PERSON> hier <PERSON>gen", "slotsRemaining": "{count} Plätze verfügbar", "removeFile": "<PERSON><PERSON> ent<PERSON>nen", "filesUploaded": "{current} von {max} <PERSON><PERSON>", "limitReached": "<PERSON>it erreicht", "searchModeTooltips": {"internalSearch": "Interne Dokumente und Wissensdatenbank durchsuchen", "webSearch": "Das Web nach aktuellen Informationen durchsuchen", "deepResearch": "Umfassende Analyse auf Forschungsniveau", "internalSearchDisabled": "Interne Suche ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "webSearchDisabled": "Websuche ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "deepResearchDisabled": "Tiefenforschung ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "mcpOnlyRestriction": "Nur MCP-Too<PERSON> sind durch die LLM-Bereichseinstellungen Ihrer Organisation erlaubt"}}, "quickAsk": {"buttonLabel": "Schnellfrage", "placeholder": "<PERSON><PERSON><PERSON> eine schnelle Frage...", "thinking": "Denke nach...", "searching": "Suche nach der besten Antwort", "mcpSearch": "MCP", "internalSearch": "Intern", "webSearch": "Web", "deepResearch": "Tiefenforschung", "emptyStateDescription": "Erhalten Sie sofortige Antworten, ohne die Unterhaltung in Ihrem Arbeitsbereich zu speichern", "emptyState": "<PERSON><PERSON><PERSON> eine Frage, um eine schnelle Antwort zu erhalten", "openAsChat": "Als <PERSON><PERSON>", "clearConversation": "Unterhaltung löschen", "tooltips": {"addAttachments": "Anhänge hinzufügen", "webSearch": "Durchsuchen Sie das Web nach aktuellen Informationen. Klicken Sie sowohl auf Intern als auch auf Web für hybride Suche.", "webSearchHybrid": "Websuche (kombiniert mit interner Suche)", "webSearchDisabled": "Websuche ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "internalSearch": "Durchsuchen Sie interne Dokumente und Wissensdatenbank. Klicken Sie sowohl auf Intern als auch auf Web für hybride Suche.", "internalSearchHybrid": "<PERSON>ne <PERSON> (kombiniert mit Websuche)", "internalSearchDisabled": "Interne Suche ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "mcpSearch": "MCP-Server durchsuchen", "mcpSearchDisabled": "MCP-Suche ist durch die LLM-Bereichseinstellungen Ihrer Organisation deaktiviert", "loadingSettings": "LLM-Bereichseinstellungen werden geladen..."}}, "integration": {"comingSoon": "Demnächst verfügbar", "oneDriveDescription": "Verbinden Sie Ihre SharePoint-Sites sofort, um Ihre Dateien zu synchronisieren.", "sharePointDescription": "Verbinden Sie Ihre SharePoint-Sites sofort, um Ihre Dateien zu synchronisieren.", "googleDriveDescription": "Verbinden Sie Ihren Google Drive-Arbeitsbereich sofort, um Ihre Dateien zu synchronisieren.", "connected": "Verbunden", "notConnected": "Nicht verbunden", "connect": "Verbinden", "disconnect": "<PERSON><PERSON><PERSON>", "configure": "Konfigurieren", "manage": "<PERSON><PERSON><PERSON><PERSON>", "selectSite": "SharePoint-Site auswählen", "selectFolder": "SharePoint-Ordner auswählen", "selectSharePointSite": "SharePoint-Site auswählen", "selectSharePointFolder": "SharePoint-Ordner auswählen", "noSitesFound": "Keine Sites gefunden", "noFoldersFound": "<PERSON><PERSON>dner gefunden", "syncing": "Synchronisierung...", "syncWithSharePoint": "Mit SharePoint synchronisieren", "syncComplete": "Synchronisierung abgeschlossen! {count} <PERSON><PERSON> synchronisiert.", "selectedSite": "Ausgewählte Site", "selectedFolder": "Ausgewählter Ordner", "syncDisclaimer": "Alle Dateien und Unterordner innerhalb des ausgewählten Ordners werden synchronisiert und im schreibgeschützten Modus verfügbar sein.", "root": "Stammverzeichnis"}, "integrations": {"googleDriveSync": "Google Drive-Synchronisierung", "oneDriveSync": "OneDrive-Synchronisierung", "syncDescription": "Synchronisieren Sie Ihre Cloud-Ordner und -Dateien mit Ihrem Arbeitsbereich. Dies erstellt eine lokale Kopie Ihrer Dateien und behält die Ordnerstruktur bei.", "syncNow": "Jetzt synchronisieren", "filesSynced": "{count} <PERSON><PERSON> synchronisiert", "selectFolderFirst": "Bitte wählen Si<PERSON> zu<PERSON>t einen Ordner zum Synchronisieren aus"}, "bulkImport": {"members": {"title": "Mitglieder massenweise importieren", "description": "Importieren Sie mehrere Teammitglieder gleichzeitig mit einer CSV-Datei", "triggerButton": "Massenimport", "steps": {"upload": "Hochladen", "preview": "Vorschau", "import": "Importieren"}, "buttons": {"cancel": "Abbrechen", "back": "Zurück", "import": "Importieren {count} <PERSON><PERSON><PERSON><PERSON><PERSON>", "importing": "Importiere...", "importMore": "Mehr importieren", "done": "<PERSON><PERSON><PERSON>"}, "messages": {"successfullyImported": "Erfolgreich {count} Mitglied{plural} importiert", "importFailed": "Import fehlgeschlagen. Bitte versuchen Sie es erneut.", "csvParseError": "{count} <PERSON><PERSON> in CSV-Datei gefunden", "csvReadError": "Fehler beim Lesen der CSV-Datei. Bitte versuchen Sie es erneut."}, "csvUpload": {"title": "CSV-<PERSON><PERSON>", "description": "Ziehen Sie Ihre CSV-<PERSON><PERSON> hierher oder klicken Si<PERSON> zum Durchsuchen", "chooseFile": "CSV-<PERSON><PERSON>", "supportedFormat": "Unterstütztes Format: Nur CSV-Dateien", "template": {"title": "Benötigen Sie e<PERSON> Vorlage?", "description": "Laden Sie unsere CSV-Vorlage mit dem korrekten Format und Beispieldaten herunter", "downloadButton": "<PERSON><PERSON><PERSON>"}, "formatRequirements": {"title": "CSV-Format-Anforderungen:", "requiredColumns": "Erforderliche Spalten:", "optionalColumns": "Optionale Spalten:", "validRoles": "Gültige Rollen:", "note": "<PERSON><PERSON><PERSON><PERSON>:", "customRoleNote": "CUSTOM-Rolle verwendet die Standard-Einstellungen für benutzerdefinierte Rollen der Organisation"}, "customRoles": {"title": "Verfügbare benutzerdefinierte Rollen:", "usage": "Verwenden Sie \"CUSTOM\" in der Rollenspalte, um Benutzer benutzerdefinierten Rollen zuzuweisen.", "loading": "Benutzerdefinierte Rollen werden geladen..."}}}, "dialog": {"title": "Gruppenmitglieder massenweise importieren", "description": "Fügen Sie mehrere Benutzer gleichzeitig zu Gruppen hinzu, indem Sie eine CSV-Datei verwenden", "triggerButton": "Massenimport", "steps": {"upload": "Hochladen", "preview": "Vorschau", "import": "Importieren"}, "buttons": {"cancel": "Abbrechen", "back": "Zurück", "import": "Importieren {count} Zuweisungen", "importing": "Importiere...", "importMore": "Mehr importieren", "done": "<PERSON><PERSON><PERSON>"}, "messages": {"successfullyAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {count} <PERSON><PERSON><PERSON> zu Gruppen hinzugefügt", "importFailed": "Import fehlgeschlagen. Bitte versuchen Sie es erneut."}}, "upload": {"title": "CSV-<PERSON><PERSON>", "description": "Ziehen Sie Ihre CSV-<PERSON><PERSON> hierher oder klicken Si<PERSON> zum Durchsuchen", "chooseFile": "CSV-<PERSON><PERSON>", "supportedFormat": "Unterstütztes Format: Nur CSV-Dateien", "template": {"title": "Benötigen Sie e<PERSON> Vorlage?", "description": "Laden Sie unsere CSV-Vorlage mit dem korrekten Format und Beispieldaten herunter", "downloadButton": "<PERSON><PERSON><PERSON>"}, "formatRequirements": {"title": "CSV-Format-Anforderungen:", "requiredColumns": "Erforderliche Spalten:", "groupIdentifier": "Gruppenkennung:", "groupIdentifierValue": "groupName ODER groupId (mindestens eine erforderlich)", "note": "<PERSON><PERSON><PERSON><PERSON>:", "noteValue": "Benutzer müssen bereits Mitglieder der Organisation sein, um zu Gruppen hinzugefügt zu werden"}, "availableGroups": {"title": "Verfügbare Gruppen:", "usage": "Verwenden Sie entweder den Gruppennamen oder die ID in Ihrer CSV-Datei."}, "loadingGroups": "Gruppen werden geladen..."}, "preview": {"stats": {"validAssignments": "Gültige Zuweisungen", "warnings": "Warnungen", "errors": "<PERSON><PERSON>"}, "alerts": {"errorsFound": "<PERSON><PERSON> wurden {count} <PERSON>hler gefunden, die behoben werden müssen:", "warningsFound": "<PERSON><PERSON> wurden {count} Warnungen gefunden:", "moreErrors": "... und {count} weitere <PERSON>", "moreWarnings": "... und {count} weitere Warnungen", "allValid": "Alle {count} Gruppenzuweisungen sind gültig und bereit zum Import!", "row": "<PERSON><PERSON><PERSON>"}, "table": {"title": "Gruppenzuweisungs-Vorschau ({count} Zuweisungen)", "headers": {"email": "E-Mail", "groupName": "Gruppenname", "groupId": "Gruppen-ID"}, "usingGroupId": "Verwende Gruppen-ID", "showingFirst": "Zeige die ersten 10 von {count} Zuweisungen"}, "noData": {"title": "<PERSON>ine gültigen Daten gefunden", "description": "Bitte überprüfen Sie Ihr CSV-Dateiformat und versuchen Sie es erneut."}}}, "sharepoint": {"integrationRequired": "SharePoint-Integration erforderlich", "integrationRequiredMessage": "Sie müssen Ihr SharePoint-Konto verbinden, um auf diese Seite zuzugreifen. Bitte gehen Sie zu den Integrationseinstellungen, um Ihr Konto zu verbinden.", "connectAccount": "SharePoint-Konto verbinden", "checkAccess": "Zugriff prüfen", "verified": "<PERSON>ugriff bestät<PERSON>", "cachedAccess": "Zwischengespeicherten SharePoint-Zugriff verwenden", "refresh": "Aktualisieren", "accessDeniedMessage": "Sie haben keine Berechtigung, auf diese SharePoint-synchronisierte Seite zuzugreifen.", "needAccess": "Benötigen Sie Zugriff auf diesen Inhalt?", "contactOwner": "Wenden Sie sich an den SharePoint-Site-Besitzer oder Ihren Administrator, um Zugriff zu beantragen.", "lastChecked": "Zuletzt überprüft", "connectNeeded": "<PERSON>erbin<PERSON><PERSON>", "cached": "Zwischengespeichert", "checking": "Überprüfung...", "accessDenied": "Zugriff verweigert. Wenden Sie sich an den Seitenadministrator, um als Mitglied hinzugefügt zu werden.", "accessContentDenied": "Sie haben keine Berechtigung, auf diesen SharePoint-Inhalt zuzugreifen", "contentNotFound": "SharePoint-Inhalt nicht gefunden oder wurde verschoben", "unableToVerifyAccess": "Kann den SharePoint-Zugriff nicht überprüfen", "errorCheckingAccess": "Fehler beim Überprüfen des SharePoint-Zugriffs"}, "password": {"currentPassword": "Aktuelles Passwort", "newPassword": "Neues Passwort", "confirmNewPassword": "Neues Passwort bestätigen", "placeholder": "••••••••", "requirements": "Das Passwort muss mindestens 8 <PERSON>eichen lang sein.", "updateButton": "Passwort aktualisieren", "updating": "Passwort wird aktualisiert...", "updateSuccess": "Passwort erfolgreich aktualisiert", "updateFailed": "Fehler beim Aktualisieren des Passworts", "currentPasswordMinLength": "Das aktuelle Passwort muss mindestens 6 Zeichen lang sein.", "newPasswordMinLength": "Das neue Passwort muss mindestens 8 Zeichen lang sein.", "confirmPasswordMinLength": "Die Passwortbestätigung muss mindestens 8 Zeichen lang sein.", "resetPassword": "Passwort zurücksetzen", "createNewPassword": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Passwort für Ihr Konto", "createPasswordPlaceholder": "Neues Passwort erstellen", "confirmPasswordPlaceholder": "Neues Passwort bestätigen", "resetting": "Passwort wird zurückgesetzt...", "resetSuccess": "Passwort erfolgreich zurückgesetzt!", "passwordReset": "Passwort zurückgesetzt", "resetSuccessMessage": "Ihr Passwort wurde erfolgreich zurückgesetzt.", "redirectMessage": "Sie werden in wenigen Sekunden zur Anmeldeseite weitergeleitet.", "verifyingLink": "Ihr Zurücksetzungslink wird überprüft...", "invalidLink": "Ungültiger Link", "linkExpired": "Dieser Link zum Zurücksetzen des Passworts ist ungültig oder abgelaufen.", "requestNewLink": "Neuen Link anfordern", "tokenVerificationFailed": "Der Zurücksetzungstoken konnte nicht überprüft werden. Bitte versuchen Sie es erneut."}, "dashboard": {"welcomeMessage": "Willkommen bei Swiss Knowledge Hub", "getStartedMessage": "Um zu beginnen, erstellen Sie Ihre erste Organisation, um mit Ihrem Team zusammenzuarbeiten.", "dashboardTitle": "Dashboard", "organizationCount": "<PERSON><PERSON> sind Mi<PERSON><PERSON><PERSON> von {count} Organisation", "organizationCountPlural": "<PERSON><PERSON> sind <PERSON><PERSON><PERSON> von {count} Organisationen"}, "profile": {"profileInformation": "Profilinformationen", "updateProfileDetails": "Aktualisieren Sie Ihre Profilinformationen.", "firstNamePlaceholder": "Max", "lastNamePlaceholder": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "deleteAccount": "Konto löschen", "deleteAccountWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird Ihr Konto dauerhaft löschen und Ihre Daten von unseren Servern entfernen.", "confirmDeleteAccount": "<PERSON><PERSON>, mein <PERSON> löschen", "deletingAccount": "Konto wird gelöscht...", "errorLoading": "Fehler beim Laden des Profils. Bitte versuchen Sie es später erneut.", "profileUpdated": "<PERSON>il er<PERSON><PERSON>g<PERSON>ich aktualisiert", "updateFailed": "Fehler beim Aktualisieren des Profils", "accountDeleted": "Konto erfolgreich <PERSON>", "deleteFailed": "Fehler beim Löschen des Kontos", "dangerZone": "Gefahrenbereich", "areYouSure": "Sind Sie absolut sicher?"}, "verification": {"verifyEmail": "Bestätigen Sie Ihre E-Mail", "sentEmailTo": "Wir haben eine Bestätigungs-E-Mail an {email} gesendet", "checkEmail": "Bitte überprüfen Sie Ihre E-Mail, um Ihr Konto zu bestätigen", "instructions": "<PERSON><PERSON> auf Ihr Konto zugreifen können, müssen Sie Ihre E-Mail-Adresse bestätigen. Bitte überprüfen Sie Ihren Posteingang und klicken Sie auf den Bestätigungslink in der E-Mail, die wir Ihnen gesendet haben.", "cantFindEmail": "E-Mail nicht gefunden?", "checkSpam": "Überprüfen Sie Ihren Spam- oder Junk-Ordner", "checkEmailCorrect": "<PERSON><PERSON><PERSON>, dass Sie die richtige E-Mail-Adresse eingegeben haben", "allowTime": "Warten Si<PERSON> einige Minuten, bis die E-Mail ankommt", "resendEmail": "Bestätigungs-E-Mail erneut senden", "backToSignIn": "Zurück zur Anmeldung", "emailRequired": "E-Mail-Adresse ist erforderlich", "sendingEmail": "Bestätigungs-E-Mail wird gesendet...", "emailSentSuccess": "Bestätigungs-E-Mail erfolgreich gesendet!", "resendFailed": "Fehler beim erneuten Senden der Bestätigungs-E-Mail. Bitte versuchen Sie es erneut.", "registrationSuccessful": "Registrierung erfolgreich! Bitte überprüfen Sie Ihre E-Mail, um Ihr Konto zu bestätigen.", "sentVerificationEmailTo": "Wir haben eine Bestätigungs-E-Mail an <strong>{email}</strong> gesendet. Bitte überprüfen Sie Ihren Posteingang und klicken Sie auf den Bestätigungslink, um Ihr Konto zu aktivieren.", "clickToResend": "<PERSON><PERSON><PERSON> <PERSON> hier, um die Bestätigungs-E-Mail erneut zu senden", "goToSignIn": "<PERSON><PERSON> Anmeldung gehen"}, "billing": {"Starter": "Starter", "Business": "Business", "Enterprise": "Enterprise", "Custom": "Custom", "total": "Gesamt", "discountCode": "Rabattcode", "enterDiscountCode": "Rabattcode eingeben", "apply": "<PERSON><PERSON><PERSON>", "discountCodeApplied": "Rabattcode erfolgreich angewendet", "descriptionStarter": "Ideal für kleine Teams oder Einzelpersonen, die erst beginnen.", "descriptionBusiness": "Ideal für wachsende Teams mit moderaten Nutzungsanforderungen.", "descriptionEnterprise": "Für professionelle Teams mit höheren Nutzungsanforderungen.", "descriptionCustom": "Benutzerdefinierte Lösung für große Organisationen mit spezifischen Anforderungen.", "title": "Abrechnung & Abonnement", "subtitle": "Verwalten Sie Ihr Abonnement und Ihre Abrechnungsinformationen", "confirmUpdateUsers": "Benutzeraktualisierung bestätigen", "confirmUpdateStorage": "Speicheraktualisierung bestätigen", "confirmAddUsers": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON><PERSON> {count} zusätzliche Benutzer hinzufügen möchten? Dies erhöht Ihre {interval} Kosten um CHF {price}.", "confirmRemoveUsers": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {count} Benutzer entfernen möchten? Dies verringert Ihre {interval} Kosten um CHF {price}.", "confirmAddStorage": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {count} GB zusätzlichen Speicher hinzufügen möchten? Dies erhöht Ihre {interval} Kosten um CHF {price}.", "confirmRemoveStorage": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {count} GB Speicher entfernen möchten? Dies verringert Ihre {interval} Kosten um CHF {price}.", "confirmUpdate": "Aktualisierung bestätigen", "currentSubscription": "Aktuelles Abonnement", "currentSubscriptionDesc": "Ihr aktueller Plan und Nutzungsinformationen", "trialBadge": "Kostenlose Testversion", "trialEndsOn": "Testversion endet am {date}", "plan": "Plan", "manageSubscriptionAddons": "Abonnement-Erweiterungen verwalten", "adjustSubscriptionAddons": "Passen Sie Benutzer und Speicher für Ihr Abonnement an", "users": "<PERSON><PERSON><PERSON>", "storage": "<PERSON><PERSON><PERSON><PERSON>", "totalSubscriptionCost": "Gesamtkosten des Abonnements", "increasingSubscription": "Dies erhöht Ihre monatlichen Kosten um CHF {price}", "decreasingSubscription": "Ein Downgrade des Abonnements ist nicht möglich.", "additionalStorage": "Zusätzlicher Speicher", "additionalStorageGB": "{count} GB zusätzlicher Speicher", "noAdditionalStorage": "<PERSON><PERSON> zusätzlicher Speicher", "included": "Inbegriffen", "free": "<PERSON><PERSON><PERSON>", "totalStorage": "Gesamtspeicher", "basePlanStorage": "{storage} GB im Plan enthalten", "currentUsage": "Aktuelle Nutzung", "exceededBaseStorage": "Basisplanspeicher überschritten", "addingStorage": "Das Hinzufügen von {count} GB Speicher erhöht Ihre monatlichen Kosten um CHF {price}", "reducingStorage": "Die Reduzierung von {count} GB Speicher senkt Ihre monatlichen Kosten um CHF {price}", "manageStorage": "Vektorspeicher verwalten", "adjustStorage": "Passen Sie Ihren Vektordatenbankspeicher an", "updateStorageButton": "Speicher aktualisieren", "additionalStorageFee": "Zusätzlicher Speicher zu gestaffelten Preisen verfügbar", "includedStorage": "{count} GB Vektorspeicher enthalten", "includedPlusAdditionalStorage": "{included} GB enthalten + {additional} GB zusätzlicher Speicher", "subscriptionUpdateSuccess": "Abonnement erfolgreich aktualisiert", "subscriptionUpdateFailed": "Aktualisierung des Abonnements fehlgeschlagen", "includedUsers": "{count} enthal<PERSON><PERSON>", "includedPlusAdditionalUsers": "{included} enthal<PERSON><PERSON> + {additional} zusätzliche Benutzer", "vectorStoreUsage": "Vector Store Nutzung", "usageOf": "{used} GB von {total} GB verwendet", "availablePlans": "Verfügbare Pläne", "choosePlan": "<PERSON><PERSON><PERSON><PERSON> Sie den Plan, der am besten zu Ihren Bedürfnissen passt", "manageSubscription": "Abonnement verwalten", "currentPlan": "Aktueller Plan", "contactUs": "Kontaktieren Sie uns", "subscribe": "Abonnieren", "upgradePlan": "Plan upgraden", "processing": "Verarbeitung...", "priceCHF": "CHF {price}", "perMonth": "pro <PERSON><PERSON>", "perYear": "pro Jahr", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "savePercent": "Spare {percent}", "saveCHF": "Spare CHF {amount}", "save2Months": "Spare 2 Monate", "additionalUserFee": "+ CHF {fee}.- pro zusätzliche<PERSON> Benutzer", "includes": "<PERSON><PERSON><PERSON><PERSON>", "customNumberOf": "Benutzerdefini<PERSON><PERSON> von", "usersLabel": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gbVectorStore": "GB Dokumentenspeicher", "allFeatures": "Alle Funktionen", "hostingInSwitzerland": "Hosting in der Schweiz", "standardSupport": "Standard-Support", "additionalUsers": "Zusätzliche Benutzer", "additionalUsersDescription": "Ihr {planName} Plan enthält {includedUsers} Benutzer. Möchten Sie weitere hinzufügen?", "summary": "Zusammenfassung", "basePlan": "Basisplan ({users} Benutzer)", "additionalUsersCount": "<PERSON>us<PERSON><PERSON><PERSON> Benutzer ({count})", "pricePerUserCHF": "CHF {price} pro <PERSON><PERSON>er", "totalUsers": "Gesamt ({count} <PERSON><PERSON><PERSON>)", "pricePerMonth": "CHF {price}/Monat", "pricePerYear": "CHF {price}/Jahr", "confirmAndCheckout": "Bestätigen & zur Kasse", "noChangesToUpdate": "Keine Änderungen zum Aktualisieren.", "portalOpenFailed": "Fehler beim Öffnen des Kundenportals. Bitte versuchen Sie es erneut.", "loginRequiredForUpgrade": "<PERSON>e müssen angemeldet sein, um Ihren Plan zu aktualisieren", "checkoutSessionFailed": "Fehler beim Erstellen der Checkout-Sitzung. Bitte versuchen Sie es erneut.", "subscriptionUpdated": "Ihr Abonnement wurde erfolgreich aktualisiert.", "subscriptionCanceled": "Abonnement-Aktualisierung wurde abgebrochen.", "noTenantSelected": "<PERSON>in Mandant ausgewählt. Bitte wählen Sie einen Mandanten, um Abrechnungsinformationen anzuzeigen.", "adjustSubscription": "Passen Sie Benutzer und Speicher für Ihr Abonnement an", "updateSubscription": "Abonnement aktualisieren", "addingUsers": "Das Hinzufügen von {count} Benutzern erhöht Ihre monatlichen Kosten um CHF {price}", "removingUsers": "Das Entfernen von {count} Benutzern verringert Ihre monatlichen Kosten um CHF {price}", "customerNotFound": "Ihr Stripe-Kundenkonto wurde nicht gefunden. Bitte versuchen Sie es erneut oder kontaktieren Sie den Support, wenn das Problem weiterhin besteht.", "updateUsers": "Benutzer aktualisieren", "updateStorageAction": "Speicher aktualisieren", "addStorageAction": "Speicher hinzufügen", "existingAdditionalStorage": "Bestehender zusätzlicher Speicher ({count} GB)", "newTotalStorage": "<PERSON><PERSON><PERSON> Gesamtzusatzspeicher ({count} GB)", "newStorageBeingAdded": "<PERSON><PERSON><PERSON> Speicher wird hinzugefügt ({count} GB)", "totalAdditionalStorage": "Gesamter zusätzlicher Speicher ({count} GB)", "currentAdditionalStorage": "Aktueller zusätzlicher Speicher", "selectStorageIncrement": "Speicherinkrement zum Hinzufügen auswählen", "addSelectedStorage": "{size} GB Speicher hinzufügen", "addingStorageToExisting": "Hinzufügen zu vorhandenem {existing} GB", "resetStorage": "Z<PERSON>ücksetzen", "invalidStorageTier": "Ungültige Speicherstufe ausgewählt.", "noStorageTiersSelected": "<PERSON><PERSON>icherstufen ausgewählt.", "currentStorageTiers": "Aktuelle Speicherstufen", "duplicateStorageTier": "Fehler: <PERSON><PERSON><PERSON> Speicherstufe erkannt. Bitte versuchen Sie eine andere Konfiguration.", "usersUpdateSuccess": "Benutzer erfolgreich aktualisiert", "usersUpdateFailed": "Fehler beim Aktualisieren der Benutzer", "storageUpdateSuccess": "Speicher erfolgreich aktualisiert", "storageUpdateFailed": "Fehler beim Aktualisieren des Speichers", "processingRequest": "Wird verarbeitet...", "errorFetchingPrices": "Fehler beim Abrufen der Preise von Stripe. Verwende Ersatzpreise.", "downgradeNotAllowed": "<PERSON><PERSON> ist ein Downgrade nicht selbst möglich. Bitte kontaktiere den Support."}, "invitations": {"noPending": "<PERSON>ine ausstehenden Einladungen", "inviteMembers": "<PERSON>n Si<PERSON> Mi<PERSON>gliede<PERSON> e<PERSON>, werden sie hier angezeigt.", "sent": "Gesendet", "expires": "Läuft ab", "copyLink": "Einladungslink kopieren", "resend": "Einladung erneut senden", "cancel": "Einladung abbrechen", "cancelInvitation": "Einladung abbrechen", "cancelConfirmation": "<PERSON>d <PERSON><PERSON> sicher, dass Sie die an {email} gesendete Einladung abbrechen möchten? Sie werden mit dieser Einladung nicht mehr der Organisation beitreten können.", "keep": "Behalten", "cancelSuccess": "Einladung erfolgreich abgebrochen", "cancelError": "Fehler beim Abbrechen der Einladung", "resendSuccess": "Einladung erfolgreich erneut gesendet", "resendError": "Fehler beim erneuten Senden der Einladung", "linkCopied": "Einladungslink in die Zwischenablage kopiert"}, "invitationAcceptance": {"joinOrganization": "{tenantName} beitreten", "invitedByAs": "{inviterName} hat <PERSON><PERSON>, als {role} beizutreten.", "welcomeBack": "<PERSON><PERSON><PERSON><PERSON> zurück, {name}! Bitte melden Sie sich an, um diese Einladung anzunehmen.", "verifyingInvitation": "Einladung wird überprüft...", "invitationError": "Einladungsfehler", "couldNotVerify": "Wir konnten Ihre Einladung nicht überprüfen.", "returnToHome": "Zur Startseite zurückkehren", "yourName": "Ihr Name", "enterFullName": "Geben Sie Ihren vollständigen Namen ein", "createPassword": "Passwort erstellen", "confirmPassword": "Passwort bestätigen", "accountFound": "Konto gefunden", "accountExistsMessage": "Ein Konto mit dieser E-Mail-Adresse existiert bereits. Si<PERSON> können diese Einladung direkt annehmen oder", "accountExistsMessageEnd": "wenn <PERSON> möchten.", "signInFirst": "sich zu<PERSON>t anmelden", "acceptInvitation": "Einladung annehmen", "createAccountAndAccept": "Konto erstellen & Einladung annehmen", "processing": "Wird verarbei<PERSON>t", "invitationAcceptedSuccess": "Einladung erfolgreich angenommen!", "failedToAccept": "Fehler beim Annehmen der Einladung", "notCurrentUser": "<PERSON><PERSON> {name}?", "signOut": "Abmelden", "wantToSignInFirst": "<PERSON><PERSON>chten Sie sich zu<PERSON>t anmelden?", "signIn": "Anmelden", "alreadyHaveAccount": "Haben <PERSON> bereits ein Konto?", "nameMinLength": "Name muss mindestens 2 <PERSON><PERSON>chen lang sein", "passwordMinLength": "Passwort muss mindestens 8 Zeichen lang sein", "passwordsDoNotMatch": "Passwörter stimmen nicht überein"}, "llmSettings": {"providerConfiguration": "Anbieter-Konfiguration", "provider": "<PERSON><PERSON><PERSON>", "selectProvider": "Wählen Sie einen Anbieter", "chooseProvider": "Wählen Sie Ihren LLM-Anbieter", "apiKey": "API-Schlüssel", "enterApiKey": "**********", "apiKeyDescription": "Ihr {provider} API-Schlüssel", "apiKeyRequired": "API-Schlüssel ist erforderlich", "modelName": "<PERSON><PERSON><PERSON>", "enterModelName": "DeepSeek-V3", "modelDescription": "Der Name des zu verwendenden Modells (z.B. gpt-4)", "modelRequired": "Modellname ist erforderlich", "azureEndpoint": "Azure-Endpunkt", "enterAzureEndpoint": "Geben Sie die Azure-Endpunkt-URL ein", "azureEndpointDescription": "Ihre Azure OpenAI-Service-Endpunkt-URL", "azureDeploymentName": "Azure-Bereitstellungsname", "enterDeploymentName": "Geben Sie den Bereitstellungsnamen ein", "deploymentDescription": "Der Name Ihrer Azure OpenAI-Bereitstellung", "saveSettings": "Einstellungen speichern"}, "llmScope": {"configuration": "LLM-Bereich-Konfiguration", "description": "Steuern Sie, auf welche Datenquellen und Fähigkeiten der KI-Assistent für Ihre Organisation zugreifen kann. Diese Einstellung betrifft alle Benutzer und Arbeitsbereiche in Ihrer Organisation.", "refresh": "Aktualisieren", "ownerOnlyMessage": "Nur Organisationseigentümer können LLM-Bereichseinstellungen ändern. Aktueller Bereich:", "loadingSettings": "LLM-Bereichseinstellungen werden geladen...", "selectScope": "LLM-Be<PERSON>ich auswählen", "saveSettings": "Einstellungen speichern", "labels": {"internalOnly": "<PERSON><PERSON> intern", "externalOnly": "Nur extern", "mcpOnly": "Nur MCP-Tools", "hybrid": "Hybrid (Intern + Web)", "fullAccess": "Vollzugriff LLM"}, "descriptions": {"internalOnly": "Zugriff nur auf interne Dokumente und Wissensdatenbank. Keine externe Websuche.", "externalOnly": "Zugriff nur auf externe Websuche. Keine internen Dokumente.", "mcpOnly": "Zugriff nur auf MCP (Model Context Protocol) Tools und externe Dienste. Keine internen Dokumente oder Websuche.", "hybrid": "Zugriff auf interne Dokumente, externe Websuche und MCP-Server. <PERSON>ine erweiterten KI-Funktionen.", "fullAccess": "Vollständige LLM-Fähigkeiten einschließlich Deep Research, erweiterte KI-Funktionen und alle Datenquellen."}, "updateSuccess": "LLM-Bereichseinstellungen erfolgreich aktualisiert", "updateFailed": "Fehler beim Aktualisieren der LLM-Bereichseinstellungen", "updateError": "Ein Fehler ist beim Aktualisieren der Einstellungen aufgetreten", "ownerOnlyError": "Nur Organisationseigentümer können LLM-Bereichseinstellungen ändern", "capabilities": {"title": "Fähigkeiten mit ausgewähltem Bereich:", "internalDocuments": "Interne Dokumente", "webSearch": "Websuche", "mcpServers": "MCP-Server", "hybridSearch": "Hybrid-Suche", "fullLLMAccess": "Vollzugriff LLM"}, "availableFeatures": "Verfügbare Funktionen", "restrictions": {"mcpOnlyMessage": "Nur MCP-Too<PERSON> sind durch die LLM-Bereichseinstellungen Ihrer Organisation erlaubt"}, "scopeOptions": {"internalOnly": "<PERSON><PERSON> intern", "fullLLMScope": "Vollzugriff LLM-Bereich", "internalOnlyDescription": "Zugriff nur auf interne Dokumente und Wissensdatenbank. Keine externe Websuche.", "fullAccessDescription": "Vollständige LLM-Funktionen mit konfigurierbarem Zugriff auf externe Ressourcen."}, "features": {"webSearch": "Websuche", "mcpTools": "MCP-Tools", "deepSearchResearch": "Tiefensuche & Recherche", "selectFeaturesDescription": "Wählen Sie die Funktionen aus, die Sie für Ihre Organisation aktivieren möchten."}}, "subscription": {"noActiveSubscription": "Kein aktives Abonnement", "noSubscriptionMessage": "Sie haben kein aktives Abonnement. Sie können keine Arbeitsbereiche erstellen oder Mitglieder einladen.", "subscribePlan": "Abonnieren Sie einen Plan", "workspaceCreationRestricted": "Sie benötigen ein aktives Abonnement, um Arbeitsbereiche zu erstellen", "memberInvitationRestricted": "<PERSON>e benötigen ein aktives Abonnement, um Mitglieder einzuladen", "vectorStorageWarning": "Warnung: Dieser Upload wird Ihr Vektordatenbank-Speicherlimit um etwa {willExceedBy} GB überschreiten.", "vectorStorageUsage": "Aktuelle Nutzung: {currentUsageGB} GB von {limitGB} GB.", "vectorStorageContinue": "<PERSON><PERSON><PERSON><PERSON> Sie mit dem Upload fortfahren?", "userLimitReached": "Benutzerlimit erreicht", "userLimitMessage": "Sie haben das Benutzerlimit Ihres Plans erreicht ({currentCount}/{limit}). Bitte aktualisieren Sie Ihren Plan oder entfernen Sie bestehende Mitglieder, bevor <PERSON> neue einladen."}, "api": {"errors": {"failedToUploadFile": "Fehler beim Hochladen der Datei", "failedToUpdateFile": "Fehler beim Aktualisieren der Datei", "failedToDeleteFile": "Fehler beim Löschen der Datei", "fileNotFound": "Datei nicht gefunden", "unauthorized": "Nicht autorisiert - Benutzer-<PERSON>", "fileIdRequired": "Datei-ID ist erford<PERSON>lich", "fileIdAndNameRequired": "Datei-ID und Name sind erforderlich"}}, "privacyPolicy": {"title": "Datenschutzrichtlinie", "scope": "Geltungsbereich", "scopeContent": "Diese Datenschutzrichtlinie gilt für die SaaS-Plattform Swiss Knowledge Hub und alle damit verbundenen Web- und API-Dienste.", "preamble": "Präambel", "preambleContent": "Swiss Knowledge Hub GmbH (\"wir\", \"uns\") verarbeitet personenbezogene Daten gemäß dem Schweizerischen Bundesgesetz über den Datenschutz (DSG) und seiner Ausführungsverordnung.", "controller": "Verantwortlicher", "controllerContent": "Swiss Knowledge Hub GmbH\nKönizstrasse 161\n3097 Liebefeld, Schweiz\nUID CHE-219.860.\n<EMAIL> | +41 31 318 33 55\nVertreten durch Ruth Spring, Vorstandsmitglied", "purposes": "Zwecke der Verarbeitung", "purposesContent1": "1. Bereitstellung und Betrieb der Plattform", "purposesContent2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> von Benutzerkonten und Zugriffsrechten", "purposesContent3": "3. Kommunikation und Support", "purposesContent4": "4. Zahlungsabwicklung und Abonnementverwaltung", "purposesContent5": "5. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>, Protokollanalyse", "purposesContent6": "6. Einhaltung gesetzlicher Verpflichtungen und die Begründung, Ausübung oder Verteidigung von Rechtsansprüchen", "categories": "<PERSON><PERSON><PERSON>", "categoriesContent": "- Stammdaten wie Name, Geschäfts-E-Mail, Unternehmenszugehörigkeit\n- Authentifizierungsdaten wie gehashte Passwörter, JWT-Tokens, Sitzungs-IDs\n- Inhaltsdaten wie hochgeladene oder synchronisierte Dokumente und Nachrichten\n- Zahlungsdaten wie Transaktions- und Abonnement-Kennungen\n- Nutzungs- und Protokolldaten wie IP-Adresse, Zeitstempel, API-Aufrufe", "legalBases": "Rechtsgrundlagen", "legalBasesContent": "- Vertragserfüllung oder Schritte vor Vertragsabschluss\n- Berechtigte Interessen an sicherem und effizientem Betrieb\n- Einwilligung, die jederzeit mit Wirkung für die Zukunft widerrufen werden kann", "retention": "Aufbewahrung", "retentionContent": "Daten werden gelöscht oder anonymisiert, sobald sie für die angegebenen Zwecke nicht mehr erforderlich sind. Konto- und Inhaltsdaten werden spätestens zwölf Monate nach Vertragsbeendigung entfernt, es sei denn, gesetzliche Aufbewahrungspflichten erfordern eine längere Speicherung.", "recipients": "Empfänger und Auftragsverarbeiter", "recipientsContent1": "- **Microsoft Azure Switzerland North** – vollständiges Hosting von Anwen<PERSON>ng, Datenbank, Speicher und E-Mail.", "recipientsContent2": "- **Stripe Payments Europe Ltd. (Irland) / Stripe Inc. (USA)** – Zahlungsabwicklung.", "recipientsContent3": "- **<PERSON><PERSON>** zusätzlichen externen Anbieter außerhalb der Schweiz, außer den in Abschnitt 7b genannten. Alle Auftragsverarbeiter sind vertraglich zur Vertraulichkeit verpflichtet.", "ai": "Einsatz künstlicher Intelligenz", "aiContent1": "Wir setzen maschinelle Lernalgorithmen (LangChain-basierte Retrieval-Augmented Generation) für semantische Suche und Dokumentenanalyse ein.", "aiContent2": "- Die Verarbeitung ist auf Azure-Rechenzentren in der Schweiz beschränkt.", "aiContent3": "- Es werden keine automatisierten Entscheidungen getroffen, die rechtliche oder ähnlich erhebliche Auswirkungen haben.", "aiContent4": "- Vektoreinbettungen werden pseudonymisiert und in verschlüsselter Form gespeichert.", "thirdParty": "Drittanbieter-Integrationen (SharePoint, Google Drive)", "thirdPartyContent": "Wenn ein Kunde den optionalen Microsoft SharePoint- oder Google Drive-Connector aktiviert, werden ausgewählte Dateien direkt über den jeweiligen Anbieter synchronisiert. Diese Anbieter verarbeiten Daten gemäß ihren eigenen Datenschutzbestimmungen und können dies in Ländern außerhalb der Schweiz tun. Die Konfiguration der Integrationen liegt in der Verantwortung des Kunden.", "transfers": "Grenzüberschreitende Übermittlungen", "transfersContent": "Die Plattform wird in der Schweiz betrieben. Zahlungsbezogene Daten werden an Stripe übermittelt, das diese in den Vereinigten Staaten verarbeiten kann. Übermittlungen stützen sich auf die EU-Standardvertragsklauseln (SCC 2021) gemäß Art. 16 DSG.", "security": "Datensicherheit", "securityContent": "- Ende-zu-Ende-TLS-Verschlüsselung für alle Übertragungen\n- Verschlüsselung sensibler Datenbankfelder\n- Passwort-Speicherung mit bcrypt\n- Rollenbasierte Zugriffskontrolle\n- Protokollierung sicherheitsrelevanter Ereignisse und regelmäßige Audits", "rights": "Rechte der betroffenen Personen", "rightsContent": "Betroffene Personen haben das Recht auf Auskunft, <PERSON><PERSON><PERSON>gung, Löschung, Einschränkung der Verarbeitung, Datenübertragbarkeit, Widerruf der Einwilligung und Beschwerde beim Eidgenössischen Datenschutz- und Öffentlichkeitsbeauftragten. Anfragen müssen an die in Abschnitt 2 genannte Adresse gesendet werden; ein Identitätsnachweis kann erforderlich sein.", "cookies": "Cookies und ähnliche Technologien", "cookiesContent1": "Wir verwenden nur essentielle Cookies.", "cookiesContent2": "- Sitzungs-Cookie: HTTP-Only, Secure, SameSite Lax, Ablauf spätestens 24 h\n- Sprachpräferenz-Cookie: läuft mit der Sitzung ab\n- Abonnementstatus-Cookie: läuft nach 30 Tagen ab", "cookiesContent5": "Es werden keine Marketing- oder Tracking-Cookies eingesetzt.", "minors": "<PERSON><PERSON><PERSON>", "minorsContent": "Die Plattform ist ausschließlich für Benutzer ab 16 Jahren bestimmt.", "changes": "Änderungen dieser Richtlinie", "changesContent": "Wir können diese Richtlinie jederzeit ändern. Die auf der Plattform veröffentlichte Version ist maßgebend. Benutzer werden über wesentliche Änderungen innerhalb der Anwendung oder per E-Mail benachrichtigt.", "disclaimer": "Haftungsausschluss", "disclaimerContent": "Informationen werden regelmäßig überprüft, können sich jedoch im Laufe der Zeit ändern. Die Haftung ist im gesetzlich zulässigen Umfang ausgeschlossen.", "contact": "Kontakt", "contactContent": "<EMAIL> | +41 31 318 33 55\nSwiss Knowledge Hub GmbH, Könizstrasse 161, 3097 <PERSON><PERSON>feld, Schweiz", "version": "Version 1.1, April 2025", "overview": "Überblick", "detailedPolicy": "Detaillierte Richtlinie", "dataCollection": "Datenerfassung", "dataCollectionDesc": "Wir erfassen nur wesentliche Daten, die zur Bereitstellung unserer Dienste erforderlich sind, einschließlich Kontoinformationen, Inhaltsdaten und Nutzungsprotokolle.", "dataSecurity": "Datensicherheit", "dataSecurityDesc": "Wir setzen Ende-zu-Ende-Verschlüsselung, sichere Passwort-Speicherung und rollenbasierte Zugriffskontrolle ein, um Ihre Daten zu schützen.", "dataProcessing": "Datenverarbeitung", "dataProcessingDesc": "Alle Daten werden in der Schweiz verarbeitet, mit begrenzten Drittverarbeitern, die zur Vertraulichkeit verpflichtet sind.", "cookiesTitle": "Cookies", "cookiesDesc": "Wir verwenden nur essentielle Cookies für Sitzungsverwaltung, Spracheinstellungen und Abonnementstatus.", "keyPoints": "Wichtige Punkte", "keyPoint1": "Alle Daten werden in Microsoft Azure Switzerland North Rechenzentren gehostet.", "keyPoint2": "Die Zahlungsabwicklung erfolgt über Stripe, das Daten in den USA verarbeiten kann.", "keyPoint3": "Sie haben das Recht auf Zugang, <PERSON><PERSON><PERSON><PERSON>g, Löschung und Einschränkung der Verarbeitung Ihrer Daten.", "keyPoint4": "Die Plattform ist für Benutzer ab 16 Jahren bestimmt."}, "sharedThreads": {"title": "<PERSON><PERSON><PERSON> Threads", "privateShared": "Privat geteilt", "publicShared": "<PERSON><PERSON><PERSON><PERSON> geteilt", "all": "Alle", "private": "Privat", "public": "<PERSON><PERSON><PERSON><PERSON>", "unread": "<PERSON><PERSON><PERSON><PERSON>", "search": "Threads durchsuchen...", "filter": "Filtern", "refresh": "Aktualisieren", "noThreadsFound": "<PERSON><PERSON> get<PERSON>ten Threads gefunden", "noThreadsMessage": "<PERSON><PERSON>hreads get<PERSON>t werden, er<PERSON><PERSON> sie hier", "loadingThreads": "Threads werden geladen...", "comments": "Kommentare", "mentions": "Erwähnungen", "commentCount": "{count} Kommentar", "commentsCount": "{count} Komme<PERSON><PERSON>", "mentionCount": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mentionsCount": "{count} Er<PERSON>ä<PERSON><PERSON>", "newActivity": "Neue Aktivität", "sharedBy": "<PERSON><PERSON><PERSON> {name}", "expiresAt": "Läuft ab am {date}", "expired": "Abgelaufen", "allThreads": "Alle Threads", "unreadOnly": "<PERSON><PERSON>", "noThreadsMatchSearch": "<PERSON><PERSON>hreads entsprechen Ihren Suchkriterien", "noUnreadThreads": "<PERSON><PERSON> keine ungelesenen Threads", "noPrivateThreads": "<PERSON><PERSON> privaten get<PERSON>ten Threads verfügbar", "noPublicThreads": "<PERSON>ine öffentlichen geteilten Threads verfügbar", "noSharedThreads": "<PERSON><PERSON> get<PERSON>ten Threads verfügbar", "messages": "Nachrichten", "clearSearch": "<PERSON><PERSON> löschen"}, "accessibility": {"dragStart": "<PERSON><PERSON><PERSON>: {chatTitle}", "dragOver": "Zie<PERSON> {chatTitle} über {groupName}", "dragEnd": "{chatTitle} nach {groupName} verschoben", "dragCancel": "Ziehvorgang abgebrochen", "dropZone": "Ablagebereich für {groupName}", "draggableChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> Chat-Element: {chatTitle}. Drücken Sie die Leertaste, um das Ziehen zu starten."}, "globalSearch": {"title": "Globale Suche", "titleShort": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> du<PERSON>...", "noResults": "<PERSON><PERSON> \"{searchTerm}\" gefunden", "startTyping": "Beginnen Sie mit der Eingabe zum Suchen..."}, "mcpServers": {"title": "MCP Server", "description": "Konfigurieren Sie Model Context Protocol Server für bedarfsgesteuerte externe Integrationen", "refresh": "Aktualisieren", "addServer": "Server hinz<PERSON><PERSON><PERSON>", "searchPlaceholder": "Server nach Name, Beschreibung oder Befehl durchsuchen...", "allStatus": "Alle Status", "active": "Aktiv", "inactive": "Inaktiv", "error": "<PERSON><PERSON>", "testing": "<PERSON><PERSON>", "showingResults": "Zeige {showing} von {total} Servern", "searchLabel": "<PERSON><PERSON>", "statusLabel": "Status", "errorMessage": "<PERSON><PERSON>", "noServersConfigured": "Keine MCP Server konfiguriert", "noServersDescription": "Konfigurieren Sie Ihren ersten MCP Server, um bedarfsgesteuerte externe Integrationen zu ermöglichen. Server starten automatisch bei Bedarf!", "public": "<PERSON><PERSON><PERSON><PERSON>", "private": "Privat", "viewTools": "<PERSON><PERSON> anzeigen", "totalServers": "Server insgesamt", "ready": "Bereit", "configured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": "<PERSON><PERSON>", "timeout": "Timeout", "autoRestart": "Automatischer Neustart", "arguments": "Argumente", "envVariables": "Umgebungsvariablen", "deleteServerTitle": "MCP Server löschen", "deleteServerDescription": "Sind <PERSON><PERSON> sicher, dass <PERSON> \"{name}\" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden. Der Server wird gestoppt und alle zugehörigen Chat-Sitzungen werden entfernt.", "testConnection": "Verbindung testen", "statusReady": "Bereit", "statusConfigured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusError": "<PERSON><PERSON>", "statusTesting": "<PERSON><PERSON>", "statusUnknown": "Unbekannt", "serverDescriptions": {"githubMcp": "GitHub MCP Server für Repository-Verwaltung und Code-Analyse"}, "statusReadyDescription": "Server konfiguriert und bereit für bedarfsgesteuerte Nutzung", "statusConfiguredDescription": "Server konfiguriert, aber derzeit nicht aktiv", "statusErrorDescription": "Server ist auf einen Fehler gestoßen", "statusTestingDescription": "Server-Konnektivität wird getestet", "statusUnknownDescription": "Server-Status ist unbekannt", "editServer": "MCP Server bearbeiten", "addNewServer": "Neuen MCP Server hinzufügen", "serverName": "Server-Name", "serverNameRequired": "Server-Name ist er<PERSON><PERSON>", "nameMinLength": "Name muss mindestens 2 <PERSON><PERSON>chen lang sein", "serverNamePlaceholder": "z.B. GitHub MCP Server für Repository-Verwaltung und Code-Analyse", "serverDescription": "Beschreibung", "descriptionPlaceholder": "Optionale Beschreibung der Funktionen dieses MCP Servers", "serverType": "Server-Typ", "selectServerType": "Server-<PERSON><PERSON>w<PERSON>", "stdioType": "STDIO (Kommandozeile)", "httpType": "HTTP (Web API)", "form": {"addServer": "Server hinz<PERSON><PERSON><PERSON>", "editServer": "Server bearbeiten", "name": "Name", "namePlaceholder": "Servername e<PERSON>ben", "nameRequired": "Name ist erforderlich", "nameMinLength": "Name muss mindestens 2 <PERSON><PERSON>chen lang sein", "description": "Beschreibung", "descriptionPlaceholder": "Serverbeschreibung eingeben (optional)", "serverType": "Server-Typ", "selectServerType": "Server-<PERSON><PERSON>w<PERSON>", "stdioType": "STDIO (Kommandozeile)", "httpType": "HTTP (Web API)", "stdioDescription": "Kommandozeilenbasierter MCP Server", "httpDescription": "Web API basierter MCP Server mit SSE-Unterstützung", "commandRequired": "Befehl ist für STDIO-Server erford<PERSON>lich", "npxNotSupported": "NPX-basierte Server werden in dieser Umgebung nicht unterstützt. Bitte verwenden Sie direkte Python-Skripte oder andere ausführbare Befehle.", "npxWarning": "NPX-Befehle werden nicht unterstützt. Verwenden Sie Python-Skripte oder direkte ausführbare Dateien.", "argumentPlaceholder": "Argument {number}", "urlRequired": "URL ist für HTTP-Server erforderlich", "invalidUrl": "<PERSON>te geben Si<PERSON> eine gültige HTTP- oder HTTPS-URL ein", "httpHeaders": "HTTP-Header", "addHeader": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "httpHeadersDescription": "Authentifizierungs-Header wie Authorization: Bearer token hinzufügen", "headerNamePlaceholder": "Header-Name (z.B. Authorization)", "headerValuePlaceholder": "Header-Wert (z.B<PERSON> token)", "serverConfiguration": "Server-Konfiguration", "timeoutRequired": "Timeout ist erford<PERSON>lich", "timeoutMinError": "Timeout muss mindestens 1000ms betragen", "timeoutMaxError": "Timeout darf 300000ms nicht überschreiten", "timeoutDescription": "Wie lange auf Server-Antworten gewartet werden soll (empfohlen: 30000ms)", "command": "<PERSON><PERSON><PERSON>", "commandPlaceholder": "z.B. python server.py", "url": "URL", "urlPlaceholder": "z.B. http://localhost:8000", "timeout": "Timeout (Sekunden)", "timeoutPlaceholder": "30", "autoRestart": "Automatischer Neustart", "autoRestartDescription": "Server automatisch neu starten, wenn er abstürzt", "isPublic": "<PERSON><PERSON><PERSON><PERSON>", "isPublicDescription": "Server für alle Benutzer verfügbar machen", "arguments": "Argumente", "envVariables": "Umgebungsvariablen", "addArgument": "Argument hinzufügen", "addEnvVariable": "Umgebungsvariable hinzufügen", "keyPlaceholder": "Varia<PERSON><PERSON><PERSON>", "valuePlaceholder": "Variablenwert", "save": "Speichern", "cancel": "Abbrechen", "saving": "Wird gespeichert...", "creating": "Wird erstellt...", "updating": "Wird aktualisiert...", "httpOnlySupported": "Derzeit werden nur HTTP Web API Server unterstützt", "httpOnlyMessage": "Diese Version unterstützt nur HTTP Web API Server."}, "availableTools": "Verfügbar<PERSON>", "serverConnected": "Server verbunden", "toolsAvailable": "{count} <PERSON><PERSON> verfügbar", "loadingTools": "Tools werden geladen...", "tool": "Tool", "noInputSchema": "<PERSON><PERSON>gabeschema verfügbar", "noParametersRequired": "<PERSON><PERSON>", "statusIndicator": {"connecting": "Verbindung zu MCP Server wird hergestellt...", "healthCheck": "Server-Statusprüfung...", "reconnecting": "Verbindung zu MCP Server wird wiederhergestellt...", "reconnectionFailed": "Wiederverbindung fehlgeschlagen", "toolCall": "Tool wird aufgerufen: {{toolName}}", "toolCallGeneric": "Tool wird ausgeführt...", "processing": "Antwort wird verarbeitet...", "completed": "MCP-Vorgang abgeschlossen", "error": "MCP-Vorgang fehlgeschlagen", "unknown": "MCP-Status unbekannt", "processingLabel": "Wird verarbeitet...", "showErrorGuide": "Fehlerhilfe anzeigen", "hideErrorGuide": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>"}, "errorGuide": {"connectionLost": {"title": "Verbindung verloren", "description": "Die MCP-Server-Verbindung wurde unerwartet geschlossen.", "solutions": ["Der Server-Prozess könnte abgestürzt oder beendet worden sein", "Netzwerkverbindungsprobleme könnten aufgetreten sein", "Server-Timeout könnte erreicht worden sein"], "actions": ["Verbindung zum MCP-Server wied<PERSON>herstellen", "Server-Logs auf Fehler überprüfen", "Server-Konfiguration verifizieren", "Server bei <PERSON><PERSON><PERSON> neu starten"]}, "connectionFailed": {"title": "Verbindung fehlgeschlagen", "description": "Verbindung zum MCP-Server konnte nicht hergestellt werden.", "solutions": ["Server läuft möglicherweise nicht", "Falsche Server-Konfiguration", "Netzwerkverbindungsprobleme"], "actions": ["Überprüfen, ob Server läuft", "Server-Befehl und Argumente überprüfen", "Netzwerkverbindung testen", "Server-Konfiguration überprüfen"]}, "requestTimeout": {"title": "Anfrage-Timeout", "description": "Die Tool-Ausführung hat zu lange gedauert.", "solutions": ["Server ist überlastet oder reagiert nicht", "Tool-Ausführung dauert länger als erwartet", "Netzwerk-Latenz-Probleme"], "actions": ["Anfrage erneut versuchen", "Server-Performance überprüfen", "Timeout-Einstellungen anpassen", "Netzwerkverbindung überprüfen"]}, "toolExecutionFailed": {"title": "Tool-Ausführung fehlgeschlagen", "description": "Das angeforderte Tool konnte nicht ausgeführt werden.", "solutions": ["Tool-Parameter könnten ungültig sein", "Tool ist möglicherweise nicht verfügbar", "Server-<PERSON><PERSON>"], "actions": ["Tool-Parameter überprüfen", "Verfügbare Tools überprüfen", "Server-Logs überprüfen", "<PERSON>t anderen Para<PERSON>n versuchen"]}, "serverUnavailable": {"title": "Server nicht verfügbar", "description": "Der MCP-Server ist derzeit nicht verfügbar.", "solutions": ["Server ist offline oder nicht gestartet", "Server-Konfiguration ist fehlerhaft", "Netzwerkprobleme"], "actions": ["Server-Status überprüfen", "Server neu starten", "Konfiguration überprüfen", "Administrator <PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "unknownError": {"title": "Unbekannter Fehler", "description": "Ein unerwarteter Fehler ist aufgetreten.", "solutions": ["Temporärer Server-Fe<PERSON>", "Unbekanntes Problem mit der Anfrage", "System-<PERSON><PERSON>"], "actions": ["<PERSON><PERSON><PERSON> versuchen", "Server-Logs überprüfen", "Support kontaktieren", "Später erneut versuchen"]}, "retry": "<PERSON><PERSON><PERSON> versuchen", "contactSupport": "Support kontaktieren", "checkLogs": "Logs überprüfen"}}, "image": {"picture": "Bild", "supportedFormats": "Wir unterstützen PNG/JPEG unter 10MB", "uploadImage": "Bild hochladen"}, "notifications": {"title": "Benachrichtigungen", "connectedTitle": "Echtzeit-Benachrichtigungen verbunden", "disconnectedTitle": "Echtzeit-Benachrichtigungen getrennt", "newCount": "{count} neu", "live": "Live", "offline": "Offline", "markAllRead": "Alle als gelesen markieren", "loading": "Benachrichtigungen werden geladen...", "noNotifications": "Noch keine Benachrichtigungen", "noNotificationsDescription": "<PERSON>er sehen Sie Erwähnungen und Updates"}, "urlImport": {"robotsBlocked": "🚫 Import durch robots.txt blockiert", "robotsBlockedMessage": "Import blockiert: Die robots.txt-<PERSON><PERSON> dieser Website verhindert den automatisierten Zugriff auf diese URL.", "robotsBlockedTitle": "Zugriff durch robots.txt blockiert"}, "deepResearch": {"title": "Tiefenforschung", "researchComplete": "Forschung abgeschlossen", "description": "Analysiere und sammle umfassende Informationen", "step": "<PERSON><PERSON><PERSON> {step}", "progress": "Fort<PERSON><PERSON>t", "areasForFurtherResearch": "Bereiche für weitere Forschung", "researchPlan": "Forschungsplan", "topic": "<PERSON>a", "subtopics": "Unterthemen", "questions": "Fragen", "identified": "{count} identifiziert", "planned": "{count} geplant", "researchSummary": "Forschungszusammenfassung", "iterations": "Iterationen", "sources": "<PERSON><PERSON>", "confidence": "<PERSON><PERSON><PERSON><PERSON>", "quality": "Qualität", "researchSteps": "Forschungsschritte ({count})", "hide": "Verbergen", "show": "Anzeigen", "areasToExplore": "<PERSON><PERSON> erforschende Bereiche"}, "chatInput": {"internal": "Intern", "web": "Web", "mcp": "MCP", "deepResearch": "Tiefenforschung"}, "sharedThread": {"threadNotFound": "Thread nicht gefunden", "threadNotFoundDescription": "Dieser geteilte Thread existiert nicht oder wurde entfernt.", "linkExpired": "Link a<PERSON>gelau<PERSON>", "linkExpiredDescription": "Dieser geteilte Thread-Link ist abgelaufen.", "accessDenied": "<PERSON><PERSON><PERSON> verweigert", "accessDeniedDescription": "<PERSON>e haben keine Berechtigung, diesen geteilten Thread anzuzeigen.", "signInToAccess": "Anmelden für Zugriff", "errorLoadingThread": "<PERSON><PERSON> beim Laden des Threads", "errorLoadingThreadDescription": "Es gab einen Fehler beim <PERSON>den dieses geteilten Threads. Bitte versuchen Sie es später erneut.", "title": "<PERSON><PERSON><PERSON>ead", "public": "<PERSON><PERSON><PERSON><PERSON>", "private": "Privat", "createdBy": "<PERSON><PERSON><PERSON><PERSON> {name}", "copied": "Kopiert!", "copyLink": "<PERSON>", "expires": "<PERSON><PERSON><PERSON>t ab {date}", "wantToComment": "Möchten Sie kommentieren?", "signIn": "Anmelden"}, "commentSidebar": {"title": "Kommentare", "loadingComments": "Kommentare werden geladen...", "noCommentsYet": "Noch keine Kommentare. <PERSON><PERSON> der Erste, der kommentiert!", "showResolved": "Gelöste anzeigen ({count})", "hideResolved": "Gelöste ausblenden ({count})", "replyingTo": "Antwort an {name}", "addCommentPlaceholder": "Kommentar hinzufügen... (verwenden Sie @ um Benutzer zu erwähnen)", "editCommentPlaceholder": "Kommentar bearbeiten...", "pressToSend": "Drücken Sie Cmd+Enter zum Senden", "mentionedUser": "{name} erwähnt", "sending": "Wird gesendet...", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "reply": "Antworten", "resolve": "<PERSON><PERSON><PERSON>", "reopen": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "save": "Speichern", "cancel": "Abbrechen", "commentAdded": "<PERSON><PERSON><PERSON><PERSON>", "commentResolved": "<PERSON><PERSON><PERSON><PERSON>", "commentReopened": "Kommentar wieder g<PERSON><PERSON>ffnet", "commentUpdated": "Kommentar aktualisiert", "commentDeleted": "Kommentar <PERSON>", "failedToLoadComments": "Kommentare konnten nicht geladen werden", "failedToAddComment": "Kommentar konnte nicht hinzugefügt werden", "failedToUpdateComment": "Kommentar konnte nicht aktualisiert werden", "failedToDeleteComment": "Kommentar konnte nicht gelöscht werden", "comment": "Kommentar"}, "audioProcessing": {"title": "Audio-Verarbeitungsstatus", "pending": "In Warteschlange für Verarbeitung", "processing": "Audio wird verarbeitet...", "completed": "Verarbeitung abgeschlossen", "failed": "Verarbeitung fehlgeschlagen", "unknown": "Status unbekannt", "filesProcessed": "{count} Datei(en) verarbeitet"}}