"""
High-Performance Vector Search Tool for CopilotKit Agent

This tool provides optimized vector search with semantic similarity,
hybrid search capabilities, and intelligent result aggregation.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Type, Tuple
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerFor<PERSON>oolRun
from pydantic import BaseModel, Field
from bson import ObjectId
from pymongo import MongoClient
from langchain_community.vectorstores import MongoDBAtlasVectorSearch
from langchain_openai import AzureOpenAIEmbeddings
import os
import time

logger = logging.getLogger(__name__)

class SafeEmbeddingWrapper:
    """
    A wrapper around embedding models that ensures proper input validation
    to prevent tokenization issues that cause Azure OpenAI API errors.
    """

    def __init__(self, base_embedding_model):
        self.base_model = base_embedding_model

    def embed_query(self, text):
        """Safely embed a query with input validation."""
        if not isinstance(text, str):
            logger.error(f"Expected string for query, got {type(text)}: {text}")
            if isinstance(text, list):
                logger.error("Query appears to be tokenized - this will cause embedding API errors")
                # Check if this looks like token IDs (list of integers)
                if all(isinstance(x, int) for x in text):
                    logger.error(f"Detected token IDs: {text}")
                    raise ValueError(f"Query appears to be tokenized (list of token IDs: {text}) instead of a string. This indicates a bug in the query processing pipeline.")
                raise ValueError("Query appears to be tokenized (list of tokens) instead of a string")
            text = str(text)

        if not text.strip():
            logger.warning("Empty query provided to embedding model")
            # Return a zero vector for empty queries
            return [0.0] * getattr(self.base_model, 'dimension', 1536)

        logger.debug(f"Calling base model embed_query with text: {text[:100]}...")
        return self.base_model.embed_query(text)

    def embed_documents(self, texts):
        """Safely embed documents with input validation."""
        if not isinstance(texts, list):
            logger.error(f"Expected list of texts, got {type(texts)}: {texts}")
            raise ValueError(f"Expected list of texts, got {type(texts)}")

        validated_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                logger.error(f"Text at index {i} is not a string: {type(text)} - {text}")
                if isinstance(text, list):
                    logger.error("Text appears to be tokenized - this will cause embedding API errors")
                    raise ValueError(f"Text at index {i} appears to be tokenized (list of tokens) instead of a string")
                text = str(text)
            validated_texts.append(text)

        return self.base_model.embed_documents(validated_texts)

    def __getattr__(self, name):
        """Delegate other attributes to the base model."""
        return getattr(self.base_model, name)

class VectorSearchInput(BaseModel):
    """Input schema for vector search tool."""
    query: str = Field(description="The search query for semantic similarity search")
    workspace_ids: List[str] = Field(description="List of workspace IDs to search in")
    search_type: str = Field(default="similarity", description="Type of search: only 'similarity' is supported")
    top_k: int = Field(default=6, description="Number of top results to return")
    similarity_threshold: float = Field(default=0.5, description="Minimum similarity threshold")
    is_follow_up: bool = Field(default=False, description="Whether this is a follow-up question that doesn't need new vector search")

class VectorSearchTool(BaseTool):
    """
    High-performance vector search tool that performs semantic similarity search
    across multiple workspaces with advanced ranking and filtering.
    """
    
    name: str = "vector_search_tool"
    description: str = """
    Perform semantic vector search to find documents similar to the query.
    Use this tool for finding documents based on semantic meaning rather than exact keywords.
    This tool is particularly useful for conceptual queries, finding related topics,
    or when keyword search might miss relevant content due to different terminology.
    """
    args_schema: Type[BaseModel] = VectorSearchInput

    # Pydantic v2 requires explicit field definitions
    db_client: Any = Field(default=None, exclude=True)
    embedding_model: Any = Field(default=None, exclude=True)
    vector_stores: Dict[str, Any] = Field(default_factory=dict, exclude=True)

    def __init__(self, db_client=None, **kwargs):
        super().__init__(**kwargs)
        self.db_client = db_client
        self.embedding_model = None
        self.vector_stores = {}  # Cache for vector stores
        self._initialize_embedding_model()
    
    def _initialize_embedding_model(self):
        """Initialize the embedding model for vector search."""
        try:
            # Use Azure Vision embeddings instead of LangChain AzureOpenAIEmbeddings
            # to avoid the tokenization bug in langchain-openai 0.3.13
            logger.info("Using Azure Vision embeddings to avoid LangChain tokenization bug")
            from ..azure_vision_embeddings import AzureVisionEmbeddings

            base_model = AzureVisionEmbeddings(
                azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"),
                deployment_name=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT"),
                api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                model=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0")
            )

            # Wrap it with our validation wrapper
            self.embedding_model = SafeEmbeddingWrapper(base_model)
            logger.info("Vector search embedding model initialized successfully with safety wrapper")
            return self.embedding_model
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise

    async def _enhance_document_metadata(self, original_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance document metadata using existing vector embedding metadata."""
        enhanced_metadata = original_metadata.copy()

        # The workspace information should already be in the metadata from vector embeddings
        # Based on workspace_rag.py, the metadata includes: workspace_id, fileId, slug
        if "workspace" not in enhanced_metadata:
            enhanced_metadata["workspace"] = {
                "id": enhanced_metadata.get("workspace_id", ""),
                "slug": enhanced_metadata.get("slug", ""),
                "name": enhanced_metadata.get("workspaceName", "")  # This might not be in embeddings
            }

        # Ensure file information is properly structured
        file_id = enhanced_metadata.get("fileId")
        if file_id and "file_name" not in enhanced_metadata:
            # Use existing metadata fields that should be present in vector embeddings
            # The 'source' field typically contains the file name
            source_name = enhanced_metadata.get("source", "")
            enhanced_metadata.update({
                "file_name": source_name,
                "source": source_name.split("/")[-1] if source_name else ""
            })

        return enhanced_metadata

    def _normalize_query_for_search(self, query: str) -> str:
        """
        Normalize query for better semantic search while preserving important context.

        This method applies minimal normalization to improve semantic similarity
        matching while preserving case for proper nouns and technical terms.
        """
        if not query or not isinstance(query, str):
            return query

        # Preserve the original query structure for better semantic matching
        # Only apply minimal normalization to avoid losing important context
        normalized = query.strip()

        # Remove extra whitespace but preserve case for proper nouns and technical terms
        normalized = ' '.join(normalized.split())

        # Only convert to lowercase if the query is all uppercase (likely shouting)
        if normalized.isupper():
            normalized = normalized.lower()

        logger.debug(f"Query normalization: '{query}' -> '{normalized}'")
        return normalized

    async def _get_vector_store(self, workspace_id: str) -> Optional[MongoDBAtlasVectorSearch]:
        """Get or create a vector store for a specific workspace."""
        if workspace_id in self.vector_stores:
            return self.vector_stores[workspace_id]
        
        try:
            # First try to get workspace-specific vector configuration
            vector_config = await self.db_client.VectorDBSettings.find_one({
                "workspaceId": ObjectId(workspace_id)
            })

            # If no workspace-specific config, try to get tenant-level config
            if not vector_config:
                # Get workspace to find tenant
                workspace = await self.db_client.Workspace.find_one({
                    "_id": ObjectId(workspace_id)
                })

                if workspace:
                    tenant_id = workspace.get("tenantId")
                    if tenant_id:
                        # Look for any vector config for this tenant
                        vector_config = await self.db_client.VectorDBSettings.find_one({
                            "tenantId": ObjectId(tenant_id)
                        })
                        logger.info(f"Using tenant-level vector config for workspace {workspace_id}")

            if not vector_config:
                # Use default configuration from environment variables as fallback
                logger.info(f"No vector configuration found, using default environment config for workspace {workspace_id}")
                vector_config = {
                    "localUri": os.getenv("VECTOR_DATABASE_URL"),
                    "environment": os.getenv("VECTOR_DATABASE_NAME"),
                    "provider": "MONGODB"  # Default provider
                }
                logger.debug(f"Vector config: {vector_config}")

                # Validate that we have the required environment variables
                if not vector_config["localUri"] or not vector_config["environment"]:
                    logger.error(f"Missing required environment variables: VECTOR_DATABASE_URL or VECTOR_DATABASE_NAME")
                    return None
            
            # Create MongoDB connection
            logger.debug(f"Creating MongoDB connection for workspace {workspace_id}")
            connection_string = vector_config.get("localUri", os.getenv("VECTOR_DATABASE_URL"))
            db_name = vector_config.get("environment", os.getenv("VECTOR_DATABASE_NAME"))
            
            logger.debug(f"Connecting to MongoDB with connection string: {connection_string} and database name: {db_name}")
            client = MongoClient(connection_string)
            db = client[db_name]
            logger.debug(f"Connected to MongoDB with connection string: {connection_string} and database name: {db_name}")
            logger.debug(f"Creating collection for workspace {workspace_id}")
            collection = db[f"workspace_{workspace_id}"]
            logger.debug(f"Created collection for workspace {workspace_id}")
            
            # Create vector store
            logger.debug(f"Creating vector store for workspace {workspace_id}")
            vector_store = MongoDBAtlasVectorSearch(
                collection=collection,
                embedding=self.embedding_model,
                index_name="default_vector_index",
                text_key="content",
                embedding_key="embedding"
            )
            logger.debug(f"Created vector store for workspace {workspace_id}")
            
            # Cache the vector store
            self.vector_stores[workspace_id] = vector_store
            logger.info(f"Vector store created for workspace {workspace_id}")
            return vector_store
            
        except Exception as e:
            logger.error(f"Failed to create vector store for workspace {workspace_id}: {e}")
            return None
    
    async def _similarity_search(self, workspace_id: str, query: str, top_k: int,
                                similarity_threshold: float) -> List[Tuple[Dict[str, Any], float]]:
        """Perform similarity search in a specific workspace."""
        vector_store = await self._get_vector_store(workspace_id)
        if not vector_store:
            return []

        try:
            # Ensure query is a string and not tokenized
            if not isinstance(query, str):
                logger.error(f"Query is not a string: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return []
                query = str(query)

            # Normalize query for case-insensitive search
            normalized_query = self._normalize_query_for_search(query)

            logger.debug(f"Performing similarity search for workspace {workspace_id} with query: '{query[:100]}...'")
            logger.debug(f"Normalized query: '{normalized_query[:100]}...'")

            # Perform similarity search with scores and additional debugging
            logger.debug(f"About to call similarity_search_with_score with query type: {type(normalized_query)}")
            logger.debug(f"Query content: {normalized_query}")

            # Ensure query is definitely a string before calling vector store
            if not isinstance(normalized_query, str):
                logger.error(f"Query is not a string before vector store call: {type(normalized_query)} - {normalized_query}")
                normalized_query = str(normalized_query)

            # Retrieve more documents initially for better filtering
            initial_k = min(top_k * 2, 30)

            docs_with_scores = await asyncio.to_thread(
                vector_store.similarity_search_with_score,
                normalized_query,
                k=initial_k
            )

            # Enhanced similarity calculation and filtering
            results = []
            filtered_count = 0

            for doc, score in docs_with_scores:
                # Enhanced similarity score calculation
                # MongoDB Atlas returns cosine distance (0 = identical, 2 = opposite)
                # Be more lenient with scoring

                if score <= 2.0:  # Increased from 1.0 to 2.0 for better coverage
                    similarity = max(0.0, 1.0 - (float(score) / 2.0))
                else:
                    # Even for higher distances, give some score
                    similarity = max(0.0, 1.0 / (1.0 + float(score)))

                # Special boost for person name queries
                content_lower = doc.page_content.lower()
                query_lower = query.lower()

                # Check if this looks like a person name match
                if any(term in content_lower for term in query_lower.split() if len(term) > 2):
                    # Boost score for potential person name matches
                    similarity = min(1.0, similarity * 1.5)

                # Apply threshold for relevance filtering - but be more lenient
                # Use a much lower effective threshold for internal searches
                effective_threshold = min(similarity_threshold, 0.1)  # Cap at very low threshold

                if similarity >= effective_threshold:
                    # Enhance metadata using existing vector embedding metadata
                    enhanced_metadata = await self._enhance_document_metadata(doc.metadata)

                    result = {
                        "content": doc.page_content,
                        "metadata": {
                            **enhanced_metadata,
                            "workspace_id": workspace_id,
                            "similarity_score": similarity,
                            "search_score": float(score),
                            "raw_distance": float(score)
                        }
                    }
                    results.append((result, similarity))
                else:
                    filtered_count += 1

            logger.info(f"Found {len(results)} similar documents in workspace {workspace_id} (filtered out {filtered_count} below threshold {effective_threshold})")
            return results

        except Exception as e:
            logger.error(f"Error in similarity search for workspace {workspace_id}: {e}")
            return []
    
    async def _mmr_search(self, workspace_id: str, query: str, top_k: int) -> List[Tuple[Dict[str, Any], float]]:
        """Perform Maximum Marginal Relevance search for diverse results."""
        vector_store = await self._get_vector_store(workspace_id)
        if not vector_store:
            return []

        try:
            # Ensure query is a string and not tokenized
            if not isinstance(query, str):
                logger.error(f"Query is not a string: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return []
                query = str(query)

            # Normalize query for case-insensitive search
            normalized_query = self._normalize_query_for_search(query)

            logger.debug(f"Performing MMR search for workspace {workspace_id} with query: '{query[:100]}...'")
            logger.debug(f"Normalized query: '{normalized_query[:100]}...'")

            # Perform MMR search
            docs = await asyncio.to_thread(
                vector_store.max_marginal_relevance_search,
                normalized_query,
                k=top_k,
                fetch_k=top_k * 2  # Fetch more candidates for diversity
            )

            # Format results (MMR doesn't return scores, so we need to calculate actual similarity)
            results = []

            # Get the query embedding for similarity calculation
            try:
                from ..azure_vision_embeddings import AzureVisionEmbeddings
                embedding_model = AzureVisionEmbeddings(
                    azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                    api_key=os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"),
                    deployment_name=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT"),
                    api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                    model=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0")
                )
                query_embedding = embedding_model.embed_query(normalized_query)
            except Exception as e:
                logger.error(f"Failed to get query embedding for MMR scoring: {e}")
                # Fallback to position-based scoring with more conservative estimates
                for i, doc in enumerate(docs):
                    # More conservative position-based scoring
                    estimated_score = max(0.3, 0.9 - (i * 0.08))  # Start at 0.9, decrease by 0.08

                    enhanced_metadata = await self._enhance_document_metadata(doc.metadata)
                    result = {
                        "content": doc.page_content,
                        "metadata": {
                            **enhanced_metadata,
                            "workspace_id": workspace_id,
                            "mmr_rank": i + 1,
                            "estimated_relevance": estimated_score
                        }
                    }
                    results.append((result, estimated_score))
                return results

            # Calculate actual similarity scores for MMR results
            for i, doc in enumerate(docs):
                try:
                    # Get document embedding from metadata or calculate it
                    doc_embedding = None
                    if hasattr(doc, 'metadata') and 'embedding' in doc.metadata:
                        doc_embedding = doc.metadata['embedding']

                    if doc_embedding and query_embedding:
                        # Calculate cosine similarity
                        import numpy as np
                        query_norm = np.linalg.norm(query_embedding)
                        doc_norm = np.linalg.norm(doc_embedding)
                        if query_norm > 0 and doc_norm > 0:
                            cosine_sim = np.dot(query_embedding, doc_embedding) / (query_norm * doc_norm)
                            similarity_score = max(0.0, cosine_sim)
                        else:
                            similarity_score = max(0.3, 0.9 - (i * 0.08))
                    else:
                        # Fallback to conservative position-based scoring
                        similarity_score = max(0.3, 0.9 - (i * 0.08))

                except Exception as e:
                    logger.warning(f"Error calculating similarity for MMR result {i}: {e}")
                    similarity_score = max(0.3, 0.9 - (i * 0.08))

                # Enhance metadata using existing vector embedding metadata
                enhanced_metadata = await self._enhance_document_metadata(doc.metadata)

                result = {
                    "content": doc.page_content,
                    "metadata": {
                        **enhanced_metadata,
                        "workspace_id": workspace_id,
                        "mmr_rank": i + 1,
                        "similarity_score": similarity_score,
                        "estimated_relevance": similarity_score
                    }
                }
                results.append((result, similarity_score))

            logger.info(f"Found {len(results)} diverse documents in workspace {workspace_id}")
            return results

        except Exception as e:
            logger.error(f"Error in MMR search for workspace {workspace_id}: {e}")
            return []
    
    async def _hybrid_search(self, workspace_id: str, query: str, top_k: int, 
                           similarity_threshold: float) -> List[Tuple[Dict[str, Any], float]]:
        """Perform hybrid search combining similarity and MMR."""
        # Get both similarity and MMR results
        similarity_task = self._similarity_search(workspace_id, query, top_k // 2, similarity_threshold)
        mmr_task = self._mmr_search(workspace_id, query, top_k // 2)
        
        similarity_results, mmr_results = await asyncio.gather(similarity_task, mmr_task)
        
        # Combine and deduplicate results
        combined_results = {}
        
        # Add similarity results
        for result, score in similarity_results:
            doc_id = result.get("metadata", {}).get("fileId", id(result))
            if doc_id not in combined_results:
                combined_results[doc_id] = (result, score, "similarity")
        
        # Add MMR results (avoid duplicates)
        for result, score in mmr_results:
            doc_id = result.get("metadata", {}).get("fileId", id(result))
            if doc_id not in combined_results:
                combined_results[doc_id] = (result, score, "mmr")
        
        # Sort by score and return top_k
        sorted_results = sorted(
            [(result, score) for result, score, _ in combined_results.values()],
            key=lambda x: x[1],
            reverse=True
        )
        
        return sorted_results[:top_k]
    
    async def _parallel_vector_search(self, workspace_ids: List[str], query: str, 
                                    search_type: str, top_k: int, 
                                    similarity_threshold: float) -> List[Tuple[Dict[str, Any], float]]:
        """Perform parallel vector searches across multiple workspaces."""
        # Create search tasks based on search type
        if search_type == "similarity":
            search_tasks = [
                self._similarity_search(workspace_id, query, top_k, similarity_threshold)
                for workspace_id in workspace_ids
            ]
        elif search_type == "mmr":
            search_tasks = [
                self._mmr_search(workspace_id, query, top_k)
                for workspace_id in workspace_ids
            ]
        elif search_type == "hybrid":
            search_tasks = [
                self._hybrid_search(workspace_id, query, top_k, similarity_threshold)
                for workspace_id in workspace_ids
            ]
        else:
            raise ValueError(f"Unsupported search type: {search_type}")
        
        # Execute searches in parallel
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Combine results
        all_results = []
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.error(f"Vector search failed for workspace {workspace_ids[i]}: {result}")
                continue
            all_results.extend(result)
        
        return all_results
    
    def _aggregate_and_rank_results(self, results: List[Tuple[Dict[str, Any], float]], 
                                  top_k: int) -> List[Dict[str, Any]]:
        """Aggregate results from multiple workspaces and rank them."""
        if not results:
            return []
        
        # Sort by score (descending)
        sorted_results = sorted(results, key=lambda x: x[1], reverse=True)
        
        # Take top_k and format for output
        final_results = []
        for result, score in sorted_results[:top_k]:
            result["metadata"]["final_score"] = score
            final_results.append(result)
        
        return final_results
    
    async def _arun(
        self,
        query: str,
        workspace_ids: List[str],
        search_type: str = "similarity",
        top_k: int = 5,
        similarity_threshold: float = 0.5,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Async implementation of the vector search tool."""
        start_time = time.time()

        try:
            # Validate query input early to catch tokenization issues
            if not isinstance(query, str):
                logger.error(f"Vector search received non-string query: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return {
                        "content": "Error: Query was tokenized instead of being a string. This indicates a bug in the query processing pipeline.",
                        "documents": []
                    }
                query = str(query)

            if not query.strip():
                logger.warning("Empty query provided to vector search")
                return {
                    "content": "No query provided for vector search.",
                    "documents": []
                }

            # Skip vector search for follow-up questions
            if is_follow_up:
                logger.info(f"Skipping vector search for follow-up question: '{query[:100]}...'")
                return {
                    "content": "Follow-up question detected - using existing conversation context instead of performing new vector search.",
                    "documents": []
                }

            logger.info(f"Starting {search_type} vector search across {len(workspace_ids)} workspaces for query: '{query[:100]}...'")
            logger.debug(f"Full query: {query}")
            
            # Perform parallel vector search
            results_with_scores = await self._parallel_vector_search(
                workspace_ids, query, search_type, top_k, similarity_threshold
            )

            # If no results found with the initial threshold, try with progressively lower thresholds
            if not results_with_scores:
                fallback_thresholds = [
                    max(0.1, similarity_threshold - 0.2),  # First fallback
                    0.1,  # Very low threshold
                    2.0   # Very permissive for cosine distance
                ]

                for fallback_threshold in fallback_thresholds:
                    if fallback_threshold != similarity_threshold:  # Don't repeat the same threshold
                        logger.info(f"No results found with threshold {similarity_threshold}, trying fallback threshold {fallback_threshold}")
                        results_with_scores = await self._parallel_vector_search(
                            workspace_ids, query, search_type, top_k, fallback_threshold
                        )
                        if results_with_scores:
                            logger.info(f"Found {len(results_with_scores)} results with fallback threshold {fallback_threshold}")
                            break

            if not results_with_scores:
                logger.info("No vector search results found even with fallback threshold")
                return {
                    "content": "No semantically similar documents found.",
                    "documents": []
                }
            
            # Aggregate and rank results
            # final_results = self._aggregate_and_rank_results(results_with_scores, top_k)
            
            # Format response
            response_parts = [f"Found {len(results_with_scores)} semantically similar documents:"]

            # Convert results to proper format for response
            formatted_results = []
            for i, item in enumerate(results_with_scores, 1):
                # Handle both tuple format (result, score) and dict format
                if isinstance(item, tuple) and len(item) == 2:
                    doc, score = item
                    metadata = doc.get("metadata", {})
                    source = metadata.get("source", "Unknown")
                    content_preview = doc.get("content", "")[:150]

                    # Add final_score to metadata for consistency
                    metadata["final_score"] = score
                    formatted_results.append(doc)
                else:
                    # Assume it's already a dict
                    doc = item
                    metadata = doc.get("metadata", {})
                    score = metadata.get("final_score", 0)
                    source = metadata.get("source", "Unknown")
                    content_preview = doc.get("content", "")[:150]
                    formatted_results.append(doc)

                response_parts.append(
                    f"\n[{i}] Source: {source} (Score: {score:.3f})\n"
                    f"Content: {content_preview}..."
                )
            
            elapsed_time = time.time() - start_time
            logger.info(f"Vector search completed in {elapsed_time:.2f} seconds")

            return {"content": "\n".join(response_parts), "documents": formatted_results}
            
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            return {
                "content": f"Error performing vector search: {str(e)}",
                "documents": []
            }
    
    def _run(
        self,
        query: str,
        workspace_ids: List[str],
        search_type: str = "similarity",
        top_k: int = 6,
        similarity_threshold: float = 0.5,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Sync wrapper for async implementation."""
        return asyncio.run(self._arun(query, workspace_ids, search_type, top_k, similarity_threshold, is_follow_up, run_manager))
