"""
Azure Video Indexer Service for audio transcription and analysis.

This service handles audio file upload, transcription, and content extraction
using Azure Video Indexer API with Account ID + Access Token authentication.
"""

import os
import json
import time
import base64
import logging
import aiohttp
import asyncio
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin
from datetime import datetime, timedelta
from src.config import settings

# Import diskcache with graceful fallback
try:
    import diskcache as dc
    DISKCACHE_AVAILABLE = True
except ImportError:
    DISKCACHE_AVAILABLE = False
    dc = None

logger = logging.getLogger(__name__)

class AzureVideoIndexerService:
    """Service for Azure Video Indexer audio transcription."""
    
    def __init__(self):
        # Core configuration
        self.account_id = settings.AZURE_VIDEO_INDEXER_ACCOUNT_ID
        self.location = settings.AZURE_VIDEO_INDEXER_LOCATION

        # Azure Resource Manager configuration (preferred method)
        self.subscription_id = getattr(settings, 'AZURE_SUBSCRIPTION_ID', None)
        self.resource_group_name = getattr(settings, 'AZURE_RESOURCE_GROUP_NAME', None)
        self.account_name = getattr(settings, 'AZURE_VIDEO_INDEXER_ACCOUNT_NAME', None)
        self.tenant_id = getattr(settings, 'AZURE_TENANT_ID', None)
        self.client_id = getattr(settings, 'AZURE_CLIENT_ID', None)
        self.client_secret = getattr(settings, 'AZURE_CLIENT_SECRET', None)

        # Static token fallback
        self.static_access_token = getattr(settings, 'AZURE_VIDEO_INDEXER_ACCESS_TOKEN', None)

        # Token management
        self.access_token = None
        self.token_expires_at = None
        self.token_refresh_buffer = 300  # Refresh token 5 minutes before expiry

        # Diskcache configuration for token persistence
        self._cache = None
        self._cache_directory = "./cache/azure_video_indexer_tokens"
        self._token_key_prefix = "token"

        logger.info(f"Azure Video Indexer configuration: Account {self.account_id} in {self.location}")

        # Check Azure Resource Manager configuration (preferred)
        has_arm_config = all([
            self.subscription_id, self.resource_group_name, self.account_name,
            self.tenant_id, self.client_id, self.client_secret
        ])

        # Check static token fallback
        has_static_token = bool(self.static_access_token)

        auth_method = "Azure Resource Manager" if has_arm_config else ("Static Token" if has_static_token else "None")
        logger.info(f"Authentication method: {auth_method}")

        if self.account_id and self.location and (has_arm_config or has_static_token):
            self.configured = True
            self.base_url = f"https://api.videoindexer.ai/{self.location}/Accounts/{self.account_id}"
            logger.info(" Azure Video Indexer configured successfully")
        else:
            self.configured = False
            self.base_url = None
            logger.warning("Azure Video Indexer not configured - missing required credentials")

    def _truncate_filename(self, filename: str, max_length: int = 80) -> str:
        """
        Truncate filename to meet Azure Video Indexer's 80-character limit.

        Args:
            filename: Original filename
            max_length: Maximum allowed length (default: 80)

        Returns:
            Truncated filename preserving extension
        """
        if len(filename) <= max_length:
            return filename

        # Find the last dot to separate name and extension
        last_dot_index = filename.rfind('.')
        if last_dot_index == -1:
            # No extension found, just truncate
            return filename[:max_length]

        extension = filename[last_dot_index:]
        name_without_ext = filename[:last_dot_index]

        # Calculate how much space we have for the name
        max_name_length = max_length - len(extension)

        if max_name_length <= 0:
            # Extension is too long, just truncate the whole thing
            return filename[:max_length]

        # Truncate the name and add back the extension
        truncated_name = name_without_ext[:max_name_length]
        result = truncated_name + extension

        logger.info(f"Truncated filename from '{filename}' to '{result}' (length: {len(result)})")
        return result

    @property
    def cache(self):
        """Get diskcache Cache instance for token persistence."""
        if self._cache is None:
            try:
                if not DISKCACHE_AVAILABLE:
                    logger.warning("diskcache library not available, token persistence disabled")
                    return None

                # Create cache directory if it doesn't exist
                os.makedirs(self._cache_directory, exist_ok=True)

                # Initialize diskcache with TTL support
                self._cache = dc.Cache(
                    directory=self._cache_directory,
                    size_limit=50 * 1024 * 1024,  # 50MB limit
                    eviction_policy='least-recently-used'
                )
                logger.debug(f"Diskcache initialized at {self._cache_directory}")

            except Exception as e:
                logger.warning(f"Failed to initialize diskcache for token persistence: {e}")
                self._cache = None
        return self._cache

    def _get_token_cache_key(self) -> str:
        """Generate cache key for storing tokens."""
        # Create unique key based on service, account, and location
        key_parts = [
            self._token_key_prefix,
            "azure_video_indexer",
            self.account_id or "unknown",
            self.location or "unknown"
        ]
        return ":".join(key_parts)

    def _save_token_to_storage(self, token: str, expires_at: datetime) -> bool:
        """Save access token to persistent storage using diskcache."""
        try:
            cache = self.cache
            if cache is None:
                logger.warning("Cache not available, skipping persistent storage")
                return False

            # Create token data structure
            token_data = {
                "service": "azure_video_indexer",
                "account_id": self.account_id,
                "location": self.location,
                "access_token": token,
                "expires_at": expires_at.isoformat(),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Calculate TTL in seconds
            current_time = datetime.now()
            ttl_seconds = int((expires_at - current_time).total_seconds())

            # Ensure TTL is positive
            if ttl_seconds <= 0:
                logger.warning("Token already expired, not saving to storage")
                return False

            # Save to cache with TTL
            cache_key = self._get_token_cache_key()
            cache.set(cache_key, token_data, expire=ttl_seconds)

            logger.info(f"Access token saved to persistent storage (expires: {expires_at})")
            return True

        except Exception as e:
            logger.warning(f"Failed to save token to persistent storage: {e}")
            return False

    def _load_token_from_storage(self) -> Optional[Dict[str, Any]]:
        """Load access token from persistent storage using diskcache."""
        try:
            cache = self.cache
            if cache is None:
                logger.debug("Cache not available, skipping persistent storage lookup")
                return None

            # Get token from cache
            cache_key = self._get_token_cache_key()
            token_data = cache.get(cache_key)

            if token_data:
                expires_at_str = token_data.get("expires_at")
                access_token = token_data.get("access_token")

                if expires_at_str and access_token:
                    # Parse expiration time
                    expires_at = datetime.fromisoformat(expires_at_str)

                    # Check if token is still valid (with buffer)
                    current_time = datetime.now()
                    buffer_time = timedelta(seconds=self.token_refresh_buffer)

                    if current_time < (expires_at - buffer_time):
                        logger.info(f"Loaded valid token from persistent storage (expires: {expires_at})")
                        return {
                            "access_token": access_token,
                            "expires_at": expires_at
                        }
                    else:
                        logger.info(f"Token in persistent storage expired at {expires_at}, will generate new one")
                        # Clean up expired token
                        self._remove_token_from_storage()
                        return None
                else:
                    logger.warning("Invalid token data in persistent storage")
                    return None
            else:
                logger.debug("No token found in persistent storage")
                return None

        except Exception as e:
            logger.warning(f"Failed to load token from persistent storage: {e}")
            return None

    def _remove_token_from_storage(self) -> bool:
        """Remove access token from persistent storage using diskcache."""
        try:
            cache = self.cache
            if cache is None:
                return False

            cache_key = self._get_token_cache_key()

            # Check if key exists before deletion
            if cache_key in cache:
                del cache[cache_key]
                logger.info("Removed expired token from persistent storage")
                return True
            else:
                logger.debug("No token found to remove from persistent storage")
                return False

        except Exception as e:
            logger.warning(f"Failed to remove token from persistent storage: {e}")
            return False

    def _parse_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Parse JWT token and extract payload without verification."""
        try:
            # JWT tokens have 3 parts separated by dots: header.payload.signature
            parts = token.split('.')
            if len(parts) != 3:
                logger.warning("Invalid JWT token format: expected 3 parts")
                return None

            # Decode the payload (second part)
            payload_part = parts[1]

            # Add padding if needed (JWT base64 encoding may not have padding)
            padding = 4 - (len(payload_part) % 4)
            if padding != 4:
                payload_part += '=' * padding

            # Decode base64
            payload_bytes = base64.urlsafe_b64decode(payload_part)
            payload = json.loads(payload_bytes.decode('utf-8'))

            return payload

        except Exception as e:
            logger.warning(f"Failed to parse JWT token: {e}")
            return None

    def _validate_token_expiration(self, token: str) -> tuple[bool, Optional[datetime]]:
        """Validate token expiration and return (is_valid, expiration_time)."""
        try:
            payload = self._parse_jwt_token(token)
            if not payload:
                logger.warning("Cannot validate token: failed to parse JWT")
                return False, None

            # Extract expiration time (exp claim)
            exp_timestamp = payload.get('exp')
            if not exp_timestamp:
                logger.warning("JWT token missing 'exp' claim")
                return False, None

            # Convert timestamp to datetime
            exp_datetime = datetime.fromtimestamp(exp_timestamp)
            current_time = datetime.now()

            # Check if token is still valid (with buffer)
            buffer_time = timedelta(seconds=self.token_refresh_buffer)
            is_valid = current_time < (exp_datetime - buffer_time)

            return is_valid, exp_datetime

        except Exception as e:
            logger.warning(f"Error validating token expiration: {e}")
            return False, None

    def _check_environment_token(self) -> Optional[str]:
        """Check if a valid access token exists in environment variables."""
        try:
            env_token = getattr(settings, 'AZURE_VIDEO_INDEXER_ACCESS_TOKEN', None)
            if not env_token:
                return None

            logger.info("Found environment token, validating...")

            # Validate the environment token
            is_valid, exp_time = self._validate_token_expiration(env_token)

            if is_valid and exp_time:
                logger.info(f" Environment token is valid until {exp_time}")
                return env_token
            else:
                if exp_time:
                    logger.warning(f"Environment token expired at {exp_time}")
                else:
                    logger.warning("Environment token is invalid or malformed")
                return None

        except Exception as e:
            logger.warning(f"Error checking environment token: {e}")
            return None

    async def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests with automatic token management."""
        # Ensure we have a valid access token
        await self._ensure_valid_token()

        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

    async def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token with intelligent caching and validation."""
        try:
            # Step 1: Check if we have a cached token that's still valid
            if self.access_token and self.token_expires_at:
                current_time = datetime.now()
                buffer_time = timedelta(seconds=self.token_refresh_buffer)

                if current_time < (self.token_expires_at - buffer_time):
                    logger.info(f"Using cached token (valid until {self.token_expires_at})")
                    return True
                else:
                    logger.info(f"Cached token expired at {self.token_expires_at}, need new token")

            # Step 2: Check persistent storage for valid token
            stored_token = self._load_token_from_storage()
            if stored_token:
                logger.info("Using valid token from persistent storage")
                self.access_token = stored_token["access_token"]
                self.token_expires_at = stored_token["expires_at"]
                return True

            # Step 3: Check environment token as fallback
            env_token = self._check_environment_token()
            if env_token:
                # Validate and use environment token
                is_valid, exp_time = self._validate_token_expiration(env_token)
                if is_valid and exp_time:
                    logger.info(" Using valid environment token")
                    self.access_token = env_token
                    self.token_expires_at = exp_time
                    # Save to persistent storage for future use
                    self._save_token_to_storage(env_token, exp_time)
                    return True
                else:
                    logger.warning("Environment token is expired or invalid, generating new token")

            # Step 4: Generate new token via Azure Resource Manager
            logger.info("Generating new access token...")
            success = await self._generate_access_token()
            if not success:
                logger.error("Failed to generate access token")
                return False

            logger.info("New access token generated and cached successfully")
            return True

        except Exception as e:
            logger.error(f"Error ensuring valid token: {e}")
            return False

    def _needs_token_refresh(self) -> bool:
        """Check if the access token needs to be refreshed."""
        # If no token exists, we need one
        if not self.access_token:
            logger.info("No access token available, need to generate")
            return True

        # If no expiration time is set, assume we need to refresh
        if not self.token_expires_at:
            logger.info("No token expiration time set, need to refresh")
            return True

        # Check if token is close to expiry (within buffer time)
        current_time = datetime.now()
        buffer_time = timedelta(seconds=self.token_refresh_buffer)

        if current_time >= (self.token_expires_at - buffer_time):
            logger.info(f"Token expires at {self.token_expires_at}, refreshing proactively")
            return True

        logger.debug(f"Token valid until {self.token_expires_at}")
        return False

    async def _generate_access_token(self) -> bool:
        try:
            # Try Azure Resource Manager first (preferred method)
            has_arm_config = all([
                self.subscription_id, self.resource_group_name, self.account_name,
                self.tenant_id, self.client_id, self.client_secret
            ])

            if has_arm_config:
                logger.info("Generating access token using Azure Resource Manager...")
                success = await self._generate_token_with_arm()
                if success:
                    return True
                logger.warning("Azure Resource Manager token generation failed, trying static token fallback...")

            # Fall back to static token if available
            if self.static_access_token:
                logger.info("Using static access token as fallback...")

                # Try to extract actual expiration time from JWT token
                is_valid, exp_time = self._validate_token_expiration(self.static_access_token)

                if is_valid and exp_time:
                    self.access_token = self.static_access_token
                    self.token_expires_at = exp_time
                    logger.info(f"Static access token configured")
                    # Save to persistent storage
                    self._save_token_to_storage(self.access_token, self.token_expires_at)
                    return True
                else:
                    if exp_time:
                        logger.warning(f"Static access token expired, using with default expiration")
                    else:
                        logger.warning("Static access token invalid, using with default expiration")
                    # Still use it but with default expiration as fallback
                    self.access_token = self.static_access_token
                    self.token_expires_at = datetime.now() + timedelta(hours=1)
                    logger.info(" Static access token configured")
                    # Save to persistent storage
                    self._save_token_to_storage(self.access_token, self.token_expires_at)
                    return True

            logger.error("No valid token generation method available")
            logger.error("Configure Azure Resource Manager authentication or provide a static token")
            return False

        except Exception as e:
            logger.error(f"Error generating access token: {e}")
            return False

    async def _generate_token_with_arm(self) -> bool:
        """Generate access token using Azure Resource Manager API with Azure AD authentication."""
        try:
            # First, get Azure AD access token for Resource Manager
            azure_ad_token = await self._get_azure_ad_token()
            if not azure_ad_token:
                logger.error("Failed to get Azure AD token")
                return False

            # Get required Azure configuration
            subscription_id = getattr(settings, 'AZURE_SUBSCRIPTION_ID', None)
            resource_group_name = getattr(settings, 'AZURE_RESOURCE_GROUP_NAME', None)
            account_name = getattr(settings, 'AZURE_VIDEO_INDEXER_ACCOUNT_NAME', None)

            if not all([subscription_id, resource_group_name, account_name]):
                logger.error("Missing Azure configuration for Video Indexer")
                return False

            # Azure Resource Manager endpoint for Video Indexer token generation
            token_url = (
                f"https://management.azure.com/subscriptions/{subscription_id}/"
                f"resourceGroups/{resource_group_name}/"
                f"providers/Microsoft.VideoIndexer/accounts/{account_name}/"
                f"generateAccessToken?api-version=2025-01-01"
            )

            headers = {
                "Authorization": f"Bearer {azure_ad_token}",
                "Content-Type": "application/json"
            }

            request_body = {
                "permissionType": "Contributor",
                "scope": "Account"
            }

            logger.info(f"Requesting Video Indexer access token from Azure Resource Manager")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    token_url,
                    headers=headers,
                    json=request_body
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        token = result.get("accessToken")

                        if token:
                            # Extract actual expiration time from JWT token
                            _, exp_time = self._validate_token_expiration(token)

                            self.access_token = token

                            if exp_time:
                                self.token_expires_at = exp_time
                                logger.info(f"Video Indexer access token generated successfully via Azure Resource Manager")
                                logger.info(f"   Token expires at: {self.token_expires_at}")
                            else:
                                # Fallback to default expiration if JWT parsing fails
                                self.token_expires_at = datetime.now() + timedelta(hours=1)
                                logger.info("Video Indexer access token generated successfully via Azure Resource Manager")
                                logger.info(f"   Token expires at: {self.token_expires_at} (default)")

                            # Save token to persistent storage
                            self._save_token_to_storage(self.access_token, self.token_expires_at)

                            return True
                        else:
                            logger.error(" No access token in response")
                            logger.error(f"   Response: {result}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f" Failed to generate token: {error_text}")
                        return False

        except Exception as e:
            logger.error(f" Exception during Video Indexer token generation: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def _get_azure_ad_token(self) -> Optional[str]:
        """Get Azure AD access token for Resource Manager API."""
        try:
            # Azure AD token endpoint for service principal authentication
            tenant_id = getattr(settings, 'AZURE_TENANT_ID', None)
            client_id = getattr(settings, 'AZURE_CLIENT_ID', None)
            client_secret = getattr(settings, 'AZURE_CLIENT_SECRET', None)

            if not all([tenant_id, client_id, client_secret]):
                return None

            token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"

            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }

            data = {
                "grant_type": "client_credentials",
                "client_id": client_id,
                "client_secret": client_secret,
                "scope": "https://management.azure.com/.default"
            }

            logger.info(f"Requesting Azure AD token for Resource Manager")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    token_url,
                    headers=headers,
                    data=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        access_token = result.get("access_token")

                        if access_token:
                            logger.info(" Azure AD access token obtained successfully")
                            return access_token
                        else:
                            logger.error("No access token in Azure AD response")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"Error: {error_text}")
                        return None

        except Exception as e:
            logger.error(f" Exception during Azure AD token request: {e}")
            import traceback
            traceback.print_exc()
            return None



    async def _handle_token_expired_error(self) -> bool:
        """Handle token expiration by forcing token refresh."""
        try:
            logger.warning("Handling token expiration, forcing token refresh...")

            # Clear current token to force regeneration
            self.access_token = None
            self.token_expires_at = None

            # Remove expired token from persistent storage
            self._remove_token_from_storage()

            # Generate new token
            success = await self._generate_access_token()
            if success:
                logger.info(" Token refreshed after expiration")
                return True
            else:
                logger.error(" Failed to refresh token after expiration")
                return False

        except Exception as e:
            logger.error(f" Error handling token expiration: {e}")
            return False

    async def _make_authenticated_request(self, method: str, url: str, **kwargs):
        """Make an authenticated request with automatic token refresh on 401 errors."""
        max_retries = 2

        for attempt in range(max_retries):
            try:
                headers = await self._get_headers()

                # Merge with any additional headers
                if 'headers' in kwargs:
                    headers.update(kwargs['headers'])
                kwargs['headers'] = headers

                # Set timeout for Azure Video Indexer requests (they can be slow)
                timeout = aiohttp.ClientTimeout(total=60, connect=10)

                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.request(method, url, **kwargs) as response:
                        # Check for token expiration
                        if response.status == 401:
                            logger.warning(f"Received 401 error on attempt {attempt + 1}, token may be expired")

                            # Try to refresh token and retry (but only once)
                            if attempt < max_retries - 1:
                                token_refreshed = await self._handle_token_expired_error()
                                if token_refreshed:
                                    logger.info("Retrying request with refreshed token...")
                                    continue
                                else:
                                    logger.error(" Failed to refresh token, aborting request")
                                    return {
                                        'status': response.status,
                                        'success': False,
                                        'error': 'Failed to refresh token'
                                    }

                        # Read response data while connection is still open
                        if response.status == 200:
                            try:
                                response_data = await response.json()
                                return {
                                    'status': response.status,
                                    'success': True,
                                    'data': response_data
                                }
                            except Exception as json_error:
                                logger.error(f" Failed to parse JSON response: {json_error}")
                                response_text = await response.text()
                                return {
                                    'status': response.status,
                                    'success': False,
                                    'error': f'JSON parse error: {json_error}',
                                    'raw_response': response_text
                                }
                        else:
                            response_text = await response.text()
                            return {
                                'status': response.status,
                                'success': False,
                                'error': response_text
                            }

            except aiohttp.ClientConnectionError as e:
                logger.error(f" Connection error on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    return {
                        'status': 0,
                        'success': False,
                        'error': f'Connection error after {max_retries} attempts: {e}'
                    }
                # Wait before retry
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

            except aiohttp.ClientTimeout as e:
                logger.error(f" Timeout error on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    return {
                        'status': 0,
                        'success': False,
                        'error': f'Timeout error after {max_retries} attempts: {e}'
                    }
                # Wait before retry
                await asyncio.sleep(2 ** attempt)

            except Exception as e:
                logger.error(f" Unexpected error on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    return {
                        'status': 0,
                        'success': False,
                        'error': f'Unexpected error: {e}'
                    }

        return {
            'status': 0,
            'success': False,
            'error': 'Max retries exceeded'
        }
    
    async def upload_audio_from_url(
        self,
        audio_url: str,
        filename: str,
        language: str = "en-US"
    ) -> Optional[str]:
        """
        Upload audio file from URL to Azure Video Indexer.

        Args:
            audio_url: URL of the audio file to process
            filename: Name of the audio file
            language: Language code for transcription

        Returns:
            Video ID if successful, None otherwise
        """
        if not self.configured:
            logger.error("Azure Video Indexer not configured")
            return None

        try:
            # Truncate filename to meet Azure Video Indexer's 80-character limit
            truncated_filename = self._truncate_filename(filename)

            upload_url = f"{self.base_url}/Videos"

            params = {
                "name": truncated_filename,
                "videoUrl": audio_url,
                "language": language,
                "indexingPreset": "Default",
                "streamingPreset": "NoStreaming",  # No streaming needed
                "privacy": "Private"
            }

            response = await self._make_authenticated_request(
                "POST",
                upload_url,
                params=params
            )

            if response and response.get('success'):
                result = response.get('data', {})
                video_id = result.get("id")
                logger.info(f" Audio upload initiated successfully: {video_id}")
                return video_id
            else:
                error_msg = response.get('error', 'Unknown error') if response else 'No response'
                status = response.get('status', 0) if response else 0
                logger.error(f"Audio upload failed: {status} - {error_msg}")
                return None

        except Exception as e:
            logger.error(f"Exception during Azure Video Indexer upload: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def get_video_index(self, video_id: str) -> Optional[Dict[str, Any]]:
        """
        Get video index (transcription and insights).
        
        Args:
            video_id: Video ID from upload
            
        Returns:
            Video index data if available, None otherwise
        """
        try:
            index_url = f"{self.base_url}/Videos/{video_id}/Index"
            response = await self._make_authenticated_request("GET", index_url)

            if response and response.get('success'):
                video_data = response.get('data', {})
                state = video_data.get('state', 'unknown').lower()

                if state == 'processed':
                    logger.info(f"Azure Video Indexer processing completed for video {video_id}")
                elif state == 'failed':
                    logger.error(f"Azure Video Indexer processing failed for video {video_id}")

                return video_data
            else:
                error_msg = response.get('error', 'Unknown error') if response else 'No response'
                status = response.get('status', 0) if response else 0
                logger.error(f"Failed to get video index: {status} - {error_msg}")
                return None
                        
        except Exception as e:
            logger.error(f"Error getting video index: {e}")
            return None
    
    async def wait_for_processing(
        self, 
        video_id: str, 
        max_wait_seconds: int = 300,
        poll_interval: int = 10
    ) -> bool:
        """
        Wait for video processing to complete.
        
        Args:
            video_id: Video ID to monitor
            max_wait_seconds: Maximum time to wait
            poll_interval: Seconds between status checks
            
        Returns:
            True if processing completed successfully, False otherwise
        """
        start_time = time.time()
        last_progress_log = 0
        last_state = None

        while time.time() - start_time < max_wait_seconds:
            try:
                elapsed = time.time() - start_time

                # Log progress every 60 seconds
                if elapsed - last_progress_log >= 60:
                    minutes_elapsed = int(elapsed // 60)
                    minutes_remaining = int((max_wait_seconds - elapsed) // 60)
                    logger.info(f"⏱️  Processing progress: {minutes_elapsed} minutes elapsed, ~{minutes_remaining} minutes remaining")
                    last_progress_log = elapsed

                status_url = f"{self.base_url}/Videos/{video_id}/Index"

                # Get headers properly with await
                headers = await self._get_headers()

                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        status_url,
                        headers=headers,
                        params={"includeStreamingUrls": "false"}
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            state = result.get("state", "").lower()

                            # Only log status if it changed or every 30 seconds to reduce noise
                            if last_state != state or elapsed % 30 < poll_interval:
                                logger.info(f"Video processing status: {state}")
                                last_state = state

                            if state == "processed":
                                logger.info(f" Processing completed successfully after {int(elapsed // 60)} minutes and {int(elapsed % 60)} seconds")
                                return True
                            elif state == "failed":
                                logger.error(f" Video processing failed: {video_id}")
                                return False
                        else:
                            logger.warning(f"Status check failed: {response.status}")

                await asyncio.sleep(poll_interval)

            except Exception as e:
                logger.error(f"Error checking processing status: {e}")
                await asyncio.sleep(poll_interval)
        
        logger.error(f"Processing timeout for video: {video_id}")
        return False
    
    def extract_transcript(self, video_index: Dict[str, Any]) -> str:
        """
        Extract transcript text from video index.
        
        Args:
            video_index: Video index data from API
            
        Returns:
            Formatted transcript text
        """
        try:
            transcript_parts = []
            
            # Extract transcript from videos array
            videos = video_index.get("videos", [])
            if videos:
                insights = videos[0].get("insights", {})
                transcript = insights.get("transcript", [])
                
                for item in transcript:
                    text = item.get("text", "").strip()
                    if text:
                        transcript_parts.append(text)
            
            return " ".join(transcript_parts)
            
        except Exception as e:
            logger.error(f"Error extracting transcript: {e}")
            return ""
    
    def extract_audio_metadata(self, video_index: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract audio metadata and insights.
        
        Args:
            video_index: Video index data from API
            
        Returns:
            Dictionary containing audio metadata
        """
        try:
            metadata = {
                "duration": 0,
                "language": "unknown",
                "keywords": [],
                "topics": [],
                "sentiment": "neutral"
            }
            
            videos = video_index.get("videos", [])
            if videos:
                video_data = videos[0]
                insights = video_data.get("insights", {})
                
                # Duration
                duration_seconds = video_data.get("durationInSeconds", 0)
                metadata["duration"] = duration_seconds
                
                # Language
                source_language = insights.get("sourceLanguage", "")
                if source_language:
                    metadata["language"] = source_language
                
                # Keywords
                keywords = insights.get("keywords", [])
                metadata["keywords"] = [kw.get("text", "") for kw in keywords[:10]]
                
                # Topics
                topics = insights.get("topics", [])
                metadata["topics"] = [topic.get("name", "") for topic in topics[:5]]
                
                # Sentiment (if available)
                sentiments = insights.get("sentiments", [])
                if sentiments:
                    # Get overall sentiment
                    positive_count = sum(1 for s in sentiments if s.get("sentimentType") == "Positive")
                    negative_count = sum(1 for s in sentiments if s.get("sentimentType") == "Negative")
                    
                    if positive_count > negative_count:
                        metadata["sentiment"] = "positive"
                    elif negative_count > positive_count:
                        metadata["sentiment"] = "negative"
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting audio metadata: {e}")
            return {"duration": 0, "language": "unknown", "keywords": [], "topics": [], "sentiment": "neutral"}
    
    async def transcribe_audio(
        self,
        audio_url: str,
        filename: str,
        language: str = "en-US",
        wait_for_completion: bool = False,
        max_wait_seconds: int = 300
    ) -> Dict[str, Any]:
        """
        Complete audio transcription workflow.

        Args:
            audio_url: URL of the audio file
            filename: Name of the audio file
            language: Language code for transcription
            wait_for_completion: If True, wait for processing to complete and return results
            max_wait_seconds: Maximum time to wait for completion (only used if wait_for_completion=True)

        Returns:
            Dictionary containing transcript and metadata (if wait_for_completion=True)
            or video_id and status (if wait_for_completion=False)
        """
        if not self.configured:
            return {
                "error": "Azure Video Indexer not configured",
                "message": "Please set AZURE_VIDEO_INDEXER_ACCOUNT_ID and AZURE_VIDEO_INDEXER_ACCESS_TOKEN environment variables"
            }

        try:
            logger.info(f"Starting audio transcription workflow for: {filename} (wait_for_completion={wait_for_completion})")

            # Validate URL format
            if not audio_url.startswith(('http://', 'https://')):
                logger.error(f"Invalid URL format: {audio_url} - Azure Video Indexer requires HTTP/HTTPS URLs")
                return {"error": f"Invalid URL format. Azure Video Indexer requires HTTP/HTTPS URLs, got: {audio_url}"}

            # Step 1: Upload audio to Azure Video Indexer
            logger.info(f"Uploading audio to Azure Video Indexer...")
            video_id = await self.upload_audio_from_url(audio_url, filename, language)
            if not video_id:
                error_msg = "Failed to upload audio file to Azure Video Indexer. Check credentials and blob URL accessibility."
                logger.error(f"Upload failed: {error_msg}")
                return {"error": error_msg}

            logger.info(f"Upload completed: Video ID = {video_id}")

            if not wait_for_completion:
                # Step 2a: Return immediately for async processing
                logger.info(f"Audio processing initiated in Azure Video Indexer")
                logger.info("Note: Audio processing will continue in background. Frontend will poll for completion.")
                return {
                    "video_id": video_id,
                    "status": "processing"
                }
            else:
                # Step 2b: Wait for processing to complete and return results
                logger.info(f"Waiting for audio processing to complete (max {max_wait_seconds} seconds)...")
                processing_completed = await self.wait_for_processing(video_id, max_wait_seconds)

                if not processing_completed:
                    logger.warning(f"Audio processing did not complete within {max_wait_seconds} seconds")
                    return {
                        "error": f"Audio processing timeout after {max_wait_seconds} seconds",
                        "video_id": video_id,
                        "status": "timeout"
                    }

                # Step 3: Get the processed results
                logger.info(f"Retrieving transcription results...")
                video_index = await self.get_video_index(video_id)

                if not video_index:
                    logger.error(f"Failed to retrieve video index for {video_id}")
                    return {
                        "error": "Failed to retrieve transcription results",
                        "video_id": video_id,
                        "status": "failed"
                    }

                # Step 4: Extract transcript and metadata
                transcript = self.extract_transcript(video_index)
                metadata = self.extract_audio_metadata(video_index)

                logger.info(f"Audio transcription completed successfully. Transcript length: {len(transcript)} characters")

                return {
                    "transcript": transcript,
                    "metadata": metadata,
                    "video_id": video_id,
                    "status": "completed"
                }

        except Exception as e:
            logger.error(f"Audio transcription workflow failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"error": f"Transcription failed: {str(e)}"}

    async def analyze_video(self, video_url: str, filename: str, language: str = "en-US") -> dict:
        """
        Comprehensive video analysis using Azure Video Indexer.

        This method processes video files for both audio transcription and visual insights
        including object detection, face recognition, text extraction, and scene analysis.

        Args:
            video_url: SAS-enabled URL of the video file
            filename: Name of the video file
            language: Language code for transcription (default: en-US)

        Returns:
            Dictionary containing transcript, metadata, and visual insights
        """
        try:
            logger.info(f"Starting comprehensive video analysis for: {filename}")
            logger.info(f"Video URL: {video_url}")

            # Step 1: Upload video to Azure Video Indexer
            logger.info("Step 1: Uploading video to Azure Video Indexer...")
            video_id = await self.upload_video_from_url(video_url, filename, language)
            if not video_id:
                error_msg = "Failed to upload video file to Azure Video Indexer. Check credentials and blob URL accessibility."
                logger.error(error_msg)
                return {"error": error_msg}

            logger.info(f"Step 1 completed: Video ID = {video_id}")

            # Step 2: Video processing initiated - frontend will poll for completion
            logger.info("Step 2: Video processing initiated in Azure Video Indexer")
            logger.info("Note: Video processing will continue in background. Status endpoint will poll for completion.")

            # Return video ID immediately - status endpoint will handle polling and results
            return {
                "video_id": video_id,
                "status": "processing"
            }

        except Exception as e:
            logger.error(f"Exception in video analysis workflow: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"Video analysis failed: {str(e)}"}

    async def upload_video_from_url(self, video_url: str, filename: str, language: str = "en-US") -> str:
        """Upload video from URL to Azure Video Indexer for comprehensive analysis."""
        try:
            # Truncate filename to meet Azure Video Indexer's 80-character limit
            truncated_filename = self._truncate_filename(filename)

            upload_url = f"{self.base_url}/Videos"

            params = {
                "name": truncated_filename,
                "videoUrl": video_url,
                "language": language,
                "indexingPreset": "Default",  # Full analysis including visual insights
                "streamingPreset": "NoStreaming",  # No streaming needed
                "privacy": "Private"
            }

            logger.info(f"Starting Azure Video Indexer upload for: {truncated_filename}")

            response = await self._make_authenticated_request(
                "POST",
                upload_url,
                params=params
            )

            if response and response.get('success'):
                result = response.get('data', {})
                video_id = result.get("id")
                if video_id:
                    logger.info(f"Video uploaded successfully to Azure Video Indexer. Video ID: {video_id}")
                    return video_id
                else:
                    logger.error("Video upload succeeded but no video ID returned")
                    return None
            else:
                error_msg = response.get('error', 'Unknown error') if response else 'No response'
                status = response.get('status', 0) if response else 0
                logger.error(f"Azure Video Indexer upload failed: {status} - {error_msg}")
                return None

        except Exception as e:
            logger.error(f"Exception during Azure Video Indexer video upload: {e}")
            import traceback
            traceback.print_exc()
            return None

    def extract_video_metadata(self, video_index: dict) -> dict:
        """Extract comprehensive metadata from video index results."""
        try:
            metadata = {}

            # Basic video information
            if "durationInSeconds" in video_index:
                metadata["duration"] = video_index["durationInSeconds"]

            if "sourceLanguage" in video_index:
                metadata["language"] = video_index["sourceLanguage"]

            # Video properties
            if "videos" in video_index and video_index["videos"]:
                video_info = video_index["videos"][0]
                if "insights" in video_info:
                    insights = video_info["insights"]

                    # Resolution information
                    if "resolution" in insights:
                        metadata["resolution"] = insights["resolution"]

                    # Frame rate
                    if "frameRate" in insights:
                        metadata["frameRate"] = insights["frameRate"]

            # Extract keywords from video insights
            keywords = []
            if "summarizedInsights" in video_index:
                summarized = video_index["summarizedInsights"]

                if "keywords" in summarized:
                    keywords.extend([kw.get("name", "") for kw in summarized["keywords"][:10]])

                if "topics" in summarized:
                    topics = [topic.get("name", "") for topic in summarized["topics"][:5]]
                    metadata["topics"] = topics

                if "sentiments" in summarized and summarized["sentiments"]:
                    # Get overall sentiment
                    sentiments = summarized["sentiments"]
                    if sentiments:
                        sentiment_scores = {}
                        for sentiment in sentiments:
                            sentiment_type = sentiment.get("sentimentKey", "")
                            if sentiment_type:
                                sentiment_scores[sentiment_type] = sentiment.get("seenDurationRatio", 0)

                        # Get dominant sentiment
                        if sentiment_scores:
                            dominant_sentiment = max(sentiment_scores, key=sentiment_scores.get)
                            metadata["sentiment"] = dominant_sentiment.capitalize()
                        else:
                            metadata["sentiment"] = "Neutral"
                    else:
                        metadata["sentiment"] = "Neutral"
                else:
                    metadata["sentiment"] = "Neutral"

            metadata["keywords"] = keywords

            return metadata

        except Exception as e:
            logger.error(f"Error extracting video metadata: {e}")
            return {"duration": 0, "language": "Unknown", "sentiment": "Neutral", "keywords": []}

    def extract_visual_insights(self, video_index: dict) -> dict:
        """Extract visual insights from video index results."""
        try:
            visual_insights = {
                "objects": [],
                "faces": [],
                "text": [],
                "scenes": [],
                "brands": [],
                "emotions": []
            }

            if "summarizedInsights" in video_index:
                summarized = video_index["summarizedInsights"]

                # Extract detected objects/labels
                if "labels" in summarized:
                    visual_insights["objects"] = [
                        label.get("name", "") for label in summarized["labels"][:20]
                        if label.get("name")
                    ]

                # Extract detected faces
                if "faces" in summarized:
                    faces_info = []
                    for face in summarized["faces"][:10]:
                        face_info = {
                            "name": face.get("name", "Unknown"),
                            "confidence": face.get("confidence", 0),
                            "description": face.get("description", "")
                        }
                        faces_info.append(face_info)
                    visual_insights["faces"] = faces_info

                # Extract OCR text
                if "keywords" in summarized:
                    # Filter keywords that might be OCR text
                    text_keywords = [
                        kw.get("name", "") for kw in summarized["keywords"]
                        if kw.get("name") and len(kw.get("name", "")) > 2
                    ][:15]
                    visual_insights["text"] = text_keywords

                # Extract brands
                if "brands" in summarized:
                    visual_insights["brands"] = [
                        brand.get("name", "") for brand in summarized["brands"][:10]
                        if brand.get("name")
                    ]

                # Extract emotions
                if "emotions" in summarized:
                    emotions_info = []
                    for emotion in summarized["emotions"][:5]:
                        emotion_info = {
                            "type": emotion.get("type", ""),
                            "confidence": emotion.get("seenDurationRatio", 0)
                        }
                        emotions_info.append(emotion_info)
                    visual_insights["emotions"] = emotions_info

            # Extract scenes from video timeline
            if "videos" in video_index and video_index["videos"]:
                video_info = video_index["videos"][0]
                if "insights" in video_info and "scenes" in video_info["insights"]:
                    scenes_info = []
                    for scene in video_info["insights"]["scenes"][:10]:
                        scene_info = {
                            "start": scene.get("start", ""),
                            "end": scene.get("end", ""),
                            "keyFrame": scene.get("keyFrame", {}).get("id", "")
                        }
                        scenes_info.append(scene_info)
                    visual_insights["scenes"] = scenes_info

            return visual_insights

        except Exception as e:
            logger.error(f"Error extracting visual insights: {e}")
            return {
                "objects": [],
                "faces": [],
                "text": [],
                "scenes": [],
                "brands": [],
                "emotions": []
            }

# Global instance
azure_video_indexer_service = AzureVideoIndexerService()
