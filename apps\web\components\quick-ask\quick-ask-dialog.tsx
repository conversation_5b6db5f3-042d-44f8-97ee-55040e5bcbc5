"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Loader2,
  ArrowUpIcon,
  Globe,
  ExternalLink,
  FileText,
  Sparkles,
  MessageSquare,
  Trash2,
  Plus,
  Zap,
  Search,
  Server,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { createChat } from "@/services/src/chat";
import { createMessage } from "@/services/src/message";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ReactMarkdown from "react-markdown";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Source } from "../wrapper-screens/chat/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import { getLLMScopeCapabilities } from "@/types/llm-scope";
import { useLLMScope } from "@/lib/llm-scope-context";
import { Input } from "../ui/input";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  copilotKitChatService,
  CopilotKitChatQuery,
} from "@/services/copilotkit-chat";
import { getCookie } from "@/utils/cookies";

// Utility function to strip markdown formatting from table cell content
const stripMarkdownFormatting = (text: string): string => {
  return (
    text
      // Remove bold formatting (**text** or __text__)
      .replace(/\*\*(.*?)\*\*/g, "$1")
      .replace(/__(.*?)__/g, "$1")
      // Remove italic formatting (*text* or _text_) - but be careful not to break other formatting
      .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, "$1")
      .replace(/(?<!_)_([^_]+)_(?!_)/g, "$1")
      // Remove inline code formatting (`text`)
      .replace(/`([^`]+)`/g, "$1")
      // Remove strikethrough formatting (~~text~~)
      .replace(/~~(.*?)~~/g, "$1")
  );
};

// Custom table parser for markdown tables
const parseMarkdownTable = (content: string) => {
  const lines = content.split("\n");
  const tables: Array<{
    startIndex: number;
    endIndex: number;
    headers: string[];
    rows: string[][];
    alignments: string[];
  }> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes("|") && line.split("|").length > 2) {
      // Potential table start
      const nextLine = lines[i + 1]?.trim();
      if (nextLine && nextLine.includes("|") && nextLine.includes("-")) {
        // This is a table header with separator
        const headers = line
          .split("|")
          .map((h) => stripMarkdownFormatting(h.trim()))
          .filter((h) => h);
        const separatorCells = nextLine
          .split("|")
          .map((s) => s.trim())
          .filter((s) => s);
        const alignments = separatorCells.map((cell) => {
          if (cell.startsWith(":") && cell.endsWith(":")) return "center";
          if (cell.endsWith(":")) return "right";
          return "left";
        });

        const rows: string[][] = [];
        let j = i + 2;

        // Parse table rows
        while (j < lines.length && lines[j].trim().includes("|")) {
          const rowCells = lines[j]
            .split("|")
            .map((c) => stripMarkdownFormatting(c.trim()))
            .filter((c) => c);
          if (rowCells.length > 0) {
            rows.push(rowCells);
          }
          j++;
        }

        if (rows.length > 0) {
          tables.push({
            startIndex: i,
            endIndex: j - 1,
            headers,
            rows,
            alignments,
          });
        }

        i = j - 1; // Skip processed lines
      }
    }
  }

  return tables;
};

interface Message {
  id?: string;
  role: "user" | "assistant";
  content: string;
  sources?: Source[];
  timestamp: Date;
  metadata?: any;
  images?: any[];
}

interface QuickAskDialogProps {
  userId: string;
  userName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QuickAskDialog({
  userId,
  userName,
  open,
  onOpenChange,
}: QuickAskDialogProps) {
  const router = useRouter();
  const { t, language } = useLanguage();
  const tenantId = getCookie("currentOrganizationId") ?? "";

  const [queryStatus, setQueryStatus] = useState("");

  // Function to render content with tables
  const renderContentWithTables = (content: string) => {
    const tables = parseMarkdownTable(content);

    if (tables.length === 0) {
      // No tables found, render normally
      return <ReactMarkdown className="markdown">{content}</ReactMarkdown>;
    }

    // Split content and render with tables
    const lines = content.split("\n");
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;

    tables.forEach((table, tableIndex) => {
      // Add content before table
      if (table.startIndex > lastIndex) {
        const beforeTableContent = lines
          .slice(lastIndex, table.startIndex)
          .join("\n");
        if (beforeTableContent.trim()) {
          elements.push(
            <ReactMarkdown key={`before-${tableIndex}`} className="markdown">
              {beforeTableContent}
            </ReactMarkdown>
          );
        }
      }

      // Add table
      elements.push(
        <div key={`table-${tableIndex}`} className="my-4 overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {table.headers.map((header, i) => (
                  <TableHead
                    key={i}
                    style={{ textAlign: table.alignments[i] as any }}
                  >
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell
                      key={cellIndex}
                      style={{ textAlign: table.alignments[cellIndex] as any }}
                    >
                      {cell}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      );

      lastIndex = table.endIndex + 1;
    });

    // Add remaining content after last table
    if (lastIndex < lines.length) {
      const afterTableContent = lines.slice(lastIndex).join("\n");
      if (afterTableContent.trim()) {
        elements.push(
          <ReactMarkdown key="after-last" className="markdown">
            {afterTableContent}
          </ReactMarkdown>
        );
      }
    }

    return <>{elements}</>;
  };
  const [input, setInput] = useState("");
  const [conversationMessages, setConversationMessages] = useState<Message[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [includeWebResults, setIncludeWebResults] = useState(false);

  // LLM Scope from context (real-time synchronized)
  const { llmScope, llmScopeLoading } = useLLMScope();
  const llmCapabilities = getLLMScopeCapabilities(llmScope);

  // Search mode state
  type SearchMode = "mcp" | "web" | "internal" | "hybrid" | "";
  const [selectedSearchMode, setSelectedSearchMode] =
    useState<SearchMode>("internal");

  // Track selected modes for hybrid logic (similar to ask-ai)
  let initialSelectedModes: Set<"internal" | "web"> = new Set();
  if (selectedSearchMode === "hybrid") {
    initialSelectedModes = new Set(["internal", "web"]);
  } else if (selectedSearchMode === "internal") {
    initialSelectedModes = new Set(["internal"]);
  } else if (selectedSearchMode === "web") {
    initialSelectedModes = new Set(["web"]);
  }

  const [selectedModes, setSelectedModes] =
    React.useState<Set<"internal" | "web">>(initialSelectedModes);

  // Update search mode based on selected modes (but don't override MCP mode)
  React.useEffect(() => {
    // Don't override MCP mode when it's selected
    if (selectedSearchMode === "mcp") {
      return;
    }

    if (selectedModes.size === 0) {
      setSelectedSearchMode("");
      setIncludeWebResults(false);
    } else if (selectedModes.size === 2) {
      setSelectedSearchMode("hybrid");
      setIncludeWebResults(true);
    } else if (selectedModes.has("internal")) {
      setSelectedSearchMode("internal");
      setIncludeWebResults(false);
    } else if (selectedModes.has("web")) {
      setSelectedSearchMode("web");
      setIncludeWebResults(true);
    }
  }, [
    selectedModes,
    selectedSearchMode,
    setSelectedSearchMode,
    setIncludeWebResults,
  ]);

  // Handle search mode changes
  const handleSearchModeChange = (mode: "internal" | "web" | "mcp") => {
    if (mode === "mcp") {
      if (selectedSearchMode === "mcp") {
        setSelectedSearchMode("internal");
        setSelectedModes(new Set(["internal"]));
      } else {
        setSelectedSearchMode("mcp");
        setSelectedModes(new Set());
      }
      return;
    }

    // Handle internal and web mode toggles
    const newSelectedModes = new Set(selectedModes);
    if (newSelectedModes.has(mode)) {
      newSelectedModes.delete(mode);
    } else {
      newSelectedModes.add(mode);
    }
    setSelectedModes(newSelectedModes);
  };
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // LLM scope is now managed by the context provider
  // No need for manual loading - it's handled automatically with real-time sync

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        setInput("");
        setConversationMessages([]);
        setIsLoading(false);
      }, 300);
    }
  }, [open]);

  // Auto-scroll disabled - users have full manual control over scroll position
  // useEffect(() => {
  //   if (messagesEndRef.current) {
  //     messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
  //   }
  // }, [conversationMessages]);

  // Save conversation to local storage when it changes
  useEffect(() => {
    if (conversationMessages.length > 0) {
      try {
        localStorage.setItem(
          "quick_ask_conversation",
          JSON.stringify(conversationMessages)
        );
      } catch (error) {
        console.error("Error saving conversation to local storage:", error);
      }
    }
  }, [conversationMessages]);

  // Load conversation from local storage on initial load
  useEffect(() => {
    try {
      const savedConversation = localStorage.getItem("quick_ask_conversation");
      if (savedConversation && JSON.parse(savedConversation).length > 0) {
        setConversationMessages(JSON.parse(savedConversation));
      }
    } catch (error) {
      console.error("Error loading conversation from local storage:", error);
    }
  }, []);

  // CopilotKit chat state
  const [status, setStatus] = useState<"idle" | "streaming">("idle");

  // Update loading state based on status
  useEffect(() => {
    if (status === "streaming") {
      setIsLoading(true);
    } else if (status === "idle") {
      setIsLoading(false);
    }
    // Don't set isLoading to false during streaming, as we want to show the loading indicator
    // in the send button, but we'll show the streaming content in the message
  }, [status]);

  const handleChange = (value: string) => {
    setInput(value);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (input.trim() === "" || isLoading || status === "streaming") return;

    // Store the current input before clearing it
    const currentInput = input.trim();
    setInput("");

    // Add user message to conversation
    const userMessage: Message = {
      role: "user",
      content: currentInput,
      timestamp: new Date(),
    };

    // Update conversation with user message
    setConversationMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);
    setStatus("streaming");

    // Small delay to ensure user message is rendered before starting API call
    await new Promise((resolve) => setTimeout(resolve, 50));

    try {
      // Get conversation context for better continuity
      const getConversationContext = () => {
        // Get the last few messages for context (max 2 exchanges)
        const recentMessages = conversationMessages.slice(-8); // Last 4 messages (2 user + 2 assistant)

        if (recentMessages.length === 0) return undefined;

        // Format as conversation history
        const conversationHistory = recentMessages
          .map(
            (msg) =>
              `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`
          )
          .join("\n");

        return conversationHistory;
      };

      const previousMessage = getConversationContext();

      // Prepare CopilotKit query
      const query: CopilotKitChatQuery = {
        question: currentInput,
        stream: true,
        search_mode:
          selectedSearchMode === "mcp"
            ? "mcp" // Keep MCP mode instead of converting to internal
            : selectedSearchMode === ""
              ? ""
              : (selectedSearchMode as "internal" | "web" | "hybrid"),
        include_web_results:
          selectedSearchMode === "hybrid" || selectedSearchMode === "web"
            ? true
            : false,
        previous_message: previousMessage,
        selected_mcp_servers: selectedSearchMode === "mcp" ? [] : undefined, // Auto-fetch all MCP servers when mode is mcp
        language: language, // Pass user's preferred language
      };

      // Create assistant message placeholder
      const assistantMessage: Message = {
        id: `assistant_${Date.now()}`,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        sources: [],
        metadata: {},
      };

      setConversationMessages((prev) => [...prev, assistantMessage]);

      // Stream the response using CopilotKit
      let fullContent = "";
      let currentSources: any[] = [];

      for await (const chunk of copilotKitChatService.streamChat(
        query,
        userId,
        tenantId,
        userName
      )) {
        if (chunk.error) {
          throw new Error(chunk.error);
        }
        if (chunk?.message) {
          setQueryStatus(chunk?.message);
        }

        // Handle different chunk types
        if (chunk.sources) {
          currentSources = chunk.sources;
        }

        if (chunk.answer_chunk) {
          fullContent += chunk.answer_chunk;
        } else if (chunk.answer) {
          fullContent = chunk.answer;
        }

        // Update the assistant message with current content
        if (fullContent || currentSources.length > 0) {
          setConversationMessages((prev) => {
            const updated = [...prev];
            const lastMessage = updated[updated.length - 1];
            if (lastMessage && lastMessage.role === "assistant") {
              updated[updated.length - 1] = {
                ...lastMessage,
                content: fullContent,
                sources: currentSources,
              };
            }
            return updated;
          });
        }

        if (chunk.done) {
          // Final update
          setConversationMessages((prev) => {
            const updated = [...prev];
            const lastMessage = updated[updated.length - 1];
            if (lastMessage && lastMessage.role === "assistant") {
              updated[updated.length - 1] = {
                ...lastMessage,
                content: chunk.answer || fullContent,
                sources: currentSources,
              };
            }
            return updated;
          });

          setStatus("idle");
          break;
        }
      }
    } catch (error) {
      console.error("Error in CopilotKit chat:", error);
      // Show error message to user
      setConversationMessages((prev) => [
        ...prev,
        {
          id: "assistant_error",
          role: "assistant",
          content:
            "Sorry, I encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ]);
    } finally {
      setIsLoading(false);
      setStatus("idle");
    }
  };

  // Function to clear conversation history
  const handleClearConversation = () => {
    setConversationMessages([]);
    localStorage.removeItem("quick_ask_conversation");
  };

  const handleOpenAsChat = async () => {
    if (conversationMessages.length === 0) return;

    try {
      // Get the first user message for the title
      const firstUserMessage = conversationMessages.find(
        (msg) => msg.role === "user"
      );
      const title = firstUserMessage
        ? firstUserMessage.content.substring(0, 50) +
          (firstUserMessage.content.length > 50 ? "..." : "")
        : "Quick Ask Conversation";

      // Create a new chat
      const chatResponse = await createChat({
        title,
        tenantId,
      });

      if (chatResponse?.chat?.id) {
        // Add all messages to the chat with proper metadata including images
        for (const message of conversationMessages) {
          await createMessage({
            chatId: chatResponse.chat.id,
            userId,
            content: message.content,
            role: message.role,
            metadata: {
              ...(message.role === "user" ? { includeWebResults } : {}),
              ...(message.metadata || {}),
              // Preserve any images that might be in the message
              images: message.images || message.metadata?.images || undefined,
              hasImages: !!(message.images || message.metadata?.images),
            },
            sources: message.sources || [],
          });
        }

        // Navigate to the new chat
        router.push(`/ask-ai/${chatResponse.chat.id}`);
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error creating chat:", error);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        style={{
          zIndex: 10000,
        }}
        side="right"
        className="w-full sm:max-w-md p-0 border-l border-border/30 shadow-xl overflow-auto"
      >
        <div className="flex flex-col h-full">
          {/* Header with gradient */}
          <SheetHeader className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent px-4 py-3 border-b border-border/30">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <SheetTitle className="text-lg font-semibold text-foreground">
                {t("quickAsk.buttonLabel") || "Quick Ask"}
              </SheetTitle>
            </div>
          </SheetHeader>

          {/* Messages area */}
          <ScrollArea className="flex-1 p-4 bg-background/80 w-full">
            <AnimatePresence>
              {conversationMessages.length === 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex flex-col items-center justify-center py-10 text-center"
                >
                  <MessageSquare className="h-12 w-12 text-muted-foreground/30 mb-4" />
                  <p className="text-muted-foreground font-medium">
                    {t("quickAsk.emptyState") ||
                      "Ask a question to get a quick answer"}
                  </p>
                  <p className="text-xs text-muted-foreground/70 mt-2 max-w-md">
                    {t("quickAsk.emptyStateDescription") ||
                      "Get instant answers without saving the conversation to your workspace"}
                  </p>
                </motion.div>
              )}

              {/* Conversation messages */}
              <div className="space-y-4">
                {conversationMessages.map((message, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex ${
                      message.role === "user"
                        ? "justify-end w-[90%] mr-auto"
                        : "justify-start"
                    }`}
                  >
                    <div
                      className={`rounded-2xl px-4 py-2.5 text-sm shadow-sm quick-ask-message ${
                        message.role === "user"
                          ? "bg-primary/10 rounded-tr-sm max-w-[85%]"
                          : "bg-card rounded-tl-sm max-w-[90%]"
                      }`}
                    >
                      {message.role === "assistant" ? (
                        <div className="prose prose-sm dark:prose-invert max-w-none break-words overflow-wrap-anywhere">
                          {renderContentWithTables(message.content)}
                          {status === "streaming" &&
                            index === conversationMessages.length - 1 && (
                              <span className="inline-block w-full h-4 ml-1 animate-pulse rounded-sm">
                                {queryStatus && (
                                  <p className="text-sm text-gray-700 leading-relaxed animate-pulse select-text cursor-text">
                                    {queryStatus}
                                  </p>
                                )}
                              </span>
                            )}
                        </div>
                      ) : (
                        <div className="">{message.content}</div>
                      )}

                      {/* Sources for assistant messages */}
                      {message.role === "assistant" &&
                        message.sources &&
                        message.sources.length > 0 && (
                          <div className="mt-4 pt-3 border-t border-border/30">
                            <Accordion
                              type="single"
                              collapsible
                              className="w-full border-none"
                            >
                              <AccordionItem
                                value={`citations-${index}`}
                                className="border-none"
                              >
                                <AccordionTrigger className="py-1.5 px-2 hover:no-underline hover:bg-muted/50 rounded-lg group transition-colors">
                                  <div className="flex justify-between items-center gap-2 w-full">
                                    <div className="flex items-center gap-2">
                                      <div className="bg-primary/10 p-1.5 rounded-full">
                                        <FileText className="h-3.5 w-3.5 text-primary" />
                                      </div>
                                      <div className="text-xs font-medium text-primary">
                                        {t("chat.sources") || "Sources"}
                                        <Badge
                                          variant="outline"
                                          className="ml-2 bg-primary/5 text-[10px] font-normal py-0 h-4"
                                        >
                                          {message.sources.length}
                                        </Badge>
                                      </div>
                                    </div>
                                    <div className="text-[10px] text-muted-foreground mr-1 opacity-70 group-hover:opacity-100">
                                      {t("chat.citationsAccordion") ||
                                        "Click to view sources"}
                                    </div>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent className="pt-2 pb-1 animate-in fade-in-50 duration-300">
                                  <div className="grid grid-cols-1 gap-2">
                                    {message.sources.map((source, idx) => (
                                      <motion.div
                                        key={idx}
                                        onClick={() => {
                                          // Open the web link in a new tab
                                          window.open(
                                            source.metadata?.link ?? "#",
                                            "_blank",
                                            "noopener,noreferrer"
                                          );
                                        }}
                                        initial={{ opacity: 0, y: 5 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: idx * 0.05 }}
                                        className="group relative p-3 rounded-lg border border-border/50
                                              bg-muted/30 hover:bg-muted/50
                                              transition-all duration-200 cursor-pointer hover:shadow-md"
                                      >
                                        <div className="flex items-start gap-3">
                                          <div className="mt-0.5 flex-shrink-0 bg-primary/10 p-1.5 rounded-full">
                                            <FileText className="h-3.5 w-3.5 text-primary/80" />
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                              <div className="text-xs font-medium truncate">
                                                {source.metadata?.fileName ||
                                                  "Document"}
                                              </div>
                                            </div>
                                            <div className="mt-1 text-[11px] text-muted-foreground line-clamp-2">
                                              {source.metadata?.relevantText
                                                ? source.metadata.relevantText.substring(
                                                    0,
                                                    100
                                                  ) + "..."
                                                : source.content.substring(
                                                    0,
                                                    100
                                                  ) + "..."}
                                            </div>
                                          </div>
                                        </div>
                                      </motion.div>
                                    ))}
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        )}
                    </div>
                  </motion.div>
                ))}

                {/* Loading indicator - only show when submitted but not yet streaming */}
                {isLoading &&
                  status === "streaming" &&
                  conversationMessages.length > 0 &&
                  conversationMessages[conversationMessages.length - 1].role ===
                    "user" && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center gap-3 p-4 bg-muted/30 rounded-2xl rounded-tl-sm max-w-[90%]"
                    >
                      <div className="bg-primary/20 p-2 rounded-full">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          {t("quickAsk.thinking") || "Thinking..."}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {t("quickAsk.searching") ||
                            "Searching for the best answer"}
                        </p>
                      </div>
                    </motion.div>
                  )}

                {/* Invisible element for scrolling to bottom */}
                <div ref={messagesEndRef} />
              </div>
            </AnimatePresence>
          </ScrollArea>
          {/* Input area with enhanced styling */}
          <div className="border-t border-border/30 p-4 bg-background/90">
            <div className="space-y-3">
              {/* Search input */}
              <form onSubmit={handleSubmit} className="flex items-center gap-2">
                <div className="relative flex-1">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <Search className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <Input
                    ref={inputRef}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSubmit(e as any);
                      }
                    }}
                    value={input}
                    onChange={(e) => handleChange(e.target.value)}
                    placeholder={
                      t("quickAsk.placeholder") ||
                      "Ask a quick question..." ||
                      ""
                    }
                    disabled={isLoading || status === "streaming"}
                    className="h-10 pl-9 pr-4 rounded-full bg-muted/50 border-none focus-visible:ring-1 focus-visible:ring-primary/30 transition-all"
                  />
                </div>

                {input.trim() !== "" && !isLoading && status !== "streaming" ? (
                  <Button
                    type="submit"
                    size="icon"
                    variant="ghost"
                    className="h-10 w-10 rounded-full hover:bg-primary/10 transition-colors flex-shrink-0"
                  >
                    <ArrowUpIcon className="h-4 w-4 text-primary" />
                  </Button>
                ) : (
                  (isLoading || status === "streaming") && (
                    <div className="h-10 w-10 flex items-center justify-center flex-shrink-0">
                      <Loader2 className="h-4 w-4 animate-spin text-primary" />
                    </div>
                  )
                )}
              </form>

              {/* Action buttons below input */}
              <div className="flex items-center gap-2">
                {/* Add Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 rounded-full hover:bg-muted/50"
                        disabled={isLoading || status === "streaming"}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>{t("quickAsk.tooltips.addAttachments")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Web Search Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="sm"
                        variant={selectedModes.has("web") ? "default" : "ghost"}
                        className="h-8 px-3 rounded-full text-sm"
                        disabled={
                          !llmCapabilities.canAccessWebSearch ||
                          isLoading ||
                          status === "streaming" ||
                          llmScopeLoading
                        }
                        onClick={() => handleSearchModeChange("web")}
                      >
                        <Globe className="h-4 w-4 mr-2" />
                        {t("quickAsk.webSearch") || "Web"}
                        {selectedSearchMode === "hybrid" && (
                          <span className="ml-1 text-xs opacity-75">+</span>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>
                        {!llmCapabilities.canAccessWebSearch
                          ? t("quickAsk.tooltips.webSearchDisabled")
                          : llmScopeLoading
                            ? t("quickAsk.tooltips.loadingSettings")
                            : selectedSearchMode === "hybrid"
                              ? t("quickAsk.tooltips.webSearchHybrid")
                              : t("quickAsk.tooltips.webSearch")}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Internal Search Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="sm"
                        variant={
                          selectedModes.has("internal") ? "default" : "ghost"
                        }
                        className="h-8 px-3 rounded-full text-sm"
                        disabled={
                          !llmCapabilities.canAccessInternalDocs ||
                          isLoading ||
                          status === "streaming" ||
                          llmScopeLoading
                        }
                        onClick={() => handleSearchModeChange("internal")}
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        {t("quickAsk.internalSearch") || "Internal"}
                        {selectedSearchMode === "hybrid" && (
                          <span className="ml-1 text-xs opacity-75">+</span>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>
                        {!llmCapabilities.canAccessInternalDocs
                          ? t("quickAsk.tooltips.internalSearchDisabled")
                          : llmScopeLoading
                            ? t("quickAsk.tooltips.loadingSettings")
                            : selectedSearchMode === "hybrid"
                              ? t("quickAsk.tooltips.internalSearchHybrid")
                              : t("quickAsk.tooltips.internalSearch")}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {/* mcp Search Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="sm"
                        variant={
                          selectedSearchMode === "mcp" ? "default" : "ghost"
                        }
                        className="h-8 px-3 rounded-full text-sm"
                        disabled={
                          !llmCapabilities.canAccessMCPServers ||
                          isLoading ||
                          status === "streaming" ||
                          llmScopeLoading
                        }
                        onClick={() => handleSearchModeChange("mcp")}
                      >
                        <Server className="h-4 w-4 mr-2" />
                        {t("quickAsk.mcpSearch") || "MCP"}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>
                        {!llmCapabilities.canAccessMCPServers
                          ? t("quickAsk.tooltips.mcpSearchDisabled")
                          : llmScopeLoading
                            ? t("quickAsk.tooltips.loadingSettings")
                            : t("quickAsk.tooltips.mcpSearch")}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {conversationMessages.length > 0 && (
              <div className="mt-3 flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1.5 rounded-full hover:bg-destructive/10 hover:text-destructive transition-colors"
                  onClick={handleClearConversation}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                  {t("quickAsk.clearConversation") || "Clear Conversation"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1.5 rounded-full hover:bg-primary/10 hover:text-primary transition-colors border-primary/20"
                  onClick={handleOpenAsChat}
                >
                  <ExternalLink className="h-3.5 w-3.5" />
                  {t("quickAsk.openAsChat") || "Open as Chat"}
                </Button>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
