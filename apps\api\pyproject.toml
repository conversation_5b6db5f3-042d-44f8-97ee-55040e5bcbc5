[tool.poetry]
name = "api"
version = "0.1.0"
description = ""
authors = ["increscotech.com"]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
fastapi = "^0.109.2"
uvicorn = "^0.27.1"
email-validator = "^2.1.0"
pydantic-settings = "^2.2.1"
python-dotenv = "^1.0.1"
prisma = "^0.15.0"
openai = "^1.61.1"
langchain = "^0.3.18"
fastapi-cors = "^0.0.6"
pypdf = "^5.3.0"
tiktoken = "^0.8.0"
unstructured = "^0.16.20"
langchain-pinecone = "^0.2.2"
pinecone = "^5.4.2"
python-multipart = "^0.0.20"
langchain-community = "^0.3.17"
langchain-openai = "0.3.9"
lancedb = "^0.19.0"
phidata = "^2.7.10"
tantivy = "^0.22.0"
sqlalchemy = "^2.0.38"
cohere = "^5.13.12"
pandas = "^2.2.3"
chromadb = "^0.6.3"
phi = "^0.6.7"
langchain-deepseek = "^0.1.2"
motor = "^3.7.0"

pyjwt = "^2.10.1"
markitdown = { extras = ["all"], version = "^0.1.1" }
cryptography = "^42.0.0"
python-docx = "^1.1.0"
# CopilotKit and Agent dependencies
langgraph = "^0.2.50"
langchain-core = "^0.3.18"
aiofiles = "^24.1.0"
mcp = "^1.10.1"
sseclient-py = "^1.8.0"

[tool.poetry.group.dev.dependencies]
isort = "^5.10.1"
black = "^22.6.0"
pytest = "^8.0.1"
pylint-pydantic = "^0.3.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
