import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { workspaceChatService } from "@/services/workspace-chat";
import { getFileExtension } from "@/lib/utils/file-utils";
import { BlobServiceClient } from "@azure/storage-blob";
import { Client } from "@microsoft/microsoft-graph-client";
import { getServerSession } from "next-auth";
import jwt from "jsonwebtoken";
import { authOptions } from "@/lib/next-auth";
import axios from "axios";
import { SupportedExtensions } from "@/lib/constant/supported-extensions";
interface SyncOptions {
  folderId: string;
  tenantId: string;
  userId: string;
  workspaceSlug?: string;
  pageId?: string;
  workspaceId?: string;
  slug: string;
  recursive: boolean;
  checkSubFiles: boolean;
  parentFolderId?: string; // Added to track parent folder ID
  siteId?: string; // SharePoint site ID
  driveId?: string; // SharePoint drive ID
}

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

// Helper function to determine document type based on file extension
const getDocumentType = (filename: string): string => {
  const extension = getFileExtension(filename);

  switch (extension) {
    case 'md':
    case 'mdx':
    case 'markdown':
      return 'markitdown'; // Use markitdown for better markdown processing
    case 'pdf':
      return 'pdf';
    case 'txt':
      return 'text';
    case 'csv':
      return 'csv';
    case 'docx':
    case 'doc':
      return 'word';
    case 'xlsx':
    case 'xls':
      return 'excel';
    case 'pptx':
    case 'ppt':
      return 'powerpoint';
    default:
      return 'auto'; // Let the backend auto-detect
  }
};
// Helper function to get authenticated Graph client
const getAuthenticatedClient = async (tenantId: string, userId: string) => {
  const integration = await db.integration.findFirst({
    where: { tenantId, platform: "OUTLOOK", userId },
  });

  if (!integration) {
    throw new Error("Outlook integration not found");
  }
  const response = await axios.get(
    `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`
  );

  const client = Client.init({
    authProvider: (done) =>
      done(null, response?.data?.integration?.accessToken),
  });

  return client;
};
/**
 * Lists the contents of a OneDrive or SharePoint folder
 */
async function listFolderContents(
  client: Client,
  folderId: string,
  options?: { siteId?: string; driveId?: string }
) {
  try {
    let endpoint;

    if (options?.siteId && options?.driveId) {
      // SharePoint site folder
      endpoint =
        folderId === "root"
          ? `/sites/${options.siteId}/drives/${options.driveId}/root/children`
          : `/sites/${options.siteId}/drives/${options.driveId}/items/${folderId}/children`;
    } else {
      // Personal OneDrive folder (legacy support)
      endpoint =
        folderId === "root"
          ? "/me/drive/root/children"
          : `/me/drive/items/${folderId}/children`;
    }

    const response = await client
      .api(endpoint)
      .select(
        "id,name,folder,file,size,lastModifiedDateTime,@microsoft.graph.downloadUrl,parentReference"
      )
      .get();

    return response.value;
  } catch (error) {
    console.error("Error listing folder contents:", error);
    throw error;
  }
}

/**
 * Downloads file content from OneDrive or SharePoint
 */
async function getFileContent(
  client: Client,
  fileId: string,
  downloadUrl: string,
  options?: { siteId?: string; driveId?: string }
) {
  try {
    if (downloadUrl) {
      // Use the download URL if available
      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } else {
      // Fall back to the Graph API
      let endpoint;

      if (options?.siteId && options?.driveId) {
        // SharePoint site file
        endpoint = `/sites/${options.siteId}/drives/${options.driveId}/items/${fileId}/content`;
      } else {
        // Personal OneDrive file (legacy support)
        endpoint = `/me/drive/items/${fileId}/content`;
      }

      const response = await client.api(endpoint).get();
      return await new Response(response).arrayBuffer();
    }
  } catch (error) {
    console.error(`Error getting content for file ${fileId}:`, error);
    return null;
  }
}

/**
 * Uploads file content to Azure Blob Storage
 */
async function uploadToBlobStorage(
  fileBuffer: ArrayBuffer,
  fileName: string,
  tenantSlug: string,
  workspaceSlug: string
): Promise<string> {
  try {
    // Create blob service client
    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path with tenant and workspace structure
    const blobName = `${tenantSlug}/${workspaceSlug}/${Date.now()}-${fileName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Upload file
    await blockBlobClient.uploadData(fileBuffer);

    // Return the blob URL
    return blockBlobClient.url;
  } catch (error) {
    console.error("Error uploading to blob storage:", error);
    throw new Error("Failed to upload file to Azure Blob Storage");
  }
}

/**
 * Syncs a OneDrive or SharePoint folder with the local database
 */
async function syncFolder(client: Client, options: SyncOptions, token: string) {
  const {
    folderId,
    tenantId,
    userId,
    slug,
    recursive,
    pageId,
    parentFolderId,
    siteId,
    driveId,
  } = options;
  let syncedFiles = 0;

  try {
    // Get folder contents from OneDrive or SharePoint
    const driveContents = await listFolderContents(client, folderId, {
      siteId,
      driveId,
    });
    const workspace = await db.workspace.findFirst({
      where: { slug: decodeURI(options?.workspaceSlug || "") },
    });
    const workspaceId = workspace?.id;
    let localPage: any = null;
    // Get or create the local folder
    let localFolder = await db.folder.findFirst({
      where: { oneDriveFolderId: folderId, workspaceId },
      include: { files: true },
    });
    if (!localFolder) {
      localPage = await db.page.findFirst({
        where: { oneDriveFolderId: folderId, workspaceId },
        include: { files: true },
      });
    }

    // Create local folder if it doesn't exist
    if (!localFolder && !localPage) {
      // First, check if parent folder exists in our database
      let parentFolder: any = null;
      if (parentFolderId) {
        parentFolder = await db.folder.findFirst({
          where: { oneDriveFolderId: parentFolderId, workspaceId },
        });
      }

      // Get folder info from OneDrive or SharePoint
      let folderName = "Folder";
      if (folderId === "root") {
        folderName = "Root";
      } else {
        let endpoint;

        if (siteId && driveId) {
          // SharePoint site folder
          endpoint = `/sites/${siteId}/drives/${driveId}/items/${folderId}`;
        } else {
          // Personal OneDrive folder (legacy support)
          endpoint = `/me/drive/items/${folderId}`;
        }

        const folderInfo = await client.api(endpoint).select("name").get();
        folderName = folderInfo.name;
      }

      // Create the folder without setting parentId directly
      localFolder = await db.folder.create({
        data: {
          name: folderName,
          workspaceId,
          pageId,
          oneDriveFolderId: folderId,
          // Don't set parentId here as it's causing the MongoDB ObjectID error
        },
      });

      // If parent folder exists, create the relationship using folderHierarchy
      if (parentFolder) {
        try {
          // Create folder hierarchy relationship
          await db.folderHierarchy.create({
            data: {
              parentId: parentFolder.id,
              childId: localFolder.id,
              pageId,
            },
          });
        } catch (hierarchyError) {
          console.error(
            `Error creating folder hierarchy relationship:`,
            hierarchyError
          );
          // Continue even if hierarchy creation fails
        }
      }
    }

    // Process each item in the folder
    for (const item of driveContents) {
      if (item.folder && recursive) {
        // Recursively sync subfolders with parent reference
        const subFolderResult = await syncFolder(
          client,
          {
            ...options,
            folderId: item.id,
            parentFolderId: folderId, // Pass current folder as parent
          },
          token
        );

        syncedFiles += subFolderResult.syncedFiles;
      } else if (item.file) {
        // Handle files
        // Check if file exists locally by OneDrive ID
        const localFile = await db.file.findFirst({
          where: { oneDriveFileId: item.id, workspaceId },
        });

        if (!localFile) {
          // Determine file extension from filename
          let extension = item.name.split(".").pop() || "";

          // Get tenant info for blob storage path
          const tenant = await db.tenant.findUnique({
            where: { id: tenantId },
          });

          let fileUrl = item["@microsoft.graph.downloadUrl"] || "";

          // For supported file types, try to download and upload to blob storage
          const supportedTypes = SupportedExtensions;

          if (supportedTypes.includes(extension.toLowerCase())) {
            try {
              // Download file content from OneDrive or SharePoint
              const fileContent = await getFileContent(
                client,
                item.id,
                item["@microsoft.graph.downloadUrl"],
                { siteId, driveId }
              );

              if (fileContent) {
                // Upload to Azure Blob Storage
                fileUrl = await uploadToBlobStorage(
                  fileContent,
                  item.name,
                  tenant?.slug || "unknown",
                  options.workspaceSlug || "default"
                );
              }
            } catch (downloadError) {
              console.error(
                `Error downloading/uploading file ${item.id}:`,
                downloadError
              );
              // Continue with OneDrive URL if download/upload fails
            }
          }

          // Create new file record
          const newFile = await db.file.create({
            data: {
              name: item.name,
              size: `${item.size || "0"}`,
              extension,
              oneDriveFileId: item.id,
              workspaceId,
              pageId: localFolder?.pageId ?? pageId,
              // Only set folderId, not parentId which could cause ObjectID issues
              ...(localFolder?.id ? { folderId: localFolder?.id } : {}),
              ...(localPage ? { parentId: pageId } : {}),
              type: "file",
              url: fileUrl,
              updatedAt: new Date(item.lastModifiedDateTime || Date.now()),
            },
          });

          // Only index supported files
          if (
            fileUrl.includes("blob.core.windows.net") ||
            supportedTypes.includes(extension.toLowerCase())
          ) {
            try {
              // Determine document type for better processing
              const documentType = getDocumentType(newFile.name);

              // Use workspaceChatService instead of direct fetch
              await workspaceChatService.uploadForIndexing(
                {
                  document_path: newFile.url,
                  document_type: documentType,
                  userId,
                  workspaceSlug: slug,
                  tenantId,
                  file_id: newFile?.id,
                },
                {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                }
              );
            } catch (indexError) {
              console.error(`Error indexing file ${newFile.id}:`, indexError);
              // Continue even if indexing fails
            }
          }

          syncedFiles++;
        } else if (
          new Date(item.lastModifiedDateTime) >
          (localFile.modifiedAt || new Date(0))
        ) {
          // Update existing file if OneDrive version is newer
          let fileUrl =
            item["@microsoft.graph.downloadUrl"] || localFile.url || "";

          // For supported file types, try to download and upload to blob storage if not already in blob storage
          if (!localFile.url?.includes("blob.core.windows.net")) {
            const extension =
              localFile.extension || item.name.split(".").pop() || "";
            const supportedTypes = SupportedExtensions;
            if (supportedTypes.includes(extension.toLowerCase())) {
              try {
                // Get tenant info for blob storage path
                const tenant = await db.tenant.findUnique({
                  where: { id: tenantId },
                });

                // Download file content from OneDrive or SharePoint
                const fileContent = await getFileContent(
                  client,
                  item.id,
                  item["@microsoft.graph.downloadUrl"],
                  { siteId, driveId }
                );

                if (fileContent) {
                  // Upload to Azure Blob Storage
                  fileUrl = await uploadToBlobStorage(
                    fileContent,
                    item.name,
                    tenant?.slug || "unknown",
                    options.workspaceSlug || "default"
                  );
                }
              } catch (downloadError) {
                console.error(
                  `Error updating file ${item.id} to blob storage:`,
                  downloadError
                );
                // Continue with existing URL if download/upload fails
              }
            }
          }

          await db.file.update({
            where: { id: localFile.id },
            data: {
              size: `${item.size || "0"}`,
              updatedAt: new Date(item.lastModifiedDateTime),
              url: fileUrl,
            },
          });
          syncedFiles++;
        }
      }
    }

    return {
      synced: true,
      syncedFiles,
      folderId,
      localFolderId: localFolder?.id,
    };
  } catch (error) {
    console.error(`Error syncing folder ${folderId}:`, error);
    return {
      synced: false,
      syncedFiles,
      error: error.message || "Unknown error syncing folder",
      folderId,
    };
  }
}

export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    const options: SyncOptions = await req.json();
    const client = await getAuthenticatedClient(
      options.tenantId,
      session.userId
    );
    const workspace = await db.workspace.findFirst({
      where: { slug: options?.workspaceSlug },
    });

    if (!workspace) {
      return NextResponse.json(
        { synced: false, error: "Workspace not found" },
        { status: 404 }
      );
    }

    // If this is a root folder sync, update the workspace's folder ID
    if (!options.parentFolderId && options.folderId) {
      if (options.siteId) {
        // For SharePoint sites, store both the site ID and folder ID
        await db.workspace.update({
          where: { id: workspace.id },
          data: {
            oneDriveFolderId: options.folderId,
          },
        });
      } else {
        // For personal OneDrive (legacy support)
        await db.workspace.update({
          where: { id: workspace.id },
          data: { oneDriveFolderId: options.folderId },
        });
      }
    }

    const result = await syncFolder(
      client,
      {
        ...options,
        userId: session.userId,
        workspaceId: workspace?.id,
      },
      token
    );

    // If sync was successful, return success response
    if (result.synced) {
      return NextResponse.json({
        synced: true,
        syncedFiles: result?.syncedFiles,
        folderId: result?.folderId,
        localFolderId: result?.localFolderId,
      });
    } else {
      // If sync failed for this folder but we want to continue with others
      return NextResponse.json(
        {
          synced: false,
          error: result.error || "Failed to sync folder",
          folderId: result?.folderId,
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Sync error:", error);
    return NextResponse.json(
      {
        synced: false,
        error: error.message || "Failed to sync with OneDrive",
      },
      { status: 500 }
    );
  }
}
