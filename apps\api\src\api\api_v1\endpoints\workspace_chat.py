from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.services.workspace_rag import WorkspaceRAGManager
from src.api.deps import get_db
from starlette.responses import StreamingResponse
import json
import datetime
from src.services.usage_tracker import Usage<PERSON>racker
from bson import ObjectId
from src.agents.rag_agent import CustomJSONEncoder
import logging
from urllib.parse import unquote
import aiohttp
import base64
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize the workspace RAG manager
workspace_rag_manager = WorkspaceRAGManager()

class ImageAttachment(BaseModel):
    id: str
    url: str
    name: str
    type: str
    size: int
    preview: Optional[str] = None

def convert_dict_to_image_attachment(img_dict: dict) -> ImageAttachment:
    """Convert dictionary to ImageAttachment object"""
    return ImageAttachment(
        id=img_dict.get('id', ''),
        url=img_dict.get('url', ''),
        name=img_dict.get('name', ''),
        type=img_dict.get('type', ''),
        size=img_dict.get('size', 0),
        preview=img_dict.get('preview')
    )

async def process_images_for_context(images: List[ImageAttachment]) -> str:
    """
    Process images and generate descriptions using vision model.
    """
    image_descriptions = []

    for i, image in enumerate(images):
        try:
            # Handle different file types
            if image.type == 'application/pdf':
                # Download PDF and extract text
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            import tempfile
                            from pypdf import PdfReader
                            # Save PDF to temp file
                            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                                temp_file.write(await response.read())
                                temp_file_path = temp_file.name

                            try:
                                # Extract text from PDF
                                with open(temp_file_path, 'rb') as pdf_file:
                                    pdf_reader = PdfReader(pdf_file)
                                    text_content = []
                                    for page in pdf_reader.pages:
                                        text_content.append(page.extract_text())

                                description = f"PDF Content Summary:\n{''.join(text_content)}"
                                os.unlink(temp_file_path)  # Clean up temp file
                            except Exception as pdf_error:
                                logger.error(f"Error extracting PDF content: {pdf_error}")
                                description = f"Unable to extract content from PDF {image.name} - {str(pdf_error)}"
                        else:
                            description = f"Failed to download PDF {image.name}: HTTP {response.status}"
            elif (image.type.startswith('text/') or
                  image.type in ['application/csv', 'text/csv'] or
                  'markdown' in image.type or
                  (image.type == 'application/octet-stream' and
                   (image.name.lower().endswith('.md') or
                    image.name.lower().endswith('.mdx') or
                    image.name.lower().endswith('.markdown') or
                    image.name.lower().endswith('.txt') or
                    image.name.lower().endswith('.csv')))):
                # Handle text, CSV, and markdown files (including files with missing MIME types)
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            try:
                                # Check if this is likely a markdown file based on filename
                                is_markdown = (image.name.lower().endswith('.md') or
                                             image.name.lower().endswith('.mdx') or
                                             image.name.lower().endswith('.markdown') or
                                             'markdown' in image.type)

                                # Try different encodings for better compatibility
                                try:
                                    text_content = await response.text(encoding='utf-8')
                                except UnicodeDecodeError:
                                    try:
                                        text_content = await response.text(encoding='latin-1')
                                    except UnicodeDecodeError:
                                        text_content = await response.text(encoding='cp1252', errors='ignore')

                                # Determine file type based on content and filename
                                if 'csv' in image.type:
                                    file_type = "CSV"
                                elif is_markdown:
                                    file_type = "Markdown"
                                else:
                                    file_type = "Text"

                                content_preview = text_content[:2000] if len(text_content) > 2000 else text_content
                                truncated = "..." if len(text_content) > 2000 else ""

                                # Add specific statistics based on file type
                                if 'csv' in image.type:
                                    lines = text_content.split('\n')
                                    description = f"{file_type} File Content ({len(lines)} lines):\n{content_preview}{truncated}"
                                elif is_markdown:
                                    lines = text_content.split('\n')
                                    # Count markdown elements
                                    headers = len([line for line in lines if line.strip().startswith('#')])
                                    code_blocks = text_content.count('```')
                                    links = text_content.count('[') + text_content.count('](')

                                    description = f"Markdown Document Analysis:\n"
                                    description += f"- File: {image.name}\n"
                                    description += f"- Lines: {len(lines)}\n"
                                    description += f"- Characters: {len(text_content)}\n"
                                    description += f"- Headers: {headers}\n"
                                    description += f"- Code blocks: {code_blocks // 2}\n"  # Divide by 2 since each block has opening and closing
                                    description += f"- Links/References: {links // 2}\n\n"
                                    description += f"Content:\n{content_preview}{truncated}"
                                else:
                                    description = f"{file_type} File Content ({len(text_content)} characters):\n{content_preview}{truncated}"

                            except Exception as text_error:
                                logger.error(f"Error reading text content: {text_error}")
                                description = f"Unable to read content from {image.name} - {str(text_error)}"
                        else:
                            description = f"Failed to download text file {image.name}: HTTP {response.status}"
            elif image.type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-word']:
                # Handle Word documents (DOCX/DOC)
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            try:
                                import tempfile
                                from docx import Document

                                # Determine file extension based on MIME type
                                file_ext = '.docx' if 'openxmlformats' in image.type else '.doc'

                                # Save Word document to temp file
                                with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
                                    temp_file.write(await response.read())
                                    temp_file_path = temp_file.name

                                # Read Word document
                                if file_ext == '.docx':
                                    doc = Document(temp_file_path)

                                    # Extract text from all paragraphs
                                    paragraphs = []
                                    for paragraph in doc.paragraphs:
                                        if paragraph.text.strip():
                                            paragraphs.append(paragraph.text.strip())

                                    # Extract text from tables
                                    table_content = []
                                    for table in doc.tables:
                                        for row in table.rows:
                                            row_text = []
                                            for cell in row.cells:
                                                if cell.text.strip():
                                                    row_text.append(cell.text.strip())
                                            if row_text:
                                                table_content.append(" | ".join(row_text))

                                    # Combine content
                                    full_text = "\n".join(paragraphs)
                                    if table_content:
                                        full_text += "\n\nTables:\n" + "\n".join(table_content)

                                    word_summary = f"Word Document Analysis:\n"
                                    word_summary += f"- Paragraphs: {len(paragraphs)}\n"
                                    word_summary += f"- Tables: {len(doc.tables)}\n"
                                    word_summary += f"- Total characters: {len(full_text)}\n\n"
                                    word_summary += f"Content:\n{full_text[:1500]}{'...' if len(full_text) > 1500 else ''}"

                                    description = word_summary
                                else:
                                    # For .doc files, we'll use a fallback method
                                    description = f"Word Document (.doc): {image.name}\nNote: .doc files require additional processing. Please convert to .docx for better text extraction."

                                os.unlink(temp_file_path)  # Clean up temp file
                            except Exception as word_error:
                                logger.error(f"Error reading Word document: {word_error}")
                                description = f"Unable to read content from Word document {image.name} - {str(word_error)}"
                        else:
                            description = f"Failed to download Word document {image.name}: HTTP {response.status}"
            elif image.type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']:
                # Handle Excel files
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            try:
                                import tempfile
                                import pandas as pd

                                # Determine file extension based on MIME type
                                file_ext = '.xlsx' if 'openxmlformats' in image.type else '.xls'

                                # Save Excel to temp file
                                with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
                                    temp_file.write(await response.read())
                                    temp_file_path = temp_file.name

                                # Read Excel file with error handling for different engines
                                try:
                                    if file_ext == '.xlsx':
                                        df = pd.read_excel(temp_file_path, engine='openpyxl')
                                    else:
                                        df = pd.read_excel(temp_file_path, engine='xlrd')
                                except Exception:
                                    # Fallback to default engine with additional options
                                    try:
                                        df = pd.read_excel(temp_file_path)
                                    except Exception:
                                        # Last resort: try with string conversion
                                        df = pd.read_excel(temp_file_path, dtype=str)

                                # Create comprehensive summary
                                rows, cols = df.shape
                                excel_summary = f"Excel File Analysis:\n"
                                excel_summary += f"- Dimensions: {rows} rows × {cols} columns\n"

                                # Convert column names to strings to avoid join errors
                                column_names = [str(col) for col in df.columns[:10]]
                                excel_summary += f"- Columns: {', '.join(column_names)}{'...' if len(df.columns) > 10 else ''}\n"

                                # Add data types info
                                if cols > 0:
                                    try:
                                        data_types = df.dtypes.value_counts().to_dict()
                                        # Convert data type keys to strings
                                        data_types_str = {str(k): v for k, v in data_types.items()}
                                        excel_summary += f"- Data types: {data_types_str}\n"
                                    except Exception as dtype_error:
                                        logger.warning(f"Error getting data types: {dtype_error}")
                                        excel_summary += f"- Data types: Mixed types detected\n"

                                # Add sample data with error handling
                                if rows > 0:
                                    try:
                                        # Limit sample data size to prevent memory issues
                                        sample_df = df.head(3)
                                        # Convert to string with max column width to prevent overflow
                                        sample_data = sample_df.to_string(max_cols=10, max_colwidth=20)
                                        excel_summary += f"\nSample data (first 3 rows):\n{sample_data}"
                                    except Exception as sample_error:
                                        logger.warning(f"Error getting sample data: {sample_error}")
                                        try:
                                            # Fallback: try with string conversion
                                            sample_df = df.head(3).astype(str)
                                            sample_data = sample_df.to_string(max_cols=10, max_colwidth=20)
                                            excel_summary += f"\nSample data (first 3 rows, as text):\n{sample_data}"
                                        except Exception:
                                            excel_summary += f"\nSample data: Unable to display sample (contains complex data types)"

                                description = excel_summary[:2000] + ('...' if len(excel_summary) > 2000 else '')

                                os.unlink(temp_file_path)  # Clean up temp file
                            except Exception as excel_error:
                                logger.error(f"Error reading Excel content: {excel_error}")
                                description = f"Unable to read content from Excel file {image.name} - {str(excel_error)}"
                        else:
                            description = f"Failed to download Excel file {image.name}: HTTP {response.status}"
            elif image.type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint']:
                # Handle PowerPoint files
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            try:
                                import tempfile
                                import os

                                # For now, we'll provide basic metadata for PowerPoint files
                                # Full text extraction from PPTX requires python-pptx library
                                file_ext = '.pptx' if 'openxmlformats' in image.type else '.ppt'

                                # Get file size from response headers
                                content_length = response.headers.get('content-length')
                                file_size = int(content_length) if content_length else 0
                                file_size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0

                                description = f"PowerPoint Presentation Analysis:\n"
                                description += f"- File: {image.name}\n"
                                description += f"- Format: {file_ext.upper()}\n"
                                description += f"- Size: {file_size_mb}MB\n"
                                description += f"- MIME Type: {image.type}\n"
                                description += f"- Status: Ready for processing\n"
                                description += f"- Note: Full slide content extraction available with enhanced processing"

                            except Exception as ppt_error:
                                logger.error(f"Error processing PowerPoint file: {ppt_error}")
                                description = f"PowerPoint File: {image.name} (Format: {image.type}) - Processing failed"
                        else:
                            description = f"Failed to access PowerPoint file {image.name}: HTTP {response.status}"
            elif image.type.startswith('audio/'):
                # Handle audio files - extract metadata and prepare for transcription
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            try:
                                # Get file size from response headers
                                content_length = response.headers.get('content-length')
                                file_size = int(content_length) if content_length else 0
                                file_size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0

                                # Determine audio format
                                audio_format = image.type.split('/')[-1].upper()
                                if audio_format == 'MPEG':
                                    audio_format = 'MP3'

                                description = f"Audio File Analysis:\n"
                                description += f"- File: {image.name}\n"
                                description += f"- Format: {audio_format}\n"
                                description += f"- Size: {file_size_mb}MB\n"
                                description += f"- MIME Type: {image.type}\n"

                                # Try to use Azure Video Indexer for transcription
                                try:
                                    from src.services.azure_video_indexer import azure_video_indexer_service

                                    if hasattr(azure_video_indexer_service, 'account_id') and azure_video_indexer_service.account_id:
                                        # Attempt transcription with synchronous processing
                                        transcription_result = await azure_video_indexer_service.transcribe_audio(
                                            image.url,
                                            image.name,
                                            language="en-US",
                                            wait_for_completion=True,
                                            max_wait_seconds=300  # 5 minutes timeout
                                        )

                                        if transcription_result.get('status') == 'completed' and not transcription_result.get('error'):
                                            transcript = transcription_result.get('transcript', '')
                                            metadata = transcription_result.get('metadata', {})

                                            description += f"- Status: Transcription completed\n"
                                            description += f"- Duration: {metadata.get('duration', 0)} seconds\n"
                                            description += f"- Language: {metadata.get('language', 'unknown')}\n"
                                            description += f"- Transcript: {transcript[:500]}{'...' if len(transcript) > 500 else ''}\n"

                                            if metadata.get('keywords'):
                                                description += f"- Keywords: {', '.join(metadata['keywords'][:5])}\n"
                                        else:
                                            error_msg = transcription_result.get('error', 'Unknown error')
                                            status = transcription_result.get('status', 'failed')
                                            description += f"- Status: Transcription {status} - {error_msg}\n"
                                            description += f"- Note: Audio file uploaded but transcription unavailable"
                                    else:
                                        description += f"- Status: Ready for transcription\n"
                                        description += f"- Note: Azure Video Indexer not configured (set AZURE_VIDEO_INDEXER_ACCOUNT_ID and ACCESS_TOKEN)"

                                except Exception as transcription_error:
                                    logger.warning(f"Audio transcription failed: {transcription_error}")
                                    description += f"- Status: Transcription error - {str(transcription_error)}\n"
                                    description += f"- Note: Audio file uploaded successfully but transcription failed"

                            except Exception as audio_error:
                                logger.error(f"Error processing audio metadata: {audio_error}")
                                description = f"Audio File: {image.name} (Format: {image.type}) - Metadata extraction failed"
                        else:
                            description = f"Failed to access audio file {image.name}: HTTP {response.status}"
            elif image.type.startswith('image/'):
                # Handle images as before
                async with aiohttp.ClientSession() as session:
                    async with session.get(image.url) as response:
                        if response.status == 200:
                            image_data = await response.read()
                            img_base64 = base64.b64encode(image_data).decode()
                            img_format = image.type.split('/')[-1] if '/' in image.type else 'jpeg'
                            description = await analyze_image_with_vision_model(img_base64, img_format, image.name)
                        else:
                            description = f"Failed to download image {image.name}: HTTP {response.status}"
            else:
                # Unsupported file type
                description = f"Unsupported file type: {image.name} ({image.type})"

            image_descriptions.append(f"File {i+1} ({image.name}): {description}")
            logger.info(f"Description from process_images_for_context: {description}")

        except Exception as e:
            logger.error(f"Error processing file {image.name}: {str(e)}")
            image_descriptions.append(f"File {i+1} ({image.name}): Error processing file - {str(e)}")

    return "\n".join(image_descriptions)

async def analyze_image_with_vision_model(img_base64: str, img_format: str, filename: str) -> str:
    """
    Analyze image using vision model API.
    """
    try:
        endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
        headers = {
            "Content-Type": "application/json",
            "api-key": os.getenv("DEEPSEEK_API_KEY")
        }

        messages = [
            {
                "role": "system",
                "content": [
                    {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images. Focus on all visible elements, text, objects, people, scenes, and any relevant context that would help answer questions about the image."}
                ]
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": f"Analyze this image ({filename}) and provide a comprehensive description. Include all visible text, objects, people, scenes, colors, and any other relevant details."},
                    {"type": "image_url", "image_url": {"url": f"data:image/{img_format};base64,{img_base64}"}}
                ]
            }
        ]

        data = {
            "messages": messages,
            "model": os.getenv('AZURE_VISION_MODEL', "mistral-medium-2505"),
            "temperature": 0.7,
            "max_tokens": 1000
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"Vision API error: HTTP {response.status}")
                    return f"Unable to analyze image {filename} - API error"

    except Exception as e:
        logger.error(f"Error calling vision model: {str(e)}")
        return f"Unable to analyze image {filename} - {str(e)}"

class ChatMessage(BaseModel):
    role: str
    content: str
    id: Optional[str] = None
    createdAt: Optional[str] = None

class ChatQuery(BaseModel):
    question: Optional[str] = None
    stream: Optional[bool] = False
    config: Optional[Dict[str, Any]] = None
    previous_message: Optional[str] = None
    images: Optional[List[ImageAttachment]] = None
    messages: Optional[List[ChatMessage]] = None

class DocumentIndex(BaseModel):
    workspaceSlug: str
    document_path: str
    document_type: Optional[str] = "auto"
    metadata: Optional[Dict[str, Any]] = None

class WorkspaceAccess(BaseModel):
    user_id: str
    workspace_ids: List[str]

# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        # Add more custom type handling as needed
        return super().default(obj)

async def check_llm_scope_permissions(tenant_id: str, include_web_results: bool, db):
    """
    Check if the requested operation is allowed by the tenant's LLM scope settings.
    """
    try:
        # Get tenant's LLM scope settings
        tenant = await db.Tenant.find_one({"_id": ObjectId(tenant_id)})
        if not tenant:
            return False, "Tenant not found"

        llm_scope = tenant.get("llmScope", ["INTERNAL_ONLY"])

        # Handle both old single string format and new array format
        if isinstance(llm_scope, str):
            llm_scope = [llm_scope]

        # Check if web search is allowed
        if include_web_results:
            web_allowed = any(scope in ["EXTERNAL_ONLY", "HYBRID", "FULL_ACCESS", "web", "hybrid"] for scope in llm_scope)
            if not web_allowed:
                return False, f"Web search is not allowed with current LLM scope: {llm_scope}. Contact your administrator to change the scope settings."

        # Check if MCP_ONLY scope is being used correctly
        if llm_scope == ["MCP_ONLY"] or "MCP_ONLY" in llm_scope:
            return False, "MCP_ONLY scope requires using MCP servers. Please use the MCP chat endpoint instead."

        # For now, internal document access is allowed for all scopes except EXTERNAL_ONLY
        # This can be extended later if needed

        return True, None

    except Exception as e:
        logger.error(f"Error checking LLM scope permissions: {e}")
        return False, "Error checking LLM scope permissions"

@router.post("/chat", response_model=Dict[str, Any], status_code=200)
async def chat_with_workspace_documents(
    query: ChatQuery,
    current_user: str,
    user_name: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Chat with documents across all accessible workspaces or a specific workspace.
    Returns a structured JSON response with title and answer.
    """
    # Set the database client
    workspace_rag_manager.db_client = db
    stream = query.stream if hasattr(query, 'stream') else False

    # Handle both AI SDK format (messages array) and legacy format (question + previous_message)
    if query.messages and len(query.messages) > 0:
        # AI SDK format - extract question from last user message and build conversation history
        user_messages = [msg for msg in query.messages if msg.role == "user"]
        if user_messages:
            question = user_messages[-1].content
        else:
            question = query.question or ""

        # Build conversation history from messages (excluding the current question)
        conversation_messages = query.messages[:-1] if len(query.messages) > 1 else []
        if conversation_messages:
            # Format conversation history as structured JSON
            conversation_history = {
                "conversationHistory": [
                    {
                        "role": msg.role,
                        "content": msg.content.strip(),
                        "turn": idx + 1,
                        "timestamp": msg.createdAt or "",
                        "id": msg.id or ""
                    }
                    for idx, msg in enumerate(conversation_messages)
                ],
                "totalTurns": len(conversation_messages),
                "contextNote": "This is the conversation history. The current question is provided separately."
            }
            previous_message = json.dumps(conversation_history, indent=2)
            logger.info(f"AI SDK format: Built conversation history with {len(conversation_messages)} messages for question: '{question[:50]}...'")
        else:
            previous_message = None
            logger.info(f"AI SDK format: No conversation history for question: '{question[:50]}...'")
    else:
        # Legacy format
        question = query.question
        previous_message = query.previous_message
        logger.info(f"Legacy format: Using question: '{question[:50] if question else 'None'}...' with previous_message: {'Yes' if previous_message else 'No'}")

    # Override query object properties for consistent processing
    query.question = question
    query.previous_message = previous_message

    if not stream:
        # Extract web search config and search mode
        include_web_results = False
        search_mode = "internal"  # Default to internal only

        if query.config:
            include_web_results = query.config.get("includeWebResults", False) is True
            search_mode = query.config.get("searchMode", "internal")
            logger.info(f"Search mode: {search_mode}, Web search enabled: {include_web_results} for query: '{query.question}'")
        else:
            logger.info(f"Default search mode (internal) for query: '{query.question}'")

        # Check LLM scope permissions
        scope_allowed, scope_error = await check_llm_scope_permissions(tenant_id, include_web_results, db)
        if not scope_allowed:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=scope_error
            )

        # Process images if provided
        image_context = ""
        all_images = []

        # Collect images from current request
        if query.images and len(query.images) > 0:
            all_images.extend(query.images)

        # Extract images from conversation history to maintain context
        conversation_messages = query.messages[:-1] if len(query.messages) > 1 else []
        for msg in conversation_messages:
            if msg.role == "user" and hasattr(msg, 'metadata') and msg.metadata:
                # Check if message has images in metadata
                if isinstance(msg.metadata, dict) and 'images' in msg.metadata:
                    msg_images = msg.metadata['images']
                    if msg_images and isinstance(msg_images, list):
                        all_images.extend(msg_images)

        # Remove duplicates based on image ID and convert dicts to ImageAttachment objects
        unique_images = []
        seen_ids = set()
        for img in all_images:
            if hasattr(img, 'id') and img.id not in seen_ids:
                unique_images.append(img)
                seen_ids.add(img.id)
            elif isinstance(img, dict) and 'id' in img and img['id'] not in seen_ids:
                # Convert dict to ImageAttachment object
                img_attachment = convert_dict_to_image_attachment(img)
                unique_images.append(img_attachment)
                seen_ids.add(img['id'])

        if unique_images:
            logger.info(f"Processing {len(unique_images)} images for query (including {len(query.images or [])} new and {len(unique_images) - len(query.images or [])} from history): '{query.question}'")
            image_context = await process_images_for_context(unique_images)

        # Combine question with image context
        enhanced_question = query.question
        if image_context:
            enhanced_question = f"{query.question}\n\nImage Analysis:\n{image_context}"

        # Process the query with access control
        result = await workspace_rag_manager.query_workspace(
            user_id=current_user,
            question=enhanced_question,
            tenant_id=tenant_id,
            user_name=user_name,
            previous_message=query.previous_message,
            include_web_results=include_web_results,
            search_mode=search_mode
        )

        if result.get("status") == 403:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have access to this workspace"
            )
        elif result.get("status") == 500:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "An error occurred processing your request")
            )

        # Get sources and serialize them to handle ObjectId
        sources = result.get("sources", [])
        serializable_sources = json.loads(json.dumps(sources, cls=CustomJSONEncoder))

        # Return the structured JSON response with title, answer, and sources
        return {
            "answer": result.get("answer", ""),
            "title": result.get("title", ""),
            "sources": serializable_sources,  # Include serialized sources in the response
            "status": result.get("status", 200)
        }
    else:
        # Extract web search config and search mode for streaming
        include_web_results = False
        search_mode = "internal"  # Default to internal only

        if query.config:
            include_web_results = query.config.get("includeWebResults", False) is True
            search_mode = query.config.get("searchMode", "internal")
            logger.info(f"Streaming - Search mode: {search_mode}, Web search enabled: {include_web_results} for query: '{query.question}'")
        else:
            logger.info(f"Streaming - Default search mode (internal) for query: '{query.question}'")

        # Check LLM scope permissions for streaming
        scope_allowed, scope_error = await check_llm_scope_permissions(tenant_id, include_web_results, db)
        if not scope_allowed:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=scope_error
            )

        # Process images if provided for streaming
        image_context = ""
        all_images = []

        # Collect images from current request
        if query.images and len(query.images) > 0:
            all_images.extend(query.images)

        # Extract images from conversation history to maintain context
        conversation_messages = query.messages[:-1] if len(query.messages) > 1 else []
        for msg in conversation_messages:
            if msg.role == "user" and hasattr(msg, 'metadata') and msg.metadata:
                # Check if message has images in metadata
                if isinstance(msg.metadata, dict) and 'images' in msg.metadata:
                    msg_images = msg.metadata['images']
                    if msg_images and isinstance(msg_images, list):
                        all_images.extend(msg_images)

        # Remove duplicates based on image ID and convert dicts to ImageAttachment objects
        unique_images = []
        seen_ids = set()
        for img in all_images:
            if hasattr(img, 'id') and img.id not in seen_ids:
                unique_images.append(img)
                seen_ids.add(img.id)
            elif isinstance(img, dict) and 'id' in img and img['id'] not in seen_ids:
                # Convert dict to ImageAttachment object
                img_attachment = convert_dict_to_image_attachment(img)
                unique_images.append(img_attachment)
                seen_ids.add(img['id'])

        if unique_images:
            logger.info(f"Processing {len(unique_images)} images for streaming query (including {len(query.images or [])} new and {len(unique_images) - len(query.images or [])} from history): '{query.question}'")
            image_context = await process_images_for_context(unique_images)
            logger.info(f"Image context: {image_context}")

        # Combine question with image context for streaming
        enhanced_question = query.question
        if image_context:
            enhanced_question = f"{query.question}\n\nImage Analysis:\n{image_context}"
        logger.info(f"Streaming query: '{enhanced_question}...'")

        # Streaming response
        stream_generator = await workspace_rag_manager.query_workspace(
            user_id=current_user,
            question=enhanced_question,
            tenant_id=tenant_id,
            user_name=user_name,
            previous_message=query.previous_message,
            stream=True,
            include_web_results=include_web_results,
            search_mode=search_mode
        )
        async def text_stream_generator():
            # Store the title for later use when creating the chat
            title = None
            # Flag to track if we've sent the sources in the first chunk
            sources_sent = False

            async for chunk in stream_generator:
                try:
                    # Check if this is an error chunk
                    if "error" in chunk:
                        # Stream the error message directly to the client
                        error_message = chunk.get("error", "Unknown error occurred")
                        yield f"ERROR: {error_message}"
                        # Stop streaming after sending the error
                        break

                    # Extract the answer and sources from the chunk
                    answer = chunk.get("answer", "")
                    sources = chunk.get("sources", [])

                    # Store the title from the first chunk that has one
                    if title is None and chunk.get("title"):
                        title = chunk.get("title")

                    # For the first chunk, send the sources as JSON
                    if not sources_sent and sources:
                        # Send the sources as a JSON object in the first chunk
                        # Use CustomJSONEncoder to handle ObjectId serialization
                        # Include the answer in the same JSON object to ensure proper parsing
                        first_chunk = {
                            "sources": sources,
                            "answer": answer,  # Include the answer in the first chunk
                            "is_first_chunk": True  # Add a marker to identify this as the first chunk
                        }
                        sources_json = json.dumps(first_chunk, cls=CustomJSONEncoder)
                        yield sources_json
                        sources_sent = True
                        # We've already included the answer in the JSON, so no need to yield it separately
                    else:
                        # For subsequent chunks, only stream the answer text
                        yield answer
                except TypeError as e:
                    # For text streaming, just yield the error message directly
                    error_message = f"Error processing response: {str(e)}"
                    yield f"ERROR: {error_message}"
                    break
                except Exception as e:
                    # Handle any other exceptions that might occur
                    error_message = f"Unexpected error: {str(e)}"
                    yield f"ERROR: {error_message}"
                    break

        return StreamingResponse(
            content=text_stream_generator(),
            media_type="text/plain"  # Plain text format for streaming
        )
@router.post("/index", response_model=Dict[str, Any], status_code=200)
async def index_workspace_document(
    index_request: DocumentIndex,
    current_user: str,
    tenant_id: str,
    file_id: str,
    db = Depends(get_db)
):
    # Initialize usage tracker
    start_time = datetime.datetime.now(datetime.timezone.utc)
    usage_tracker = UsageTracker(db)
    """
    Index a document in a workspace, with access control.
    Downloads the document from the provided path if it's a URL.
    """
    # Set the database client
    workspace_rag_manager.db_client = db
    await db.File.update_one(
        {"_id": ObjectId(file_id)},
        {"$set": {"vectorizationStatus": "PROCESSING"}}
    )

    # Check if document_path is a URL and download if needed
    document_path = index_request.document_path

    # Check if this is an audio/video file that should use the original URL
    is_audio_video_url = False
    if document_path.startswith(('http://', 'https://')):
        # Check if it's an audio or video file by looking at the URL extension
        audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
        is_audio_video_url = any(ext in document_path.lower() for ext in audio_video_extensions)

        # Also try to check content type via HEAD request
        if not is_audio_video_url:
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.head(document_path) as response:
                        content_type = response.headers.get('content-type', '').lower()
                        is_audio_video_url = (
                            content_type.startswith('audio/') or
                            content_type.startswith('video/')
                        )
            except Exception as e:
                logger.warning(f"Could not determine content type for URL: {e}")

    if document_path.startswith(('http://', 'https://')) and not is_audio_video_url:
        # Download non-audio/video files for local processing
        try:
            import aiohttp
            from urllib.parse import urlparse

            # Create downloads directory if it doesn't exist
            download_dir = "./files"
            os.makedirs(download_dir, exist_ok=True)

            # Extract filename from URL or generate one
            parsed_url = urlparse(document_path)
            filename = os.path.basename(parsed_url.path)
            if not filename:
                # Generate filename if not present in URL
                import uuid
                ext = ".pdf"  # Default extension
                if "." in document_path:
                    ext = "." + document_path.split(".")[-1]
                filename = f"{uuid.uuid4()}{ext}"

            # Full path for downloaded file
            local_path = os.path.join(download_dir, filename)

            # Download the file
            async with aiohttp.ClientSession() as session:
                async with session.get(document_path) as response:
                    if response.status != 200:
                        return {
                            "error": f"Failed to download document: HTTP {response.status}",
                            "status": 500
                        }

                    content = await response.read()
                    with open(local_path, "wb") as f:
                        f.write(content)

            # Update document path to local path
            document_path = local_path

            # Store the path to delete after indexing
            file_to_delete = local_path

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error downloading document: {str(e)}"
            )
    elif is_audio_video_url:
        # For audio/video files, keep the original URL for Azure Video Indexer
        file_to_delete = None
        logger.info(f"Audio/video file detected, preserving URL for processing: {document_path}")
    else:
        file_to_delete = None

    slug=unquote(index_request.workspaceSlug)
    workspace=await db.Workspace.find_one(
        {
            "slug":slug,
        }
    )

    # Handle video/audio files with async processing
    if is_audio_video_url:
        logger.info(f"Starting async video processing for file {file_id}")

        # Import video processing service
        from src.services.video_processing_service import video_processing_service
        video_processing_service.db_client = db

        # Create video processing job
        video_files = [{
            "url": document_path,
            "name": os.path.basename(document_path),
            "type": "video" if any(ext in document_path.lower() for ext in ['.mp4', '.avi', '.mov', '.webm', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']) else "audio",
            "size": 0  # Size not available from URL
        }]

        job_id = await video_processing_service.create_video_processing_job(
            video_files=video_files,
            user_id=current_user,
            tenant_id=tenant_id,
            file_id=file_id,
            workspace_slug=slug
        )

        logger.info(f"Created video processing job {job_id} for file {file_id}")

        # Return success - file status will remain PROCESSING until webhook completes
        result = {
            "status": 200,
            "message": f"Video/audio file submitted for background processing",
            "job_id": job_id,
            "processing_status": "async_processing_started"
        }
    else:
        # Process non-video/audio files with access control (synchronous)
        result = await workspace_rag_manager.index_document_with_access_check(
            user_id=current_user,
            workspace_id=workspace["_id"],
            document_path=document_path,
            document_type=index_request.document_type,
            metadata=index_request.metadata,
            tenant_id=tenant_id,
            file_id=file_id,
            slug=slug
        )

    # Log token usage for document indexing
    try:
        # Read file content based on file type
        import magic
        from pypdf import PdfReader

        # Handle URL vs local file path for file type detection
        if document_path.startswith(('http://', 'https://')):
            # For URLs (audio/video files), determine type from URL extension
            audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
            if any(ext in document_path.lower() for ext in audio_video_extensions):
                if any(ext in document_path.lower() for ext in ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']):
                    file_type = 'audio/mpeg'  # Generic audio type
                else:
                    file_type = 'video/mp4'   # Generic video type
            else:
                file_type = 'application/octet-stream'  # Unknown type
        else:
            # For local files, use magic to detect type
            mime = magic.Magic(mime=True)
            file_type = mime.from_file(document_path)

        if file_type.startswith('application/pdf'):
            # Handle PDF files
            with open(document_path, 'rb') as file:
                pdf_reader = PdfReader(file)
                document_content = '\n'.join([page.extract_text() for page in pdf_reader.pages])
        elif file_type.startswith('text/') or file_type in ['application/csv', 'text/csv'] or 'markdown' in file_type:
            # Handle text, CSV, and markdown files
            with open(document_path, 'r', encoding='utf-8', errors='ignore') as file:
                document_content = file.read()
        elif file_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-word']:
            # Handle Word documents
            try:
                from docx import Document
                if file_type.endswith('wordprocessingml.document'):  # .docx
                    doc = Document(document_path)
                    paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
                    document_content = '\n'.join(paragraphs)
                else:  # .doc files
                    document_content = f"Word document (.doc): {document_path} (requires conversion to .docx for full text extraction)"
            except Exception as word_error:
                logger.warning(f"Error reading Word document for token logging: {word_error}")
                document_content = f"Word document: {document_path} (content could not be read for token counting)"
        elif file_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']:
            # Handle Excel files
            try:
                import pandas as pd
                df = pd.read_excel(document_path)
                # Convert Excel content to text for token counting
                document_content = f"Excel file with {df.shape[0]} rows and {df.shape[1]} columns\n"
                # Convert column names to strings to avoid join errors
                column_names = [str(col) for col in df.columns]
                document_content += f"Columns: {', '.join(column_names)}\n"
                try:
                    # Limit the string conversion to prevent memory issues
                    document_content += df.to_string(max_rows=50, max_cols=20, max_colwidth=30)
                except Exception as string_error:
                    logger.warning(f"Error converting Excel to string: {string_error}")
                    try:
                        # Fallback: convert to strings first
                        df_str = df.astype(str)
                        document_content += df_str.to_string(max_rows=50, max_cols=20, max_colwidth=30)
                    except Exception:
                        document_content += "Content: Unable to convert data to string (contains complex data types)"
            except Exception as excel_error:
                logger.warning(f"Error reading Excel file for token logging: {excel_error}")
                document_content = f"Excel file: {document_path} (content could not be read for token counting)"
        elif file_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint']:
            # Handle PowerPoint files
            document_content = f"PowerPoint presentation: {document_path} (slide content extraction not implemented for token counting)"
        elif file_type.startswith('audio/') or file_type.startswith('video/'):
            # Handle audio/video files - minimal content for token counting
            if document_path.startswith(('http://', 'https://')):
                document_content = f"Audio/Video file URL: {document_path} (content will be processed by Azure Video Indexer)"
            else:
                document_content = f"Audio/Video file: {document_path} (content not processed for token counting)"
        else:
            # Skip token logging for unsupported file types
            logger.warning(f"Skipping token logging for unsupported file type: {file_type}")
            document_content = ''

        metadata_str = json.dumps(index_request.metadata) if index_request.metadata else ""

        # Log token usage for document content and metadata
        await usage_tracker.log_token_usage(
            tenant_id=tenant_id,
            input_text=document_content + metadata_str,
            output_text="",  # No output for indexing
            request_type="document_indexing",
            model_used="embed-v-4-0"  # Standard embedding model
        )
        await usage_tracker.log_api_request(
           user_id=current_user,
            tenant_id=tenant_id,
            endpoint="/index_document",
            method="POST",
            status_code=200,
            duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000)
        )
    except Exception as e:
        logger.error(f"Error logging token usage for document indexing: {e}")

    # Delete the downloaded file after indexing if it was downloaded
    if file_to_delete and os.path.exists(file_to_delete):
        try:
            os.remove(file_to_delete)
            result["file_cleanup"] = "Temporary file deleted successfully"
        except Exception as e:
            result["file_cleanup_error"] = f"Failed to delete temporary file: {str(e)}"

    if result.get("status") == 403:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have access to this workspace"
        )
    elif result.get("status") == 500:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "An error occurred indexing your document")
        )

    return result

@router.get("/check-access", response_model=WorkspaceAccess, status_code=200)
async def check_workspace_access(
    current_user: str,
    db = Depends(get_db)
):
    """
    Get list of workspaces the current user has access to.
    """
    # Set the database client
    workspace_rag_manager.db_client = db

    # Get workspaces the user has access to
    workspace_ids = await workspace_rag_manager.get_user_workspaces(current_user)

    return {
        "user_id": current_user,
        "workspace_ids": workspace_ids
    }
@router.get("/generate-title", status_code=200)
async def generate_title(
    query: str,
    tenant_id:str,
    db = Depends(get_db)
):
    """
    Get list of workspaces the current user has access to.
    """

        # Set the database client
    workspace_rag_manager.db_client = db
    workspace=await db.Workspace.find_one(
        {
            "tenantId":ObjectId(tenant_id),
        }
    )
    # Get workspaces the user has access to
    content = await workspace_rag_manager.generate_title_from_question(query,tenant_id,workspace["_id"])

    return content

@router.get("/delete-file", status_code=200)
async def delete_file(
    file_id: str,
    workspaceSlug: str,
    tenant_id: str,
    db = Depends(get_db)
):
    workspace_rag_manager.db_client = db
    slug=unquote(workspaceSlug)
    workspace=await db.Workspace.find_one(
    {
        "slug":slug,
    }
    )
    content = await workspace_rag_manager.delete_file(file_id=file_id,workspace_id=workspace["_id"],tenant_id=tenant_id)

    return content

@router.post("/clear-agent-cache", status_code=200)
async def clear_agent_cache(
    workspace_id: Optional[str] = None,
    db = Depends(get_db)
):
    """
    Clear the agent cache for a specific workspace or all workspaces.
    This is useful when configuration changes require agents to be recreated.
    """
    workspace_rag_manager.db_client = db
    result = workspace_rag_manager.clear_agent_cache(workspace_id)
    return result
