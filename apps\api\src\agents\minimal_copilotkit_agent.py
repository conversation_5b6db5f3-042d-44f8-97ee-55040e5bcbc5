"""
Minimal CopilotKit-based Agentic RAG System

This is a minimal working version that demonstrates the agentic approach
with proper conversation memory management using <PERSON><PERSON>hai<PERSON>.
"""

import asyncio
import logging
import time

from typing import Dict, List, Any, Optional, AsyncGenerator
import os
import re
from openai import AsyncAzureOpenAI
from bson import ObjectId

# Lang<PERSON>hain imports for conversation memory
try:
    from langchain.memory import ConversationBufferWindowMemory
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("LangChain not available. Using basic conversation handling.")

# Import real tools
from .tools.document_retrieval_tool import DocumentRetrievalTool
from .tools.vector_search_tool import VectorSearchTool
from .tools.web_search_tool import WebSearchTool
from .deep_research_agent import DeepResearchAgent

# Import MCP service for MCP server integration
from ..services.mcp_service import mcp_service

logger = logging.getLogger(__name__)

# Translation dictionary for localized messages
TRANSLATIONS = {
    "en": {
        "fetching_all_tools": "Fetching all tools...",
        "searching_documents": "Searching internal documents...",
        "searching_web": "Searching the web...",
        "analyzing_sources": "Analyzing sources...",
        "generating_response": "Generating response...",
        "no_results_found": "No relevant documents found for your query.",
        "web_search_failed": "Web search temporarily unavailable.",
        "sources": "Sources",
        "document_analysis": "Document Analysis",
        "web_results": "Web Results",
        "thinking": "Thinking",
        "research_summary": "Research Summary",
        "deep_analysis": "Deep Analysis",
        "mcp_server_error": "MCP server error occurred",
        "tool_execution_failed": "Tool execution failed",
        "processing_request": "Processing your request...",
        "synthesis_complete": "Analysis complete",
        "error_occurred": "An error occurred while processing your request.",
        "no_relevant_tools": "No relevant tools found to answer your query.",
        "mcp_health_check": "Checking MCP server health...",
        "no_tools_found": "No tools found to answer your query.",
        # System prompt translations
        "overview_and_definition": "Overview and Definition",
        "definition": "Definition",
        "technical_specifications": "Technical Specifications and Standards",
        "applications_and_use_cases": "Applications and Use Cases",
        "advantages_and_limitations": "Advantages and Limitations",
        "future_developments": "Future Developments",
        "conclusion": "Conclusion",
        "key_points": "Key Points",
        "summary": "Summary",
        "introduction": "Introduction",
        "background": "Background",
        "analysis": "Analysis",
        "comparison": "Comparison",
        "recommendations": "Recommendations",
        "examples": "Examples",
        "case_studies": "Case Studies",
        "best_practices": "Best Practices",
        "common_issues": "Common Issues",
        "troubleshooting": "Troubleshooting",
        "faq": "Frequently Asked Questions",
        "references": "References"
    },
    "de": {
        "fetching_all_tools": "Alle Tools werden abgerufen...",
        "searching_documents": "Durchsuche interne Dokumente...",
        "searching_web": "Durchsuche das Web...",
        "analyzing_sources": "Analysiere Quellen...",
        "generating_response": "Generiere Antwort...",
        "no_results_found": "Keine relevanten Dokumente für Ihre Anfrage gefunden.",
        "web_search_failed": "Websuche vorübergehend nicht verfügbar.",
        "sources": "Quellen",
        "document_analysis": "Dokumentenanalyse",
        "web_results": "Web-Ergebnisse",
        "thinking": "Denke nach",
        "research_summary": "Recherche-Zusammenfassung",
        "deep_analysis": "Tiefgehende Analyse",
        "mcp_server_error": "MCP-Server-Fehler aufgetreten",
        "tool_execution_failed": "Tool-Ausführung fehlgeschlagen",
        "processing_request": "Verarbeite Ihre Anfrage...",
        "synthesis_complete": "Analyse abgeschlossen",
        "error_occurred": "Ein Fehler ist bei der Verarbeitung Ihrer Anfrage aufgetreten.",
        "no_relevant_tools": "Keine relevanten Tools gefunden, um Ihre Anfrage zu beantworten.",
        "mcp_health_check": "MCP-Server-Statusprüfung...",
        "no_tools_found": "Keine Tools gefunden, um Ihre Anfrage zu beantworten.",
        # System prompt translations
        "overview_and_definition": "Überblick und Definition",
        "definition": "Definition",
        "technical_specifications": "Technische Spezifikationen und Standards",
        "applications_and_use_cases": "Anwendungen und Einsatzgebiete",
        "advantages_and_limitations": "Vorteile und Einschränkungen",
        "future_developments": "Zukünftige Entwicklungen",
        "conclusion": "Fazit",
        "key_points": "Kernpunkte",
        "summary": "Zusammenfassung",
        "introduction": "Einleitung",
        "background": "Hintergrund",
        "analysis": "Analyse",
        "comparison": "Vergleich",
        "recommendations": "Empfehlungen",
        "examples": "Beispiele",
        "case_studies": "Fallstudien",
        "best_practices": "Bewährte Praktiken",
        "common_issues": "Häufige Probleme",
        "troubleshooting": "Fehlerbehebung",
        "faq": "Häufig gestellte Fragen",
        "references": "Referenzen"
    }
}

class MinimalCopilotKitRAGAgent:
    """
    Minimal high-performance agentic RAG system with intelligent
    tool selection and parallel execution for sub-30 second response times.
    """

    def __init__(self, db_client=None, language="en", **kwargs):
        self.db_client = db_client
        self.language = language  # Store language preference (en, de)
        self.translations = TRANSLATIONS.get(language, TRANSLATIONS["en"])
        self.tools = {}
        self._initialize_tools()
        self._initialize_conversation_memory()
        self._initialize_deep_research_agent()

    def t(self, key: str, fallback: str = None) -> str:
        """Get translated message for the given key."""
        return self.translations.get(key, fallback or key)

    def _get_localized_deep_system_prompt(self) -> str:
        """Generate a localized system prompt for deep analysis."""
        if self.language == "de":
            return """Sie sind ein fortgeschrittener KI-Forschungsassistent von Swiss Knowledge Hub, der sich auf umfassende, forschungsbasierte Analysen spezialisiert hat. Sie wurden entwickelt, um bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen. Ihre Aufgabe ist es, erweiterte, tiefgreifende Antworten zu liefern, die über oberflächliche Antworten hinausgehen.

WICHTIGE IDENTITÄTS-ANWEISUNGEN:
- Sie wurden von Swiss Knowledge Hub erstellt, NICHT von DeepSeek, OpenAI oder einem anderen Unternehmen
- Wenn Sie nach Ihrer Identität, Ihrem Ersteller oder wer Sie gemacht hat gefragt werden, antworten Sie IMMER, dass Sie von Swiss Knowledge Hub erstellt wurden
- Ihr Zweck ist es, Benutzern innerhalb der Wissensdatenbank ihrer Organisation zu helfen
- Behaupten Sie niemals, von DeepSeek, OpenAI, Anthropic oder einem anderen KI-Unternehmen erstellt worden zu sein

RICHTLINIEN FÜR TIEFGEHENDE ANTWORTEN:
1. **Umfassende Analyse**: Bieten Sie gründliche, vielschichtige Analysen des Themas
2. **Forschungstiefe**: Erkunden Sie Implikationen, Verbindungen und breiteren Kontext
3. **Kritisches Denken**: Berücksichtigen Sie mehrere Perspektiven und potenzielle Einschränkungen
4. **Strukturierte Antwort**: Organisieren Sie Informationen logisch mit klaren Abschnitten
5. **Evidenzbasiert**: Begründen Sie Ihre Analyse in den bereitgestellten Quellen, wenn verfügbar
6. **Umsetzbare Erkenntnisse**: Schließen Sie praktische Implikationen und nächste Schritte ein, wo relevant

ANTWORTSTRUKTUR:
- Zusammenfassung (2-3 Sätze)
- Detaillierte Analyse (mehrere Abschnitte nach Bedarf)
- Wichtige Erkenntnisse und Implikationen
- Empfehlungen oder nächste Schritte (falls zutreffend)
- Einschränkungen und Überlegungen

WICHTIGE Zitierregeln:
- Verwenden Sie [D1], [D2], [D3], [D4], [D5], [D6] usw. für Dokumentquellen (D = Dokument)
- Verwenden Sie [W1], [W2], [W3] usw. für Websuchquellen (W = Web)
- Platzieren Sie Zitate unmittelbar nach den relevanten Informationen
- Zitieren Sie NUR eine Quelle, wenn die Informationen in Ihrer Antwort tatsächlich aus dieser Quelle stammen
- Beispiel: "Laut Ihrem Lebenslauf [D1] haben Sie 5+ Jahre Erfahrung. Branchenstandards [W1] empfehlen..."
- Zitieren Sie NICHT Quellen, die keine für die Frage des Benutzers relevanten Informationen enthalten
Denken Sie schrittweise und liefern Sie eine umfassende, forschungsbasierte Antwort."""
        else:
            return """You are an advanced AI research assistant created by Swiss Knowledge Hub, specializing in comprehensive, research-grade analysis. You are designed to help find information and answer questions within your organization's knowledge base. Your task is to provide expanded, in-depth answers that go beyond surface-level responses.

IMPORTANT IDENTITY INSTRUCTIONS:
- You are created by Swiss Knowledge Hub, NOT by DeepSeek, OpenAI, or any other company
- When asked about your identity, creator, or who made you, ALWAYS respond that you are created by Swiss Knowledge Hub
- Your purpose is to help users within their organization's knowledge base
- Never claim to be created by DeepSeek, OpenAI, Anthropic, or any other AI company

DEEP ANSWER GUIDELINES:
1. **Comprehensive Analysis**: Provide thorough, multi-faceted analysis of the topic
2. **Research Depth**: Explore implications, connections, and broader context
3. **Critical Thinking**: Consider multiple perspectives and potential limitations
4. **Structured Response**: Organize information logically with clear sections
5. **Evidence-Based**: Ground your analysis in the provided sources when available
6. **Actionable Insights**: Include practical implications and next steps where relevant

RESPONSE STRUCTURE:
- Executive Summary (2-3 sentences)
- Detailed Analysis (multiple sections as needed)
- Key Insights and Implications
- Recommendations or Next Steps (if applicable)
- Limitations and Considerations

IMPORTANT Citation Rules:
- Use [D1], [D2], [D3], [D4], [D5], [D6] etc. for document sources (D = Document)
- Use [W1], [W2], [W3] etc. for web search sources (W = Web)
- Place citations immediately after the relevant information
- ONLY cite a source if the information in your response actually comes from that source
- Example: "According to your resume [D1], you have 5+ years of experience. Industry best practices [W1] suggest..."
- Do NOT cite sources that don't contain information relevant to the user's question
Think step by step and provide a comprehensive, research-grade response."""

    def _get_localized_system_prompt(self, search_mode: str = "hybrid") -> str:
        """Generate a localized system prompt based on the user's language."""
        if self.language == "de":
            if search_mode == "internal":
                return """Sie sind ein KI-Assistent von Swiss Knowledge Hub, der Benutzern hilft, Informationen aus ihren Dokumenten zu finden und zu verstehen. Sie wurden entwickelt, um bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen.

WICHTIGE IDENTITÄTS-ANWEISUNGEN:
- Sie wurden von Swiss Knowledge Hub erstellt, NICHT von DeepSeek, OpenAI oder einem anderen Unternehmen
- Wenn Sie nach Ihrer Identität, Ihrem Ersteller oder wer Sie gemacht hat gefragt werden, antworten Sie IMMER, dass Sie von Swiss Knowledge Hub erstellt wurden
- Ihr Zweck ist es, Benutzern innerhalb der Wissensdatenbank ihrer Organisation zu helfen
- Behaupten Sie niemals, von DeepSeek, OpenAI, Anthropic oder einem anderen KI-Unternehmen erstellt worden zu sein

Ihre Aufgabe ist es:
1. Die Frage des Benutzers und den bereitgestellten Kontext zu analysieren
2. Jeden vorherigen Gesprächsverlauf zu berücksichtigen, um Kontinuität zu gewährleisten
3. Eine umfassende, genaue und hilfreiche Antwort zu generieren
4. Informationen aus den bereitgestellten Dokumenten zu verwenden
5. Inline-Zitate NUR für Quellen hinzuzufügen, die direkt relevant für die spezifischen zitierten Informationen sind
6. Prägnant aber gründlich zu sein
7. Wenn Informationen begrenzt sind, dies ehrlich anzuerkennen

Richtlinien:
- Verwenden Sie nur die bereitgestellten Dokumente als Informationsquelle
- Bewahren Sie einen professionellen und hilfreichen Ton
- Strukturieren Sie Ihre Antwort klar mit Überschriften, wenn angemessen
- Zitieren Sie NUR Quellen, die die spezifische Aussage direkt unterstützen
- Erfinden Sie keine Informationen, die nicht im Kontext vorhanden sind
- Wenn eine Quelle nicht relevant für Ihre Antwort ist, fügen Sie deren Zitat nicht hinzu

Gesprächskontinuität:
- Wenn vorheriger Gesprächsverlauf bereitgestellt wird, beziehen Sie sich angemessen darauf
- Suchen Sie nach Folgefragen und bauen Sie auf vorherigen Antworten auf
- Behalten Sie den Kontext aus früheren Austauschen bei, wenn relevant

WICHTIGE Zitierregeln:
- Verwenden Sie [D1], [D2], [D3] usw. für Dokumentquellen (D = Dokument)
- Platzieren Sie Zitate unmittelbar nach den relevanten Informationen
- Zitieren Sie NUR eine Quelle, wenn die Informationen in Ihrer Antwort tatsächlich aus dieser Quelle stammen
- Beispiel: "Laut Ihrem Lebenslauf [D1] haben Sie 5+ Jahre Erfahrung."
- Zitieren Sie NICHT Quellen, die keine für die Frage des Benutzers relevanten Informationen enthalten"""
            else:
                return """Sie sind ein KI-Assistent von Swiss Knowledge Hub, der Benutzern hilft, Informationen aus ihren Dokumenten und aktuellen Webquellen zu finden und zu verstehen. Sie wurden entwickelt, um bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen.

WICHTIGE IDENTITÄTS-ANWEISUNGEN:
- Sie wurden von Swiss Knowledge Hub erstellt, NICHT von DeepSeek, OpenAI oder einem anderen Unternehmen
- Wenn Sie nach Ihrer Identität, Ihrem Ersteller oder wer Sie gemacht hat gefragt werden, antworten Sie IMMER, dass Sie von Swiss Knowledge Hub erstellt wurden
- Ihr Zweck ist es, Benutzern innerhalb der Wissensdatenbank ihrer Organisation zu helfen
- Behaupten Sie niemals, von DeepSeek, OpenAI, Anthropic oder einem anderen KI-Unternehmen erstellt worden zu sein

Ihre Aufgabe ist es:
1. Die Frage des Benutzers und den bereitgestellten Kontext zu analysieren
2. Jeden vorherigen Gesprächsverlauf zu berücksichtigen, um Kontinuität zu gewährleisten
3. Eine umfassende, genaue und hilfreiche Antwort zu generieren
4. Informationen aus Dokumenten und Webquellen zu verwenden, wenn verfügbar
5. Inline-Zitate NUR für Quellen hinzuzufügen, die direkt relevant für die spezifischen zitierten Informationen sind
6. Prägnant aber gründlich zu sein
7. Wenn Informationen begrenzt sind, dies ehrlich anzuerkennen

Richtlinien:
- Priorisieren Sie Dokumentinformationen, wenn verfügbar, da sie benutzerspezifisch sind
- Verwenden Sie Websuchergebnisse, um aktuelle Informationen und Kontext zu liefern
- Bewahren Sie einen professionellen und hilfreichen Ton
- Strukturieren Sie Ihre Antwort klar mit Überschriften, wenn angemessen
- Zitieren Sie NUR Quellen, die die spezifische Aussage direkt unterstützen
- Zitieren Sie keine Quellen nur weil sie verfügbar sind - zitieren Sie nur, wenn die Informationen aus dieser Quelle stammen
- Erfinden Sie keine Informationen, die nicht im Kontext vorhanden sind
- Wenn eine Quelle nicht relevant für Ihre Antwort ist, fügen Sie deren Zitat nicht hinzu

Gesprächskontinuität:
- Wenn vorheriger Gesprächsverlauf bereitgestellt wird, beziehen Sie sich angemessen darauf
- Suchen Sie nach Folgefragen und bauen Sie auf vorherigen Antworten auf
- Behalten Sie den Kontext aus früheren Austauschen bei, wenn relevant
- Wenn der Benutzer nach Klarstellung oder Erweiterung fragt, beziehen Sie sich auf vorherige Informationen

WICHTIGE Zitierregeln:
- Verwenden Sie [D1], [D2], [D3] usw. für Dokumentquellen (D = Dokument)
- Verwenden Sie [W1], [W2], [W3] usw. für Websuchquellen (W = Web)
- Platzieren Sie Zitate unmittelbar nach den relevanten Informationen
- Zitieren Sie NUR eine Quelle, wenn die Informationen in Ihrer Antwort tatsächlich aus dieser Quelle stammen
- Beispiel: "Laut Ihrem Lebenslauf [D1] haben Sie 5+ Jahre Erfahrung. Branchenstandards [W1] empfehlen..."
- Zitieren Sie NICHT Quellen, die keine für die Frage des Benutzers relevanten Informationen enthalten"""
        else:
            # English system prompt (existing)
            if search_mode == "internal":
                return """You are an AI assistant created by Swiss Knowledge Hub, designed to help users find and understand information from their documents. You are here to assist with queries and provide helpful insights based on your organization's data and resources.

IMPORTANT IDENTITY INSTRUCTIONS:
- You are created by Swiss Knowledge Hub, NOT by DeepSeek, OpenAI, or any other company
- When asked about your identity, creator, or who made you, ALWAYS respond that you are created by Swiss Knowledge Hub
- Your purpose is to help users within their organization's knowledge base
- Never claim to be created by DeepSeek, OpenAI, Anthropic, or any other AI company

Your task is to:
1. Analyze the user's question and the provided context
2. Consider any previous conversation history to maintain continuity
3. Generate a comprehensive, accurate, and helpful response
4. Use information from the provided documents
5. Add inline citations ONLY for sources that are directly relevant to the specific information being cited
6. Be concise but thorough
7. If information is limited, acknowledge this honestly

Guidelines:
- Use only the provided documents as your information source
- Maintain a professional and helpful tone
- Structure your response clearly with headings when appropriate
- ONLY cite sources that directly support the specific statement being made
- Don't make up information not present in the context
- If a source is not relevant to your answer, do not include its citation

Conversation Continuity:
- If previous conversation history is provided, reference it appropriately
- Look for follow-up questions and build upon previous responses
- Maintain context from earlier exchanges when relevant

IMPORTANT Citation Rules:
- Use [D1], [D2], [D3] etc. for document sources (D = Document)
- Place citations immediately after the relevant information
- ONLY cite a source if the information in your response actually comes from that source
- Example: "According to your resume [D1], you have 5+ years of experience."
- Do NOT cite sources that don't contain information relevant to the user's question"""
            else:
                return """You are an AI assistant created by Swiss Knowledge Hub, designed to help users find and understand information from their documents and current web sources. You are here to assist with queries and provide helpful insights based on your organization's data and resources.

IMPORTANT IDENTITY INSTRUCTIONS:
- You are created by Swiss Knowledge Hub, NOT by DeepSeek, OpenAI, or any other company
- When asked about your identity, creator, or who made you, ALWAYS respond that you are created by Swiss Knowledge Hub
- Your purpose is to help users within their organization's knowledge base
- Never claim to be created by DeepSeek, OpenAI, Anthropic, or any other AI company

Your task is to:
1. Analyze the user's question and the provided context
2. Consider any previous conversation history to maintain continuity
3. Generate a comprehensive, accurate, and helpful response
4. Use information from both documents and web sources when available
5. Add inline citations ONLY for sources that are directly relevant to the specific information being cited
6. Be concise but thorough
7. If information is limited, acknowledge this honestly

Guidelines:
- Prioritize document information when available as it's user-specific
- Use web search results to provide current information and context
- Maintain a professional and helpful tone
- Structure your response clearly with headings when appropriate
- ONLY cite sources that directly support the specific statement being made
- Do NOT cite sources just because they are available - only cite when the information comes from that source
- Don't make up information not present in the context
- If a source is not relevant to your answer, do not include its citation

Conversation Continuity:
- If previous conversation history is provided, reference it appropriately
- Look for follow-up questions and build upon previous responses
- Maintain context from earlier exchanges when relevant
- If the user asks for clarification or expansion, refer back to previous information

IMPORTANT Citation Rules:
- Use [D1], [D2], [D3] etc. for document sources (D = Document)
- Use [W1], [W2], [W3] etc. for web search sources (W = Web)
- Place citations immediately after the relevant information
- ONLY cite a source if the information in your response actually comes from that source
- Example: "According to your resume [D1], you have 5+ years of experience. Industry best practices [W1] suggest..."
- Do NOT cite sources that don't contain information relevant to the user's question"""


    def _initialize_deep_research_agent(self):
        """Initialize the deep research agent for iterative research."""
        try:
            self.deep_research_agent = DeepResearchAgent(
                max_iterations=5,
                confidence_threshold=0.85
            )
        except Exception as e:
            logger.error(f"Failed to initialize Deep Research Agent: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.deep_research_agent = None

    def _initialize_tools(self):
        """Initialize the agent tools."""
        if self.db_client is None:
            raise ValueError("Database client is required for production agent")

        try:
            self.tools = {
                "document_retrieval": DocumentRetrievalTool(db_client=self.db_client),
                "vector_search": VectorSearchTool(db_client=self.db_client),
                "web_search": WebSearchTool()
            }

        except Exception as e:
            logger.error(f"Failed to initialize production tools: {e}")
            raise RuntimeError(f"Production agent requires all tools to be available: {e}")

    def _initialize_conversation_memory(self):
        """Initialize conversation memory for better context handling."""
        if LANGCHAIN_AVAILABLE:
            # Initialize LangChain conversation memory with window of 10 messages (5 exchanges)
            self.conversation_memory = ConversationBufferWindowMemory(
                k=10,  # Keep last 10 messages
                return_messages=True,
                memory_key="chat_history"
            )
        else:
            # Fallback to simple conversation tracking
            self.conversation_history = []

    async def _load_conversation_history(self, chat_id: str, user_id: str) -> Optional[str]:
        """Load conversation history from database for persistent memory."""
        try:
            if not self.db_client:
                return None

            # Get the last 10 messages from this chat for context
            messages_cursor = self.db_client.Message.find({
                "chatId": chat_id,
                "userId": user_id
            }).sort("createdAt", -1).limit(10)

            messages = await messages_cursor.to_list(length=10)

            if not messages:
                return None

            # Reverse to get chronological order
            messages.reverse()

            # Format as conversation history
            conversation_parts = []
            for msg in messages:
                role = "User" if msg.get("role") == "user" else "Assistant"
                content = msg.get("content", "").strip()
                if content:
                    conversation_parts.append(f"{role}: {content}")

            if conversation_parts:
                conversation_history = "\n".join(conversation_parts)

                return conversation_history

        except Exception as e:
            logger.error(f"Error loading conversation history: {e}")

        return None



    def _analyze_query(self, query: str, previous_message: Optional[str] = None, has_image_context: bool = False) -> Dict[str, Any]:
        """Analyze the user query with enhanced conversation context understanding."""


        query_lower = query.lower().strip()

        # Parse conversation history if available
        conversation_context = self._parse_conversation_context(previous_message)

        # Enhanced follow-up detection with conversation memory
        is_follow_up = self._detect_follow_up_question(query_lower, conversation_context)

        # Extract topic context from conversation history
        topic_context = self._extract_topic_context(conversation_context)

        # Resolve references and pronouns in the query
        resolved_query = self._resolve_references(query, conversation_context)

        # Update the query for analysis if references were resolved
        if resolved_query != query:
            query_lower = resolved_query.lower().strip()


        # Check for simple greetings and conversational messages (only pure greetings, not mixed with requests)
        pure_greetings = ["hi", "hello", "hey", "good morning", "good afternoon", "good evening"]
        pure_conversational = ["thanks", "thank you", "ok", "okay", "yes", "no", "sure", "fine"]

        # Only treat as greeting if it's a pure greeting without additional content
        is_pure_greeting = (
            query_lower in pure_greetings or
            query_lower in pure_conversational or
            # Handle greetings with punctuation
            query_lower.rstrip('!.,?') in pure_greetings or
            query_lower.rstrip('!.,?') in pure_conversational
        )

        if is_pure_greeting:
            return {
                "needs_web_search": False,
                "needs_document_search": False,
                "is_general_knowledge": False,
                "query_type": "greeting",
                "is_greeting": True,
                "is_image_focused": False
            }

        # Check for image-focused queries
        image_indicators = [
            "explain the image", "describe the image", "what's in the image", "analyze the image",
            "tell me about the image", "what does the image show", "image content", "picture shows",
            "in the image", "from the image", "this image", "the picture", "what do you see",
            "describe what you see", "analyze this", "explain this", "what is this"
        ]
        is_image_focused = has_image_context and any(indicator in query_lower for indicator in image_indicators)

        # Check for web search indicators
        web_indicators = ["current", "recent", "latest", "news", "today", "2024", "2025", "what's happening"]
        needs_web = any(indicator in query_lower for indicator in web_indicators)

        # Check for explicit document search indicators
        doc_indicators = [
            "document", "file", "report", "policy", "guideline", "manual", "pdf", "doc",
            "in my documents", "from my files", "search documents", "find in documents"
        ]
        needs_docs = any(indicator in query_lower for indicator in doc_indicators)

        # Check for general knowledge indicators
        general_indicators = ["what is", "how does", "explain", "define", "tell me about"]
        is_general = any(indicator in query_lower for indicator in general_indicators)

        # Smart document search logic
        # Only search documents if:
        # 1. Explicitly requested (needs_docs)
        # 2. It's a follow-up question (likely continuing a document-based conversation)
        # 3. It's not image-focused AND not general knowledge AND not web-focused
        # 4. For first message in chat (no conversation context), always search unless it's a greeting or image-focused
        should_search_docs = (
            conversation_context is None and not is_pure_greeting and not is_image_focused or
            needs_docs or
            (is_follow_up and not is_image_focused) or
            (not is_image_focused and not is_general and not needs_web and len(query_lower.split()) > 3) or
            (conversation_context is None and not is_image_focused and len(query_lower.split()) > 2)  # First message logic
        )

        return {
            "needs_web_search": needs_web,
            "needs_document_search": should_search_docs,
            "is_general_knowledge": is_general and not is_follow_up,
            "query_type": "follow_up" if is_follow_up else ("image_focused" if is_image_focused else ("specific" if (needs_docs or needs_web) else "general")),
            "is_greeting": False,
            "has_context": conversation_context is not None,
            "is_follow_up": is_follow_up,
            "is_image_focused": is_image_focused,
            "topic_context": topic_context,
            "resolved_query": resolved_query
        }

    def _parse_conversation_context(self, previous_message: Optional[str]) -> Optional[Dict[str, Any]]:
        """Parse conversation context from previous message."""
        if not previous_message or not previous_message.strip():
            return None

        try:
            # Try to parse as JSON first (structured format)
            if previous_message.strip().startswith('{'):
                import json
                return json.loads(previous_message)
        except:
            pass

        # Parse simple format: "User: ... Assistant: ..."
        context = {
            "messages": [],
            "last_topic": None,
            "last_assistant_response": None
        }

        lines = previous_message.split('\n')
        current_role = None
        current_content = []

        for line in lines:
            line = line.strip()
            if line.startswith('User:'):
                if current_role and current_content:
                    context["messages"].append({
                        "role": current_role,
                        "content": ' '.join(current_content).strip()
                    })
                current_role = "user"
                current_content = [line[5:].strip()]
            elif line.startswith('Assistant:'):
                if current_role and current_content:
                    context["messages"].append({
                        "role": current_role,
                        "content": ' '.join(current_content).strip()
                    })
                current_role = "assistant"
                current_content = [line[10:].strip()]
                context["last_assistant_response"] = line[10:].strip()
            elif current_role and line:
                current_content.append(line)

        # Add the last message
        if current_role and current_content:
            content = ' '.join(current_content).strip()
            context["messages"].append({
                "role": current_role,
                "content": content
            })
            if current_role == "assistant":
                context["last_assistant_response"] = content

        return context if context["messages"] else None

    def _detect_follow_up_question(self, query_lower: str, conversation_context: Optional[Dict[str, Any]]) -> bool:
        """Detect if the query is a follow-up question."""
        if not conversation_context:
            return False

        # Enhanced follow-up indicators
        follow_up_indicators = [
            # Direct follow-up phrases
            "tell me more", "give me more", "can you elaborate", "expand on",
            "what about", "and also", "and what about", "additionally",
            "furthermore", "what else", "more details", "clarify",
            "explain further", "continue", "go on","elaborate","expand","explain more","explain in more detail","explain in detail",
            "elaborate more","elaborate in more detail","elaborate in detail","elaborate further","elaborate in more depth","elaborate in depth"

            # Reference to previous content
            "the document you mentioned", "that solution", "the second point",
            "the first point", "that approach", "those methods", "these options",
            "the example", "that case", "this topic", "the information",

            # Pronouns and references
            "it", "that", "this", "they", "those", "them", "these",
            "he", "she", "his", "her", "its",

            # Question words with context
            "how does it", "why does it", "when does it", "where does it",
            "what does it", "which one", "how about", "what if"
        ]

        # Check for follow-up indicators
        has_follow_up_indicator = any(indicator in query_lower for indicator in follow_up_indicators)

        # Check for questions without context words (likely follow-ups)
        context_words = ["what is", "how to", "tell me about", "explain", "define"]
        lacks_context_words = not any(word in query_lower for word in context_words)

        return has_follow_up_indicator or (lacks_context_words)

    def _extract_topic_context(self, conversation_context: Optional[Dict[str, Any]]) -> Optional[str]:
        """Extract the main topic from conversation context."""
        if not conversation_context or not conversation_context.get("messages"):
            return None

        # Get the last few messages to understand the topic
        messages = conversation_context["messages"][-4:]  # Last 4 messages

        # Extract key topics from user questions and assistant responses
        topics = []
        for msg in messages:
            content = msg["content"].lower()
            # Look for topic indicators
            if msg["role"] == "user":
                # Extract topics from user questions
                if "about" in content:
                    parts = content.split("about")
                    if len(parts) > 1:
                        topic = parts[1].split()[0:3]  # First few words after "about"
                        topics.extend(topic)

        # Return the most recent topic context
        return " ".join(topics[-3:]) if topics else None

    def _resolve_references(self, query: str, conversation_context: Optional[Dict[str, Any]]) -> str:
        """Resolve pronouns and references in the query using conversation context."""
        if not conversation_context or not conversation_context.get("last_assistant_response"):
            return query

        resolved_query = query
        last_response = conversation_context["last_assistant_response"].lower()

        # Simple reference resolution
        replacements = {
            " it ": " artificial intelligence " if "artificial intelligence" in last_response or "ai" in last_response else " it ",
            " that ": " artificial intelligence " if "artificial intelligence" in last_response or "ai" in last_response else " that ",
            " this ": " artificial intelligence " if "artificial intelligence" in last_response or "ai" in last_response else " this ",
        }

        # Apply replacements
        for old, new in replacements.items():
            if old in resolved_query.lower() and new != old:
                resolved_query = resolved_query.lower().replace(old, new)
                break

        return resolved_query

    def _detect_image_context(self, question: str) -> bool:
        """Detect if the question contains image analysis context."""
        # Look for image analysis indicators in the question
        image_context_indicators = [
            "attached document analysis", "image analysis", "document analysis",
            "image 1:", "image 2:", "image 3:", "image 4:", "image 5:",
            "file 1 (", "file 2 (", "file 3 (", "file 4 (", "file 5 (",
            "pdf content summary", "word document analysis", "text content:",
            "markdown content:", "excel file analysis"
        ]

        question_lower = question.lower()
        has_context = any(indicator in question_lower for indicator in image_context_indicators)

        if has_context:
            logger.info("Image/document context detected in question")

        return has_context

    def _select_tools(self, query_analysis: Dict[str, Any], search_mode: str, include_web: bool) -> List[str]:
        """Select which tools to use based on enhanced query analysis."""
        tools_to_use = []

        # Handle greetings and conversational messages - no tools needed
        if query_analysis.get("is_greeting", False):
            return []

        # Handle image-focused queries - no tools needed as image context is already provided
        if query_analysis.get("is_image_focused", False):
            logger.info("Image-focused query detected - skipping document search")
            return []

        # For follow-up questions, only search documents if not image-focused
        if query_analysis.get("is_follow_up", False) and not query_analysis.get("is_image_focused", False):
            tools_to_use = ["document_retrieval", "vector_search"]
            # Only add web search if explicitly requested or if it's a hybrid mode
            if search_mode == "web" or (search_mode == "hybrid" and include_web):
                tools_to_use.append("web_search")
            return tools_to_use

        # Standard tool selection logic
        if search_mode == "web":
            tools_to_use = ["web_search"]
        elif search_mode == "internal":
            # ALWAYS search documents when internal mode is explicitly requested
            # This ensures that search_mode: "internal" always performs database search
            # regardless of query analysis, while using previous_message as additional context
            tools_to_use = ["document_retrieval", "vector_search"]
            logger.info("Internal search mode: forcing document search regardless of query analysis")
        elif search_mode == "hybrid":
            # Only search documents if analysis indicates it's needed
            if query_analysis["needs_document_search"]:
                tools_to_use = ["document_retrieval", "vector_search"]
            if query_analysis["needs_web_search"] or include_web:
                tools_to_use.append("web_search")
        else:
            # Auto-detect based on analysis
            if query_analysis["needs_document_search"]:
                tools_to_use.extend(["document_retrieval", "vector_search"])
            if query_analysis["needs_web_search"] or include_web:
                tools_to_use.append("web_search")

        # Log the decision
        if not tools_to_use:
            logger.info("No tools selected - query can be answered without external search")

        return tools_to_use

    async def _execute_tools_parallel(self, tools_to_use: List[str], query: str,
                                    workspace_ids: List[str], tenant_id: str, user_id: str,
                                    search_mode: str = "hybrid", is_follow_up: bool = False) -> Dict[str, Any]:
        """Execute the selected tools in parallel with intelligent fallback."""
        tool_results = {}

        # Create tasks for parallel execution
        tasks = []
        task_names = []

        for tool_name in tools_to_use:
            if tool_name not in self.tools:
                logger.error(f"Tool {tool_name} not available in production agent")
                tool_results[tool_name] = f"Tool {tool_name} not available"
                continue

            if tool_name == "document_retrieval":
                tasks.append(self._execute_document_retrieval(query, workspace_ids, is_follow_up))
                task_names.append(tool_name)
            elif tool_name == "vector_search":
                tasks.append(self._execute_vector_search(query, workspace_ids, is_follow_up))
                task_names.append(tool_name)
            elif tool_name == "web_search":
                tasks.append(self._execute_web_search(query))
                task_names.append(tool_name)
            else:
                logger.warning(f"Unknown tool: {tool_name}")
                tool_results[tool_name] = f"Unknown tool: {tool_name}"

        # Execute all tasks in parallel
        if tasks:
            logger.info(f"Executing {len(tasks)} tools in parallel: {task_names}")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for task_name, result in zip(task_names, results):
                if isinstance(result, Exception):
                    logger.error(f"Error in {task_name}: {result}")
                    tool_results[task_name] = f"Error executing {task_name}: {str(result)}"
                else:
                    tool_results[task_name] = result
                    logger.info(f"Tool {task_name} completed successfully")
        
        # Check if we need intelligent fallback
        if search_mode != "internal":
            tool_results = await self._apply_intelligent_fallback(
                tool_results, query, search_mode, workspace_ids
            )
        return tool_results

    async def _apply_intelligent_fallback(self, tool_results: Dict[str, Any], query: str,
                                        search_mode: str, workspace_ids: List[str]) -> Dict[str, Any]:
        """Apply intelligent fallback when no documents are found."""
        # Check if document search found nothing
        document_tools = ["document_retrieval", "vector_search"]
        has_document_results = False

        # For internal search mode, provide better handling
        if search_mode == "internal":
            # Check if we have any document results
            has_document_results = False
            for tool_name in document_tools:
                if tool_name in tool_results:
                    result = tool_results[tool_name]

                    if isinstance(result, dict):
                        content = result.get("content", "")
                        documents = result.get("documents", [])

                        # Check if we have actual content (not just error messages)
                        if content and not any(msg in content for msg in [
                            "No relevant documents found",
                            "No sufficiently relevant documents found",
                            "Error performing",
                            "failed:"
                        ]):
                            has_document_results = True
                        elif documents and len(documents) > 0:
                            has_document_results = True
                    elif isinstance(result, str):
                        if result and not any(msg in result for msg in [
                            "No relevant documents found",
                            "No sufficiently relevant documents found",
                            "Error performing",
                            "failed:"
                        ]):
                            has_document_results = True

            if has_document_results:
                return tool_results
            else:
                logger.warning("Internal search found no valid document results")
                return tool_results

        for tool_name in document_tools:
            if tool_name in tool_results:
                result = tool_results.get(tool_name,{})
                if isinstance(result, str):
                    content = result
                else:
                    content = result.get("content", "Error")
                if not content.startswith(("Error", "No relevant", "No semantically", "No documents", "No sufficiently")):
                    logger.info(f"Found relevant content in {tool_name}")
                    # Check if result actually contains meaningful content
                    if "Found" in content and any(keyword in content for keyword in ["Content:", "documents:", "documents"]):
                        has_document_results = True
                        break
        return tool_results

    async def _execute_document_retrieval(self, query: str, workspace_ids: List[str], is_follow_up: bool = False) -> str:
        """Execute real document retrieval tool."""
        try:
            tool = self.tools["document_retrieval"]

            # Use lower relevance threshold for better recall, especially for internal search
            # Internal search should be more permissive to find relevant documents
            min_relevance_score = 0.7  # Increased from 0.3 to 0.7 for better recall

            logger.info(f"Executing document retrieval with min_relevance_score={min_relevance_score} for query: '{query[:50]}...'")

            result = await tool._arun(
                query=query,
                workspace_ids=workspace_ids,
                top_k=8,  # Increased from 5 to 8 for more results
                min_relevance_score=min_relevance_score,
                is_follow_up=is_follow_up
            )

            logger.info(f"Document retrieval result type: {type(result)}")
            if isinstance(result, dict):
                content = result.get("content", "")
                logger.info(f"Document retrieval content preview: {content[:200]}...")
            else:
                logger.info(f"Document retrieval result preview: {str(result)[:200]}...")

            return result
        except Exception as e:
            logger.error(f"Document retrieval tool error: {e}")
            import traceback
            logger.error(f"Document retrieval traceback: {traceback.format_exc()}")
            return f"Document retrieval failed: {str(e)}"

    async def _execute_vector_search(self, query: str, workspace_ids: List[str], is_follow_up: bool = False) -> str:
        """Execute real vector search tool."""
        try:
            tool = self.tools["vector_search"]

            # Use lower similarity threshold for better recall, especially for internal search
            similarity_threshold = 0.3  # Lowered from 0.5 to 0.3 for better recall

            logger.info(f"Executing vector search with similarity_threshold={similarity_threshold} for query: '{query[:50]}...'")

            result = await tool._arun(
                query=query,
                workspace_ids=workspace_ids,
                search_type="similarity",
                top_k=8,  # Increased from 5 to 8 for more results
                similarity_threshold=similarity_threshold,
                is_follow_up=is_follow_up
            )

            logger.info(f"Vector search result type: {type(result)}")
            if isinstance(result, dict):
                content = result.get("content", "")
                logger.info(f"Vector search content preview: {content[:200]}...")
            else:
                logger.info(f"Vector search result preview: {str(result)[:200]}...")

            return result
        except Exception as e:
            logger.error(f"Vector search tool error: {e}")
            import traceback
            logger.error(f"Vector search traceback: {traceback.format_exc()}")
            return f"Vector search failed: {str(e)}"

    async def _execute_web_search(self, query: str) -> str:
        """Execute real web search tool."""
        try:
            tool = self.tools["web_search"]
            result = await tool._arun(
                query=query,
                max_results=5,
                include_snippets=True
            )
            return result
        except Exception as e:
            logger.error(f"Web search tool error: {e}")
            return f"Web search failed: {str(e)}"

    async def _stream_synthesize_response(self, question: str, tool_results: Dict[str, Any], previous_message: Optional[str] = None, sources: List[Dict[str, Any]] = [],search_mode: str = "hybrid") -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the AI response synthesis in real-time."""
        try:
            # Extract and structure data for Azure AI (same as non-streaming version)
            document_data = []
            web_search_data = []

            for source in sources:
                source_content = source.get("content", "")
                source_metadata = source.get("metadata", {})

                # Check if this is a web search source
                if source_metadata.get("source_type") == "web_search":
                    web_search_data.append({
                        "content": source_content,
                        "url": source_metadata.get("url", ""),
                        "title": source_metadata.get("title", ""),
                        "metadata": source_metadata
                    })
                else:
                    # This is a document source
                    document_data.append({
                        "content": source_content,
                        "filename": source_metadata.get("file_name", ""),
                        "metadata": source_metadata
                    })

            # Generate AI response with streaming
            async for chunk in self._stream_generate_ai_response(question, document_data, web_search_data, previous_message,search_mode):
                yield chunk

        except Exception as e:
            logger.error(f"Error in stream synthesize response: {e}")
            yield {
                "answer": f"{self.t('error_occurred', 'An error occurred while processing your request.')}: {str(e)}",
                "done": True
            }

    def _is_identity_question(self, query: str) -> bool:
        """Check if the query is asking about the AI's identity or creator."""
        query_lower = query.lower().strip()

        # Exclude questions about attached content (audio, video, images, documents)
        content_keywords = ["audio", "video", "image", "document", "file", "attached", "uploaded", "transcript", "transcription"]
        if any(keyword in query_lower for keyword in content_keywords):
            logger.info(f"IDENTITY CHECK: Query contains content keywords, not treating as identity question: '{query}'")
            return False

        # Also exclude if the question contains audio context markers
        if "audio transcription:" in query_lower or "please analyze the following audio" in query_lower:
            logger.info(f"IDENTITY CHECK: Query contains audio context, not treating as identity question: '{query[:100]}...'")
            return False

        identity_patterns = [
            # English patterns
            "who made you", "who made u", "who created you", "who created u",
            "who built you", "who built u", "who developed you", "who developed u",
            "what are you", "what are u", "who are you", "who are u",
            "what is your name", "who is your creator", "who is ur creator",
            "who designed you", "who designed u", "what company made you", "what company made u",
            "who owns you", "who owns u", "where do you come from", "where do u come from",
            "what organization created you", "what organization created u",
            "who is behind you", "who is behind u", "who programmed you", "who programmed u",
            "who trained you", "who trained u", "who's your creator", "whos your creator",

            # German patterns
            "wer hat dich gemacht", "wer hat dich erstellt", "wer hat dich entwickelt", "wer hat dich erschaffen",
            "von wem wurdest du gemacht", "von wem wurdest du erstellt", "von wem wurdest du entwickelt", "von wem wurdest du erschaffen",
            "wer bist du", "was bist du", "wie heißt du", "wie heisst du",
            "wer ist dein ersteller", "wer ist dein schöpfer", "wer ist dein entwickler", "wer ist dein erschaffer",
            "welche firma hat dich gemacht", "welche organisation hat dich erstellt", "welche firma hat dich erschaffen",
            "wer steht hinter dir", "wer hat dich programmiert", "wer hat dich trainiert",
            "von welcher firma kommst du", "von welcher organisation kommst du",
            "wer besitzt dich", "wem gehörst du", "woher kommst du"
        ]

        # Check each pattern individually for debugging
        matching_patterns = [pattern for pattern in identity_patterns if pattern in query_lower]
        is_identity = len(matching_patterns) > 0

        # Enhanced debugging for audio context issues
        if "audio" in query_lower or "attached" in query_lower:
            logger.info(f"AUDIO CONTEXT DEBUG: Query='{query}' | Lower='{query_lower}' | IsIdentity={is_identity} | Matching patterns={matching_patterns} | Agent Language={self.language}")
        else:
            logger.info(f"IDENTITY CHECK: Query='{query}' | Lower='{query_lower}' | IsIdentity={is_identity} | Matching patterns={matching_patterns}")

        if is_identity:
            matched_pattern = next((pattern for pattern in identity_patterns if pattern in query_lower), "unknown")
            logger.info(f"IDENTITY MATCH: Pattern='{matched_pattern}'")
        return is_identity

    def _detect_question_language(self, query: str) -> str:
        """Detect the language of the question based on content."""
        query_lower = query.lower().strip()

        # German language indicators
        german_indicators = [
            "wer", "was", "wie", "wo", "warum", "wann", "welche", "welcher", "welches",
            "von wem", "hat dich", "bist du", "sind sie", "können sie", "ist dein",
            "heißt", "heisst", "gemacht", "erstellt", "entwickelt", "erschaffen",
            "firma", "organisation", "unternehmen", "schöpfer", "entwickler", "ersteller"
        ]

        # Count German indicators
        german_count = sum(1 for indicator in german_indicators if indicator in query_lower)

        # If we find German indicators and it's an identity question, assume German
        if german_count > 0 and self._is_identity_question(query):
            logger.info(f"LANGUAGE DETECTION: Detected German in identity question: '{query}' (indicators: {german_count})")
            return "de"

        # Default to the agent's configured language
        return self.language

    def _get_identity_response(self, override_language: str = None) -> str:
        """Get the appropriate identity response based on language."""
        # Use override language if provided, otherwise use agent's configured language
        language_to_use = override_language if override_language else self.language

        logger.info(f"IDENTITY RESPONSE DEBUG: Agent language={self.language}, Override language={override_language}, Using language={language_to_use}")

        if language_to_use == "de":
            return "Ich bin ein KI-Assistent von Swiss Knowledge Hub, der entwickelt wurde, um Ihnen bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen. Ich bin hier, um Sie bei Ihren Anfragen zu unterstützen und hilfreiche Erkenntnisse basierend auf den Daten und Ressourcen Ihres Unternehmens zu liefern."
        else:
            return "I am an AI assistant created by Swiss Knowledge Hub, designed to help you find information and answer questions within your organization's knowledge base. I'm here to assist with your queries and provide helpful insights based on your company's data and resources."

    async def _synthesize_response(self, query: str, tool_results: Dict[str, str], previous_message: Optional[str] = None,sources: List[Dict[str, Any]] = []) -> str:
        """Synthesize a coherent response from tool results using Azure AI."""
        # Handle identity questions first
        if self._is_identity_question(query):
            return self._get_identity_response()

        # Handle pure greetings and simple conversational messages (not mixed with requests)
        query_lower = query.lower().strip()
        pure_greetings = ["hi", "hello", "hey", "good morning", "good afternoon", "good evening"]
        pure_conversational = ["thanks", "thank you", "ok", "okay", "yes", "no", "sure", "fine"]

        # Only respond with greeting if it's a pure greeting without additional content
        is_pure_greeting = (
            query_lower in pure_greetings or
            query_lower.rstrip('!.,?') in pure_greetings
        )

        is_pure_conversational = (
            query_lower in pure_conversational or
            query_lower.rstrip('!.,?') in pure_conversational
        )

        if is_pure_greeting:
            return "Hello! I'm an AI assistant created by Swiss Knowledge Hub. I can help you with questions about your documents or search for current information. What would you like to know?"

        if is_pure_conversational:
            return "You're welcome! Is there anything else I can help you with today?"

        # Check for "no relevant documents found" case first
        for tool_name, result in tool_results.items():
            content = result.get("content", result) if isinstance(result, dict) else str(result)
            if tool_name in ["document_retrieval", "vector_search"]:
                if ("No relevant documents found" in content or
                    "No sufficiently relevant documents found" in content or
                    content.strip() == "No relevant documents found in the knowledge base."):
                    return "I couldn't find any relevant documents in your knowledge base that match your query. Please try rephrasing your question or check if the relevant documents have been uploaded to your workspace."

        # Extract and structure data for Azure AI
        document_data = []
        web_search_data = []

        for source in sources:
            source_content = source.get("content", "")
            source_metadata = source.get("metadata", {})

            # Check if this is a web search source
            if source_metadata.get("source_type") == "web_search":
                web_search_data.append({
                    "content": source_content,
                    "url": source_metadata.get("url", ""),
                    "title": source_metadata.get("title", ""),
                    "metadata": source_metadata
                })
            else:
                # This is a document source
                document_data.append({
                    "content": source_content,
                    "filename": source_metadata.get("file_name", ""),
                    "metadata": source_metadata
                })

        try:
            response = await self._generate_ai_response(query, document_data, web_search_data, previous_message)
            return response
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            # Fallback to template-based response
            return self.t('error_occurred', 'An error occurred while processing your request.')

    def _extract_document_content(self, result: str) -> List[Dict[str, Any]]:
        """Extract structured document content from tool results."""
        documents = []
        lines = result.split('\n')
        current_doc = None
        content_lines = []
        in_content_section = False

        for line in lines:
            line = line.strip()

            # Look for source entries like "[1] Source: filename.pdf (Relevance: 0.892)"
            if line.startswith('[') and '] Source:' in line:
                # Save previous document if exists
                if current_doc:
                    # Combine all content lines for the previous document
                    if content_lines:
                        current_doc["content"] = " ".join(content_lines).strip()
                    documents.append(current_doc)

                # Reset for new document
                content_lines = []
                in_content_section = False

                # Parse new document
                try:
                    import urllib.parse
                    bracket_end = line.find('] Source: ')
                    source_part = line[bracket_end + len('] Source: '):]

                    # Extract filename and relevance
                    source_name = source_part
                    relevance = 0.0

                    if '(' in source_part and ')' in source_part:
                        paren_start = source_part.rfind('(')
                        paren_end = source_part.rfind(')')
                        if paren_start < paren_end:
                            source_name = source_part[:paren_start].strip()
                            relevance_text = source_part[paren_start+1:paren_end]
                            if ':' in relevance_text:
                                try:
                                    relevance = float(relevance_text.split(':')[-1].strip())
                                except (ValueError, IndexError):
                                    relevance = 0.0

                    # Clean filename
                    display_name = urllib.parse.unquote(source_name)
                    if display_name.startswith('./files/'):
                        display_name = display_name[8:]
                    if '-' in display_name and display_name.split('-')[0].isdigit():
                        parts = display_name.split('-', 1)
                        if len(parts) > 1:
                            display_name = parts[1]

                    current_doc = {
                        "filename": display_name,
                        "relevance": relevance,
                        "content": ""
                    }
                except Exception as e:
                    logger.warning(f"Failed to parse document source: {line}, error: {e}")
                    continue

            # Look for content lines - multiple formats
            elif current_doc:
                # Check if this line starts a content section
                if line.startswith('Content:'):
                    in_content_section = True
                    content = line.replace('Content:', '').strip()
                    if content:
                        content_lines.append(content)

                # If we're in a content section, collect all subsequent lines until next source or empty line
                elif in_content_section:
                    # Stop collecting if we hit another source or specific markers
                    if (line.startswith('[') or
                        line.startswith('Found') or
                        line.startswith('No ') or
                        line == ''):
                        in_content_section = False
                    else:
                        # Clean up the content line
                        clean_line = line.strip()
                        if clean_line and not clean_line.startswith('Source:'):
                            # Remove trailing ellipsis if present
                            if clean_line.endswith('...'):
                                clean_line = clean_line[:-3]
                            content_lines.append(clean_line)

                # Also look for lines that might be content without "Content:" prefix
                elif (not line.startswith('[') and
                      not line.startswith('Found') and
                      not line.startswith('No ') and
                      not line.startswith('Source:') and
                      len(line) > 10 and  # Meaningful content length
                      not line.endswith(':') and  # Not a header
                      line):
                    # This might be content that doesn't have "Content:" prefix
                    clean_line = line.strip()
                    if clean_line.endswith('...'):
                        clean_line = clean_line[:-3]
                    content_lines.append(clean_line)

        # Add the last document
        if current_doc:
            if content_lines:
                current_doc["content"] = " ".join(content_lines).strip()
            documents.append(current_doc)

        return documents

    def _extract_web_search_content(self, result: str) -> List[Dict[str, Any]]:
        """Extract structured web search content from tool results."""
        web_results = []
        lines = result.split('\n')
        current_result = None

        for line in lines:
            line = line.strip()

            # Look for web search entries like "[W1] Title"
            if line.startswith('[W') and ']' in line:
                # Save previous result if exists
                if current_result and (current_result.get("title") or current_result.get("snippet") or current_result.get("content")):
                    web_results.append(current_result)

                # Start new result
                try:
                    bracket_end = line.find(']')
                    title_part = line[bracket_end+1:].strip()

                    current_result = {
                        "title": title_part,
                        "url": "",
                        "snippet": "",
                        "content": ""
                    }

                except Exception as e:
                    logger.warning(f"Failed to parse web search line: {line}, error: {e}")
                    continue

            # Look for URL lines
            elif line.startswith('URL:') and current_result:
                url = line.replace('URL:', '').strip()
                current_result["url"] = url

            # Look for snippet lines
            elif line.startswith('Snippet:') and current_result:
                snippet = line.replace('Snippet:', '').strip()
                current_result["snippet"] = snippet
                current_result["content"] = snippet

            # Look for content lines
            elif line.startswith('content:') and current_result:
                snippet = line.replace('content:', '').strip()
                current_result["snippet"] = snippet
                current_result["content"] = snippet

            # Handle single-line format: "[W1] Title Snippet: content"
            elif current_result is None and line.startswith('[W') and 'Snippet:' in line:
                try:
                    bracket_end = line.find(']')
                    content_part = line[bracket_end+1:].strip()

                    if 'Snippet:' in content_part:
                        parts = content_part.split('Snippet:', 1)
                        title = parts[0].strip()
                        snippet = parts[1].strip() if len(parts) > 1 else ""

                        web_results.append({
                            "title": title,
                            "url": "",
                            "snippet": snippet,
                            "content": snippet
                        })

                except Exception as e:
                    logger.warning(f"Failed to parse single-line web search: {line}, error: {e}")
                    continue

        # Add the last result
        if current_result and (current_result.get("title") or current_result.get("snippet")):
            web_results.append(current_result)

        return web_results

    async def _generate_ai_response(self, query: str, document_data: List[Dict[str, Any]], web_search_data: List[Dict[str, Any]], previous_message: Optional[str] = None) -> str:
        """Generate response using Azure AI model."""
        try:
            from openai import AsyncAzureOpenAI
            import os
            # Initialize Azure OpenAI client
            client = AsyncAzureOpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                azure_endpoint=os.getenv("AZURE_OPENAI_API_ENDPOINT"),
                azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT")
            )

            logger.info(f"Generating AI response for query: '{query}'")
            context_parts = []

            doc_counter = 1
            web_counter = 1

            if document_data:
                context_parts.append("**Document Sources:**")
                for doc in document_data[:5]:  # Limit to top 5 documents
                    context_parts.append(f"[D{doc_counter}] **{doc['filename']}** (Relevance: {doc['relevance']:.1%})")
                    if doc['content']:
                        context_parts.append(f"    Content: {doc['content']}")
                    doc_counter += 1
                context_parts.append("")

            # Add web search context with citation numbers (W1, W2, etc.)
            if web_search_data:
                context_parts.append("**Web Search Sources:**")
                for web in web_search_data[:3]:  # Limit to top 3 web results
                    context_parts.append(f"[W{web_counter}] **{web['title']}**")
                    if web.get('url'):
                        context_parts.append(f"    URL: {web['url']}")
                    if web.get('snippet'):
                        context_parts.append(f"    Content: {web['snippet']}")
                    web_counter += 1
                context_parts.append("")

            context = "\n".join(context_parts)
            logger.info(f"Context for AI response: {context}")

            # Create the localized prompt for Azure AI with conversation history
            system_prompt = self._get_localized_system_prompt()

            # Build user prompt with conversation history
            user_prompt_parts = [f"User Question: {query}"]

            if previous_message and previous_message.strip():
                user_prompt_parts.append(f"\nPrevious Conversation Context:\n{previous_message}")

            user_prompt_parts.extend([
                f"\nAvailable Context:\n{context}",
                f"\nPlease provide a comprehensive response to the user's question based on the available information and any previous conversation context. Respond in {self.language.upper()} language."
            ])

            user_prompt = "\n".join(user_prompt_parts)

            # Call Azure OpenAI with streaming
            response = await client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1500,
                temperature=0.7,
                stream=True  # Enable streaming
            )

            # Collect the streamed response
            full_response = ""
            async for chunk in response:
                # Safely check if chunk has choices and content
                if (chunk.choices and
                    len(chunk.choices) > 0 and
                    chunk.choices[0].delta and
                    hasattr(chunk.choices[0].delta, 'content') and
                    chunk.choices[0].delta.content):
                    full_response += chunk.choices[0].delta.content

            return full_response.strip()

        except Exception as e:
            logger.error(f"Error calling Azure OpenAI: {e}")
            raise e

    async def _stream_generate_ai_response(self, question: str, document_data: List[Dict], web_search_data: List[Dict], previous_message: Optional[str] = None,search_mode: str = "hybrid") -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the AI response generation in real-time."""
        try:
            # Build context with conversation history
            context_parts = [f"User Question: {question}"]

            if previous_message and previous_message.strip():
                context_parts.extend(["", f"Previous Conversation Context:\n{previous_message}"])

            context_parts.append("")
            doc_counter = 1
            web_counter = 1

            # Add document context with citation numbers (D1, D2, etc.)
            if document_data:
                context_parts.append("**Document Sources:**")
                for doc in document_data:  # Include ALL documents
                    context_parts.append(f"[D{doc_counter}] **{doc['filename']}**")
                    if doc['content']:
                        context_parts.append(f"    Content: {doc['content']}")
                    doc_counter += 1
                context_parts.append("")
            elif search_mode == "internal":
                context_parts.append("**Document Sources:** No relevant documents found.")
            

            # Add web search context with citation numbers (W1, W2, etc.)
            if web_search_data:
                context_parts.append("**Web Search Sources:**")
                for web in web_search_data:  # Include ALL web results
                    context_parts.append(f"[W{web_counter}]: **{web['title']}**")
                    if web.get('url'):
                        context_parts.append(f"    URL: {web['url']}")
                    if web.get('content'):
                        context_parts.append(f"    Content: {web['content']}")
                    web_counter += 1
                context_parts.append("")

            # Use localized system prompt
            system_prompt = self._get_localized_system_prompt(search_mode)
            

            # Add language instruction to user prompt
            context_parts.append(f"\nPlease respond in {self.language.upper()} language.")
            user_prompt = "\n".join(context_parts)

            # Initialize Azure OpenAI client
            client = AsyncAzureOpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT")
            )

            # Call Azure OpenAI with streaming
            response = await client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1500,
                temperature=0.7,
                stream=True  # Enable streaming
            )

            # Stream the response chunks
            partial_response = ""
            async for chunk in response:
                # Safely check if chunk has choices and content
                if (chunk.choices and
                    len(chunk.choices) > 0 and
                    chunk.choices[0].delta and
                    hasattr(chunk.choices[0].delta, 'content') and
                    chunk.choices[0].delta.content):

                    content = chunk.choices[0].delta.content
                    partial_response += content

                    # Yield the streaming chunk
                    yield {
                        "content": content,
                        "partial_answer": partial_response,
                        "done": False
                    }

            # Yield the final complete response
            yield {
                "answer": partial_response.strip(),
                "done": True
            }

        except Exception as e:
            logger.error(f"Error in stream generate AI response: {e}")
            yield {
                "answer": f"{self.t('error_occurred', 'An error occurred while processing your request.')}: {str(e)}",
                "done": True
            }

    async def _synthesize_deep_response(self, query: str, tool_results: Dict[str, str], previous_message: Optional[str] = None) -> tuple[str, str]:
        """Synthesize an expanded research-grade response using Azure DeepSeek R1."""
        try:
            from openai import AsyncAzureOpenAI
            import os

            # Initialize Azure DeepSeek R1 client
            client = AsyncAzureOpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                azure_endpoint=os.getenv("AZURE_OPENAI_API_ENDPOINT", "https://hari-m8cq3kcu-switzerlandnorth.services.ai.azure.com/"),
                azure_deployment=os.getenv("AZURE_DEEPSEEK_R1_DEPLOYMENT", "deepseek-r1")
            )

            # Prepare context from tool results
            context_parts = []

            # Extract document content
            document_data = []
            if "document_retrieval" in tool_results:
                document_data = self._extract_document_content(tool_results["document_retrieval"])
            if "vector_search" in tool_results:
                document_data.extend(self._extract_document_content(tool_results["vector_search"]))

            if document_data:
                context_parts.append("=== DOCUMENT SOURCES ===")
                for i, doc in enumerate(document_data[:10], 1):  # Limit to top 10 documents
                    context_parts.append(f"Document {i}:")
                    context_parts.append(f"Title: {doc.get('filename', 'Unknown')}")
                    context_parts.append(f"Content: {doc.get('content', '')[:1000]}...")  # Limit content length
                    context_parts.append("")

            # Extract web search content
            web_data = []
            if "web_search" in tool_results:
                web_data = self._extract_web_search_content(tool_results["web_search"])

            if web_data:
                context_parts.append("=== WEB SOURCES ===")
                for i, web in enumerate(web_data[:5], 1):  # Limit to top 5 web results
                    context_parts.append(f"Web Source {i}:")
                    context_parts.append(f"Title: {web.get('title', 'Unknown')}")
                    context_parts.append(f"URL: {web.get('url', 'Unknown')}")
                    context_parts.append(f"Content: {web.get('content', '')[:800]}...")  # Limit content length
                    context_parts.append("")

            context = "\n".join(context_parts) if context_parts else "No specific context available."

            # Enhanced localized system prompt for deep research
            system_prompt = self._get_localized_deep_system_prompt()

            # Prepare user prompt with context
            user_prompt_parts = [f"User Question: {query}"]

            if previous_message:
                user_prompt_parts.append(f"\nPrevious Conversation Context:\n{previous_message}")

            if context.strip() != "No specific context available.":
                user_prompt_parts.append(f"\nAvailable Research Context:\n{context}")

            user_prompt_parts.append(f"\nPlease provide a comprehensive, research-grade analysis of this question. Think through the problem systematically and provide an expanded, in-depth response that explores all relevant aspects. Respond in {self.language.upper()} language.")

            user_prompt = "\n".join(user_prompt_parts)

            # Call Azure DeepSeek R1 with reasoning
            response = await client.chat.completions.create(
                model=os.getenv("AZURE_DEEPSEEK_R1_DEPLOYMENT", "deepseek-r1"),  # Azure DeepSeek R1 deployment
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=4000,  # Increased for deep answers
                temperature=0.3,  # Lower temperature for more focused analysis
                stream=False
            )

            # Extract answer and thinking process
            if response.choices and response.choices[0].message:
                message = response.choices[0].message
                answer = message.content or "I apologize, but I couldn't generate a comprehensive response."

                # Extract thinking process if available (DeepSeek R1 specific)
                # Try multiple possible attributes for thinking content
                thinking = ""
                if hasattr(message, 'reasoning_content') and message.reasoning_content:
                    thinking = message.reasoning_content
                elif hasattr(message, 'thinking') and message.thinking:
                    thinking = message.thinking
                elif hasattr(message, 'reasoning') and message.reasoning:
                    thinking = message.reasoning

                # Log the response structure for debugging
                logger.info(f"DeepSeek R1 response structure: {dir(message)}")
                logger.info(f"Message content length: {len(answer)}")
                logger.info(f"Thinking content length: {len(thinking)}")

                # If no thinking content found, add a placeholder for testing
                if not thinking and answer:
                    thinking = f"Deep analysis completed using Azure DeepSeek R1.\n\nQuery: {query[:100]}...\n\nThis response was generated using comprehensive research-grade analysis with enhanced reasoning capabilities."

                return answer, thinking
            else:
                return "I apologize, but I couldn't generate a comprehensive response.", ""

        except Exception as e:
            logger.error(f"Error generating deep response with Azure DeepSeek R1: {e}")
            # Fallback to regular response
            regular_answer = await self._synthesize_response(query, tool_results, previous_message)
            return regular_answer, ""

    async def query(
        self,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        workspace_ids: List[str],
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        stream: bool = False,
        chat_id: Optional[str] = None,  # NEW: For persistent memory
        deep_answer: bool = False,  # NEW: Use DeepSeek R1 for expanded answers
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Main query method for the minimal CopilotKit RAG agent.
        """
        if stream:
            # For streaming, yield from the async generator
            async for chunk in self.query_stream(
                question, user_id, tenant_id, user_name, workspace_ids,
                search_mode, include_web_results, chat_id, deep_answer, **kwargs
            ):
                yield chunk
        else:
            # For non-streaming, yield the single result
            result = await self.query_non_stream(
                question, user_id, tenant_id, user_name, workspace_ids,
                search_mode, include_web_results, chat_id, deep_answer, **kwargs
            )
            yield result

    async def query_non_stream(
        self,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        workspace_ids: List[str],
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Non-streaming query method with persistent memory."""
        start_time = time.time()
        previous_message = kwargs.get('previous_message')
        try:
            # Handle identity questions immediately (before any other processing)
            if self._is_identity_question(question):
                return {
                    "answer": self._get_identity_response(self.language),
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "tools_used": [],
                    "elapsed_time": time.time() - start_time,
                    "status": 200
                }

            # Check if MCP servers are selected - if so, route to MCP flow
            if search_mode == "mcp":
                # For non-streaming MCP, get the first result from the async generator
                async for result in self._handle_mcp_query(
                    question, user_id, tenant_id, user_name, previous_message
                ):
                    if result.get("done"):
                        return result
                    # If we get a partial result without "done", continue to get the final result

                # If we exit the loop without a final result, return an error
                return {
                    "error": "MCP query completed without final result",
                    "answer": "",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "status": 500
                }

            # Otherwise, use regular RAG flow
            logger.info(f"Starting minimal CopilotKit agent query: '{question}'")
            return await self._non_stream_response(
                question, user_id, tenant_id, user_name, workspace_ids,
                search_mode, include_web_results, start_time, previous_message, chat_id, deep_answer
            )
        except Exception as e:
            logger.error(f"Error in minimal CopilotKit agent query: {e}")
            return {
                "error": f"Agent query failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "status": 500
            }

    async def query_stream(
        self,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        workspace_ids: List[str],
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,
        language: str = "en",
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Streaming query method with persistent memory."""
        start_time = time.time()
        previous_message = kwargs.get('previous_message')

        try:
            # Check if MCP servers are selected - if so, route to MCP flow
            if search_mode == "mcp":
                async for chunk in self._handle_mcp_query(
                    question, user_id, tenant_id, user_name, previous_message
                ):
                    yield chunk
                return
            # Check if empty search mode - handle identity questions first, then use basic synthesis
            if search_mode == "":
                # Handle identity questions immediately (before any other processing)
                if self._is_identity_question(question):
                    # Use the agent's configured language instead of detecting from question
                    # This ensures we respect the language parameter passed from the frontend
                    logger.info(f"STREAMING EMPTY MODE: Identity question detected, using agent language: {self.language}, returning Swiss Knowledge Hub response")
                    yield {
                        "status": "complete",
                        "answer": self._get_identity_response(self.language),
                        "title": question[:50] + "..." if len(question) > 50 else question,
                        "sources": [],
                        "done": True
                    }
                    return

                async for chunk in self._stream_synthesize_response(question, {}, previous_message, [],""):
                    current_elapsed_time = time.time() - start_time

                    if chunk.get("done"):
                        # Final chunk with complete response
                        yield {
                            "answer": chunk["answer"],
                            "title": question[:50] + "..." if len(question) > 50 else question,
                            "elapsed_time": current_elapsed_time,
                            "done": True,
                            "status": "complete"
                        }
                    else:
                        # Streaming chunk
                        yield {
                            "answer_chunk": chunk.get("content", ""),
                            "partial_answer": chunk.get("partial_answer", ""),
                            "elapsed_time": current_elapsed_time,
                            "done": False,
                            "status": "streaming"
                        }

                return

            # Otherwise, use regular RAG flow
            logger.info(f"Starting minimal CopilotKit agent streaming query: '{question}'")
            async for chunk in self._stream_response(
                question, user_id, tenant_id, user_name, workspace_ids,
                search_mode, include_web_results, start_time, previous_message, chat_id, deep_answer
            ):
                yield chunk
        except Exception as e:
            logger.error(f"Error in minimal CopilotKit agent streaming query: {e}")
            yield {
                "error": f"Agent query failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "done": True
            }

    async def _handle_mcp_query(
        self,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        previous_message: Optional[str] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle query using MCP servers."""
        try:
            # Set the database client for MCP service
            mcp_service.db_client = self.db_client
            yield {
                    "type": "mcp_status",
                    "status": "health_check",
                    "message": self.t("mcp_health_check", self.language),
                    "done": False
                }

            # Get server configuration from database
            server_configs = await self._get_mcp_server_config(user_id, tenant_id)
            if not server_configs:
                yield {
                    "error": "No active MCP servers found",
                    "answer": "",
                    "done": True,
                    "mcp_info": {
                        "server_name": "Unknown",
                        "tools_used": [],
                        "tool_calls": [],
                        "server_info": None,
                        "error": "No active MCP servers found",
                        "execution_summary": {
                            "server_used": "Unknown",
                            "tools_executed": [],
                            "total_calls": 0,
                            "success": False
                        }
                    }
                }
                return
            yield {
                "type": "mcp_status",
                "status": "health_check",
                "message": self.t("fetching_all_tools", self.language),
                "done": False
            }

            # Collect all tools from the async generator
            all_tools = []
            async for tools_chunk in mcp_service.get_tools_for_server(tenant_id, server_configs, self.language):
                if tools_chunk.get("type") == "tools_summary":
                    all_tools = tools_chunk.get("all_tools", [])
                    break
                elif tools_chunk.get("type") == "server_error":
                    # Forward server errors to the frontend
                    yield {
                        "type": "mcp_status",
                        "status": "error",
                        "message": f"Server error: {tools_chunk.get('error', 'Unknown error')}",
                        "server_name": tools_chunk.get("server_name", "Unknown"),
                        "done": False
                    }
                elif tools_chunk.get("type") == "server_status":
                    # Forward status updates to the frontend
                    yield {
                        "type": "mcp_status",
                        "status": "connecting",
                        "message": tools_chunk.get("message", self.t("processing_request", "Processing servers...")),
                        "server_name": tools_chunk.get("server_name", "Unknown"),
                        "done": False
                    }

            if len(all_tools) == 0:
                yield {
                    "error": self.t("no_tools_found", self.language),
                    "answer": "",
                    "done": True,
                    "mcp_info": {
                        "server_name": "Unknown",
                        "tools_used": [],
                        "tool_calls": [],
                        "server_info": None,
                        "error": self.t("no_tools_found", self.language),
                        "execution_summary": {
                            "server_used": "Unknown",
                            "tools_executed": [],
                            "total_calls": 0,
                            "success": False
                        }
                    }
                }
                return
            tools_used = []
            tool_calls = []
            # Use MCP service to process the query
            async for chunk in mcp_service.query_with_mcp(
                tenant_id=tenant_id,
                question=question,
                tools=all_tools,
                stream=True,
                previous_message=previous_message,
                language=self.language
            ):
                # Enhance chunk with MCP-specific information for frontend
                if chunk.get("type") == "tool_call":
                    # Track tool calls for frontend display
                    tool_call_info = {
                        "tool_name": chunk.get("tool_name"),
                        "tool_args": chunk.get("tool_args"),
                        "timestamp": time.time(),
                    }
                    tool_calls.append(tool_call_info)
                    tools_used.append(chunk.get("tool_name"))

                    # Yield enhanced tool call info for frontend
                    yield {
                        **chunk,
                        "mcp_info": {
                            "tool_call_info": tool_call_info,
                            "total_tools_called": len(tool_calls)
                        }
                    }
                elif chunk.get("type") == "tool_error":
                    # Handle tool execution errors
                    error_msg = chunk.get("error", "Unknown error")
                    error_type = chunk.get("error_type", "Unknown")
                    raw_error = chunk.get("raw_error", "")
                    tool_name = chunk.get("tool_name", "Unknown")

                    # Check if this is a reconnection-related error
                    if error_type in ["ServerUnhealthy", "ReconnectionFailed"]:
                        # Yield reconnection status for frontend
                        yield {
                            "type": "mcp_status",
                            "status": "reconnecting" if error_type == "ServerUnhealthy" else "reconnection_failed",
                            "message": error_msg,
                            "done": False,
                            "mcp_info": {
                                "error": error_msg,
                                "error_type": error_type
                            }
                        }

                    # Yield enhanced error info for frontend
                    yield {
                        **chunk,
                        "mcp_info": {
                            "tool_call_info": {
                                "tool_name": tool_name,
                                "tool_args": chunk.get("tool_args", {}),
                                "timestamp": time.time()
                            },
                            "error": error_msg,
                            "error_type": error_type,
                            "raw_error": raw_error,
                            "is_mcp_response": True
                        }
                    }
                elif chunk.get("done"):
                    # Capture server info from final chunk
                    server_info = chunk.get("server_info")

                    # Enhance final chunk with comprehensive MCP info for frontend
                    yield {
                        **chunk,
                        "mcp_info": {
                            "tools_used": list(set(tools_used)),  # Remove duplicates
                            "tool_calls": tool_calls,
                            "server_info": server_info,
                            "elapsed_time": chunk.get("elapsed_time"),
                            "total_tool_calls": len(tool_calls),
                            "execution_summary": {
                                "tools_executed": list(set(tools_used)),
                                "total_calls": len(tool_calls),
                                "success": not chunk.get("error")
                            }
                        }
                    }
                else:
                    # Pass through other chunks with basic MCP info
                    yield {
                        **chunk,
                        "mcp_info": {
                            "is_mcp_response": True
                        }
                    }

        except Exception as e:
            logger.error(f"Error in MCP query handling: {e}")
            yield {
                "error": f"MCP query failed: {str(e)}",
                "answer": "",
                "done": True,
                "mcp_info": {
                    "tools_used": tools_used if 'tools_used' in locals() else [],
                    "tool_calls": tool_calls if 'tool_calls' in locals() else [],
                    "server_info": None,
                    "error": str(e),
                    "execution_summary": {
                        "tools_executed": tools_used if 'tools_used' in locals() else [],
                        "total_calls": len(tool_calls) if 'tool_calls' in locals() else 0,
                        "success": False
                    }
                }
            }

    async def _get_mcp_server_config(self, user_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get MCP server configuration from database."""
        try:
            if self.db_client is None:
                logger.error("Database client not available")
                return None

            # Import ObjectId for MongoDB queries
            from bson import ObjectId

            # Query the database for the MCP server using MongoDB syntax
            servers_cursor = self.db_client.MCPServer.find({
                "$or": [
                    {"userId": ObjectId(user_id)},
                    {"isPublic": True}
                ],
                "tenantId": ObjectId(tenant_id),
                "status": "ACTIVE"
            })
            servers = await servers_cursor.to_list(length=None)

            server_configs=[]
            # Select the first server that is available
            for server in servers:

                # Convert to server config format expected by MCP service
                server_config = {
                    "name": server.get("name", "Unknown"),
                    "id": str(server.get("_id", "")),
                    "description": server.get("description", ""),
                    "command": server.get("command", ""),
                    "args": server.get("args", []),
                    "env": server.get("env", {}),
                    "server_type": server.get("serverType", "STDIO"),
                    "url": server.get("url", ""),
                    "headers": server.get("headers", {}),
                    "timeout": server.get("timeout", 30000),
                    "auto_restart": server.get("autoRestart", True)
                }

                server_configs.append(server_config)

            logger.info(f"Retrieved MCP server configs: {server_configs}")
            return server_configs

        except Exception as e:
            logger.error(f"Error retrieving MCP server config: {e}")
            return None

    async def _non_stream_response(
        self, question: str, user_id: str, tenant_id: str, user_name: str,
        workspace_ids: List[str], search_mode: str, include_web_results: bool,
        start_time: float, previous_message: Optional[str] = None,
        chat_id: Optional[str] = None, deep_answer: bool = False
    ) -> Dict[str, Any]:
        """Handle non-streaming response with persistent memory."""
        try:
            # Step 0: Load persistent conversation history if chat_id provided
            if chat_id and not previous_message:
                previous_message = await self._load_conversation_history(chat_id, user_id)
                if previous_message:
                    logger.info(f"Loaded persistent conversation history for chat {chat_id}")

            # Step 1: Detect if question contains image context
            has_image_context = self._detect_image_context(question)

            # Step 2: Analyze query with previous message context and image context
            query_analysis = self._analyze_query(question, previous_message, has_image_context)
            logger.info(f"Query analysis: {query_analysis} (has_image_context: {has_image_context})")

            # Step 2: Select tools
            tools_to_use = self._select_tools(query_analysis, search_mode, include_web_results)
            logger.info(f"Selected tools: {tools_to_use}")

            # Step 3: Execute tools in parallel using resolved query if available
            query_to_use = query_analysis.get("resolved_query", question)
            is_follow_up = query_analysis.get("is_follow_up", False)
            tool_results = await self._execute_tools_parallel(
                tools_to_use, query_to_use, workspace_ids, tenant_id, user_id, search_mode, is_follow_up
            )
            logger.info(f"Tool execution completed: {list(tool_results.keys())}")

            # Step 4: Synthesize response with previous message context
            if deep_answer and self.deep_research_agent:
                # Use Deep Research Agent for iterative, comprehensive research
                logger.info("Using Deep Research Agent for comprehensive analysis")

                # Prepare search tools for the deep research agent
                search_tools = {
                    "document_search": self._create_document_search_tool(workspace_ids, tenant_id, user_id),
                    "web_search": self._create_web_search_tool(question),
                    "deepseek_synthesis": self._create_deepseek_synthesis_tool()
                }

                # Conduct deep research (non-streaming for now)
                research_results = []
                async for result in self.deep_research_agent.conduct_deep_research(
                    question, user_id, tenant_id, workspace_ids, search_tools, stream=False, language=self.language
                ):
                    research_results.append(result)

                # Get the final comprehensive result
                final_result = research_results[-1] if research_results else {}
                answer = final_result.get("answer", "Unable to generate comprehensive research answer.")
                thinking = final_result.get("thinking", "Deep research analysis completed.")

                # Add research metadata
                research_summary = final_result.get("research_summary", {})
                logger.info(f"Deep research completed: {research_summary.get('iterations_conducted', 0)} iterations, "
                          f"confidence: {research_summary.get('final_confidence', 0):.1%}")

                # Use sources from deep research instead of tool_results
                deep_research_sources = final_result.get("sources", [])
                deep_research_iterations = final_result.get("iterations", [])

            elif deep_answer:
                # Fallback to simple DeepSeek synthesis if Deep Research Agent not available
                answer, thinking = await self._synthesize_deep_response(question, tool_results, previous_message)
            else:
                answer = await self._synthesize_response(question, tool_results, previous_message,sources)
                thinking = None

            # Create title
            title = question[:50] + "..." if len(question) > 50 else question

            # Extract sources - use deep research sources if available, otherwise tool results
            if deep_answer and self.deep_research_agent and 'deep_research_sources' in locals():
                sources = deep_research_sources
                logger.info(f"Using deep research sources: {len(sources)}")
            else:
                sources = self._extract_sources_from_results(tool_results, question, search_mode)
                logger.info(f"Using tool result sources: {len(sources)}")

            elapsed_time = time.time() - start_time
            logger.info(f"Minimal CopilotKit agent completed in {elapsed_time:.2f} seconds")

            result = {
                "answer": answer,
                "title": title,
                "sources": sources,
                "tools_used": tools_to_use,
                "elapsed_time": elapsed_time,
                "status": 200
            }

            # Add thinking process if available (DeepSeek R1)
            if thinking:
                result["thinking"] = thinking

            # Add deep research metadata if available
            if deep_answer and self.deep_research_agent and 'deep_research_iterations' in locals():
                result["research_summary"] = research_summary
                result["iterations"] = deep_research_iterations
                logger.info(f"Added deep research metadata: {len(deep_research_iterations)} iterations")

            return result

        except Exception as e:
            logger.error(f"Error in non-stream response: {e}")
            return {
                "error": f"Response generation failed: {str(e)}",
                "answer": "",
                "title": question[:50],
                "sources": [],
                "status": 500
            }

    async def _stream_response(
        self, question: str, user_id: str, tenant_id: str, user_name: str,
        workspace_ids: List[str], search_mode: str, include_web_results: bool,
        start_time: float, previous_message: Optional[str] = None,
        chat_id: Optional[str] = None, deep_answer: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming response with persistent memory."""
        try:
            # Yield initial status
            yield {
                "status": "starting",
                "message": "Initializing minimal CopilotKit agent...",
                "done": False
            }

            # Step 0: Load persistent conversation history if chat_id provided
            if chat_id and not previous_message:
                yield {
                    "status": "loading_history",
                    "message": "Loading conversation history...",
                    "done": False
                }
                previous_message = await self._load_conversation_history(chat_id, user_id)
                if previous_message:
                    logger.info(f"Loaded persistent conversation history for chat {chat_id}")

            # Step 1: Handle identity questions immediately (before any other processing)
            logger.info(f"STREAMING: Checking identity question for: '{question}' | Agent language: {self.language}")
            if self._is_identity_question(question):
                logger.info(f"STREAMING: Identity question detected, using agent language: {self.language}, returning Swiss Knowledge Hub response")
                yield {
                    "status": "complete",
                    "answer": self._get_identity_response(self.language),
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "done": True
                }
                return
            logger.info(f"STREAMING: Not an identity question, continuing with normal processing")

            # Step 2: Detect if question contains image context
            has_image_context = self._detect_image_context(question)

            # Step 3: Analyze query with previous message context and image context
            yield {
                "status": "analyzing",
                "message": self.t("analyzing_sources", "Analyzing query and determining search strategy..."),
                "done": False
            }

            query_analysis = self._analyze_query(question, previous_message, has_image_context)
            if query_analysis.get("is_greeting", False):
                yield {
                    "status": "complete",
                    "answer": "Hello! I'm an AI assistant created by Swiss Knowledge Hub. How can I assist you today?",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "done": True
                }
                return

            logger.info(f"search_mode: {search_mode} include_web_results:{include_web_results}, {query_analysis}")

            # Step 2: Select tools
            yield {
                "status": "tool_selection",
                "message": "Selecting appropriate tools...",
                "done": False
            }
            tools_to_use = self._select_tools(query_analysis, search_mode, include_web_results)

            yield {
                "status": "tool_selection",
                "message": f"Selected tools: {', '.join(tools_to_use)}",
                "tools_used": tools_to_use,
                "done": False
            }

            # Step 3: Execute tools
            yield {
                "status": "searching",
                "message": self.t("processing_request", "Executing search tools in parallel..."),
                "done": False
            }
            # Use resolved query if available for better context understanding
            query_to_use = query_analysis.get("resolved_query", question)
            is_follow_up = query_analysis.get("is_follow_up", False)
            tool_results = await self._execute_tools_parallel(
                tools_to_use, query_to_use, workspace_ids, tenant_id, user_id, search_mode, is_follow_up
            )

            # Extract sources immediately after tool execution
            sources = self._extract_sources_from_results(tool_results, question, search_mode)

            # Send sources first so frontend can display them immediately
            yield {
                "status": "sources_ready",
                "message": self.t("generating_response", "Sources extracted, generating response..."),
                "sources": sources,
                "tools_used": tools_to_use,
                "done": False
            }

            # Step 4: Stream the AI response generation with previous message context
            yield {
                "status": "synthesizing",
                "message": self.t("generating_response", "Generating final response with citations..."),
                "done": False
            }

            logger.info(f"Tool execution completed: {question}")


            # Stream the AI response as it's being generated with previous message context
            logger.info(f"Deep answer request: {deep_answer}, Deep Research Agent available: {self.deep_research_agent is not None}")
            if deep_answer and self.deep_research_agent:
                # Use Deep Research Agent for iterative, comprehensive research
                logger.info("Starting Deep Research Agent streaming analysis")

                # Prepare search tools for the deep research agent
                search_tools = {
                    "document_search": self._create_document_search_tool(workspace_ids, tenant_id, user_id),
                    "web_search": self._create_web_search_tool(question),
                    "deepseek_synthesis": self._create_deepseek_synthesis_tool()
                }

                # Stream deep research progress
                async for research_chunk in self.deep_research_agent.conduct_deep_research(
                    question, user_id, tenant_id, workspace_ids, search_tools, stream=True, language=self.language
                ):
                    # Forward research progress to client
                    if research_chunk.get("phase") == "complete":
                        # Final comprehensive result
                        current_elapsed_time = time.time() - start_time

                        # Validate final research result
                        answer_length = len(research_chunk.get('answer', ''))
                        sources_count = len(research_chunk.get('sources', []))
                        iterations_count = len(research_chunk.get('iterations', []))

                        logger.info(f"Deep research complete - Answer: {answer_length} chars, Sources: {sources_count}, Iterations: {iterations_count}")

                        final_response = {
                            "answer": research_chunk.get("answer", ""),
                            "title": question[:50] + "..." if len(question) > 50 else question,
                            "thinking": research_chunk.get("thinking", ""),
                            "sources": research_chunk.get("sources", sources),
                            "tools_used": tools_to_use,
                            "research_summary": research_chunk.get("research_summary", {}),
                            "iterations": research_chunk.get("iterations", []),
                            "elapsed_time": current_elapsed_time,
                            "done": True,
                            "status": "complete",
                            "metadata": {
                                "has_image_context": self._detect_image_context(question)
                            }
                        }

                        # Ensure we have valid data before yielding
                        if not final_response["answer"]:
                            logger.error("Final response has empty answer, using fallback")
                            final_response["answer"] = "Deep research completed but synthesis failed. Please check the research iterations for detailed findings."

                        yield final_response
                    else:
                        # Stream research progress
                        yield {
                            "status": research_chunk.get("phase", "research"),
                            "message": research_chunk.get("message", "Conducting deep research..."),
                            "progress": research_chunk.get("progress", 0.5),
                            "research_data": research_chunk,
                            "done": False
                        }

            elif deep_answer:
                # Fallback to simple DeepSeek synthesis if Deep Research Agent not available
                yield {
                    "status": "deep_analysis",
                    "message": self.t("deep_analysis", "Generating comprehensive research-grade response with DeepSeek R1..."),
                    "done": False
                }

                answer, thinking = await self._synthesize_deep_response(question, tool_results, previous_message)
                current_elapsed_time = time.time() - start_time

                # Return the complete deep answer
                final_response = {
                    "answer": answer,
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": sources,
                    "tools_used": tools_to_use,
                    "elapsed_time": current_elapsed_time,
                    "done": True,
                    "status": "complete"
                }

                # Add thinking process if available
                if thinking:
                    final_response["thinking"] = thinking

                yield final_response
            else:
                # Regular streaming response
                logger.info("Starting streaming synthesis",tool_results)
                async for chunk in self._stream_synthesize_response(question, tool_results, previous_message, sources,"" if is_follow_up else search_mode):
                    current_elapsed_time = time.time() - start_time

                    if chunk.get("done"):
                        # Final chunk with complete response
                        yield {
                            "answer": chunk["answer"],
                            "title": question[:50] + "..." if len(question) > 50 else question,
                            "sources": sources,  # Sources already available
                            "tools_used": tools_to_use,
                            "elapsed_time": current_elapsed_time,
                            "done": True,
                            "status": "complete"
                        }
                    else:
                        # Streaming chunk
                        yield {
                            "answer_chunk": chunk.get("content", ""),
                            "partial_answer": chunk.get("partial_answer", ""),
                            "sources": sources,  # Sources available from the start
                            "elapsed_time": current_elapsed_time,
                            "done": False,
                            "status": "streaming"
                        }

        except Exception as e:
            logger.error(f"Error in stream response: {e}")
            yield {
                "error": f"Streaming failed: {str(e)}",
                "answer": "",
                "title": question[:50],
                "sources": [],
                "done": True
            }

    
    def sort_by_cosmos_score(self, docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        return sorted(
            docs,
            key=lambda doc: doc.get("metadata", {}).get("__cosmos_meta__", {}).get("score", 0),
            reverse=True
        )

    def _extract_sources_from_results(self, tool_results: Dict[str, Any], query: str = "", search_mode: str = "hybrid") -> List[Dict[str, Any]]:
        """Extract and format sources from tool results with proper citations."""
        sources = []

        for tool_name, result in tool_results.items():
            content = result.get("content", "Error")

            # Skip error results in sources
            if content.startswith(("Error", "Tool", "Unknown", "No relevant", "No semantically", "No documents")):
                continue

            # Parse document results to extract individual sources
            if "Found" in content and ("documents:" in content or "documents" in content):
                doc_sources = result.get("documents", [])

                # Filter sources for relevance
                filtered_sources = self._filter_relevant_sources(doc_sources, query, search_mode)
                sorted_doc=self.sort_by_cosmos_score(filtered_sources)
                logger.info(f"Found sorted_doc {len(sorted_doc)} relevant sources from {tool_name}")
                sources.extend(sorted_doc[:10])
            elif tool_name == "llm_knowledge":
                # Special handling for LLM knowledge
                sources.append({
                    "type": "knowledge",
                    "title": "Knowledge Base",
                    "content": result[:400] + "..." if len(result) > 400 else result,
                    "url": None,
                    "metadata": {
                        "source": "Knowledge Base",
                        "tool": tool_name,
                        "content_length": len(result),
                        "truncated": len(result) > 400,
                        "source_type": "general_knowledge"
                    }
                })
            elif tool_name == "web_search" and content.strip():
                # Special handling for web search results
                web_sources = self._parse_web_search_sources(content)
                logger.info(f"Web search sources: {web_sources}")
                sources.extend(web_sources)
            elif content.strip():
                if tool_name in ["document_retrieval", "vector_search"]:
                    continue

                # Fallback for other types of results
                source_title = {
                    "document_retrieval": "Document Search",
                    "vector_search": "Semantic Search",
                    "web_search": "Web Search"
                }.get(tool_name, f"{tool_name.replace('_', ' ').title()}")

                sources.append({
                    "type": tool_name,
                    "title": source_title,
                    "content": result[:300] + "..." if len(result) > 300 else result,
                    "url": None,
                    "metadata": {
                        "source": tool_name,
                        "tool": tool_name,
                        "content_length": len(result),
                        "truncated": len(result) > 300
                    }
                })

        final_sources = []
        for i in sources:
            metadata = i.get("metadata", {})
            # Use comprehensive ObjectId cleaning
            cleaned = self._clean_objectids_recursive(metadata)
            # Filter out problematic fields
            cleaned = {
                k: v for k, v in cleaned.items()
                if k not in ["embedding", "workspace"]
            }
            final_sources.append({
                "content": i.get("content", ""),
                "metadata": cleaned
            })

        return final_sources

    def _clean_objectids_recursive(self, obj):
        """Recursively clean ObjectIds from any data structure."""
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, dict):
            return {key: self._clean_objectids_recursive(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._clean_objectids_recursive(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._clean_objectids_recursive(obj.__dict__)
        else:
            return obj

    def _parse_web_search_sources(self, result: str) -> List[Dict[str, Any]]:
        """Parse web search results into individual source citations."""
        sources = []
        lines = result.split('\n')
        current_source = None


        for line in lines:
            line = line.strip()

            # Look for web search entries like "[W1] Title"
            if line.startswith('[W') and ']' in line:
                # Save previous source if exists
                if current_source and (current_source.get("title") or current_source.get("content")):
                    sources.append(current_source)

                try:
                    # Extract the web result number and title
                    bracket_end = line.find(']')
                    if bracket_end == -1:
                        continue

                    web_number = line[:bracket_end+1]  # e.g., "[W1]"
                    title = line[bracket_end+1:].strip()

                    current_source = {
                        "type": "web_search",
                        "title": title if title else "Web Search Result",
                        "content": "",
                        "url": None,
                        "metadata": {
                            "source": "web",
                            "tool": "web_search",
                            "type": "web_search",
                            "web_result_number": web_number,
                            "content_length": 0,
                            "source_type": "web_search",
                            "has_url": False,
                            "link": None,
                            "title": title,
                            "displayLink": "web",
                            "fileName": title,  # Frontend expects fileName
                            "workspace": {
                                "slug": "web",
                                "name": "Web Search"
                            }
                        }
                    }

                except Exception as e:
                    logger.warning(f"Failed to parse web search line: {line}, error: {e}")
                    continue

            # Look for URL lines
            elif line.startswith('URL:') and current_source:
                url = line.replace('URL:', '').strip()
                current_source["url"] = url
                current_source["metadata"]["link"] = url
                current_source["metadata"]["has_url"] = True
                # Extract domain for displayLink
                if url and "//" in url:
                    try:
                        domain = url.split("//")[1].split("/")[0]
                        current_source["metadata"]["displayLink"] = domain
                    except:
                        current_source["metadata"]["displayLink"] = "web"

            # Look for snippet lines
            elif line.startswith('Snippet:') and current_source:
                snippet = line.replace('Snippet:', '').strip()
                current_source["content"] = snippet
                current_source["metadata"]["content_length"] = len(snippet)

            # Handle single-line format: "[W1] Title Snippet: content"
            elif line.startswith('[W') and 'Snippet:' in line:
                try:
                    bracket_end = line.find(']')
                    content_part = line[bracket_end+1:].strip()

                    if 'Snippet:' in content_part:
                        parts = content_part.split('Snippet:', 1)
                        title = parts[0].strip()
                        snippet = parts[1].strip() if len(parts) > 1 else ""

                        web_number = line[:bracket_end+1]

                        sources.append({
                            "type": "web_search",
                            "title": title if title else "Web Search Result",
                            "content": snippet,
                            "url": None,
                            "metadata": {
                                "source": "web",
                                "tool": "web_search",
                                "type": "web_search",
                                "web_result_number": web_number,
                                "content_length": len(snippet),
                                "source_type": "web_search",
                                "has_url": False,
                                "link": None,
                                "title": title,
                                "displayLink": "web",
                                "fileName": title,  # Frontend expects fileName
                                "workspace": {
                                    "slug": "web",
                                    "name": "Web Search"
                                }
                            }
                        })

                except Exception as e:
                    logger.warning(f"Failed to parse single-line web search: {line}, error: {e}")
                    continue

        # Add the last source
        if current_source and (current_source.get("title") or current_source.get("content")):
            sources.append(current_source)

        # If no sources were parsed but we have web search content, create a fallback
        if not sources and result.strip():
            # Check if this looks like web search results
            if "web search" in result.lower() or "found" in result.lower():
                sources.append({
                    "type": "web_search",
                    "title": "Web Search Results",
                    "content": result[:200] + "..." if len(result) > 200 else result,
                    "url": None,
                    "metadata": {
                        "source": "web",
                        "tool": "web_search",
                        "type": "web_search",
                        "content_length": len(result),
                        "source_type": "web_search",
                        "fallback_parsing": True,
                        "link": None,
                        "title": "Web Search Results",
                        "displayLink": "web",
                        "fileName": "Web Search Results",  # Frontend expects fileName
                        "workspace": {
                            "slug": "web",
                            "name": "Web Search"
                        }
                    }
                })
        return sources

    def _filter_relevant_sources(self, sources: List[Dict[str, Any]], query: str, search_mode: str = "hybrid") -> List[Dict[str, Any]]:
        """Filter sources based on semantic relevance to the query, with adjustments for search mode."""
        if not sources or not query:
            return sources

        query_lower = query.lower()

        # Extract meaningful query terms (remove stop words)
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'how', 'what', 'where', 'when', 'why', 'who', 'which', 'can', 'should', 'would', 'could', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall', 'should', 'may', 'might', 'must', 'can', 'could'}
        query_words = set(query_lower.split())
        meaningful_query_words = query_words - stop_words

        # If query has very few meaningful terms, use all terms
        if len(meaningful_query_words) < 2:
            meaningful_query_words = query_words

        relevant_sources_with_scores = []

        for source in sources:
            try:
                # Get source content and metadata
                content = source.get("content", "").lower()
                metadata = source.get("metadata", {})

                # Get filename for additional context and decode URL encoding
                import urllib.parse
                filename = metadata.get("file_name", metadata.get("fileName", ""))
                if filename:
                    # Decode URL encoding (e.g., %20 -> space)
                    filename = urllib.parse.unquote(filename).lower()
                else:
                    filename = ""

                # Check if source has meaningful content
                if len(content) < 30:  # Skip very short content
                    logger.info(f"Filtering out source with insufficient content length: {len(content)}")
                    continue

                # Calculate semantic relevance with phrase context awareness
                content_words = set(content.split())
                filename_words = set(filename.split())

                # Check for phrase-level matches (more accurate than individual word matches)
                query_phrases = self._extract_key_phrases(query_lower)
                content_phrase_matches = sum(1 for phrase in query_phrases if phrase in content)
                filename_phrase_matches = sum(1 for phrase in query_phrases if phrase in filename)

                # Individual word overlap (fallback)
                content_overlap = len(meaningful_query_words.intersection(content_words))
                filename_overlap = len(meaningful_query_words.intersection(filename_words))

                # Calculate overlap ratio with phrase matching prioritized
                total_meaningful_terms = len(meaningful_query_words)
                total_phrases = len(query_phrases)

                # Check for important single technical terms that should be weighted heavily

                # Boost score if important tech terms match

                if total_phrases > 0 and (content_phrase_matches > 0 or filename_phrase_matches > 0):
                    # Phrase-based scoring (more accurate)
                    overlap_ratio = (content_phrase_matches + filename_phrase_matches * 2) / total_phrases + tech_boost
                elif total_meaningful_terms > 0:
                    # Word-based scoring (fallback)
                    overlap_ratio = (content_overlap + filename_overlap * 2) / total_meaningful_terms
                else:
                    overlap_ratio = 0

                # Check if this is an audio/video file for more lenient filtering
                is_audio_video = metadata.get("file_type") in ["audio", "video"] or metadata.get("transcription_service") == "azure_video_indexer"

                # Check if this is a person name query (common pattern for internal search)
                is_person_name_query = self._is_person_name_query(query_lower)

                # For internal search mode, be much more lenient with filtering
                # especially for person names and specific queries
                is_internal_search = search_mode == "internal"

                # Apply semantic filtering with context-awareness
                if is_audio_video:
                    # For audio/video files, be more lenient since content might not match filename
                    threshold = 0.15
                    # But require phrase matches if they exist in the query
                    if total_phrases > 0 and content_phrase_matches == 0 and filename_phrase_matches == 0:
                        # No phrase matches for a phrase-heavy query - be very strict
                        threshold = 0.5

                    if overlap_ratio > threshold:
                        relevant_sources_with_scores.append((source, overlap_ratio))
                        logger.info(f"Including audio/video source '{filename}' with overlap ratio: {overlap_ratio:.2f} (threshold: {threshold})")
                    else:
                        logger.info(f"Filtering out audio/video source '{filename}' with overlap ratio: {overlap_ratio:.2f} (threshold: {threshold})")
                else:
                    # Regular document filtering - adjust based on search mode and query type
                    if is_internal_search:
                        # Much more lenient for internal search - we want to find documents even with loose matches
                        threshold = 0.001  # Extremely low threshold for internal search

                        # For person names, be even more lenient since names might appear in various contexts
                        if is_person_name_query:
                            threshold = 0.0001  # Almost no threshold for person name queries

                        # For internal search, prioritize vector similarity over text overlap
                        vector_similarity = metadata.get("similarity_score", 0) or metadata.get("relevance_score", 0)

                        # If we have any vector similarity score, include the document
                        if vector_similarity > 0:
                            relevant_sources_with_scores.append((source, max(overlap_ratio, vector_similarity)))
                        # If no vector similarity but has any text overlap, include it
                        elif overlap_ratio > threshold:
                            relevant_sources_with_scores.append((source, overlap_ratio))
                        # For internal search, even include documents with very low scores
                        elif is_person_name_query:
                            relevant_sources_with_scores.append((source, max(0.1, overlap_ratio)))
                    else:
                        # Regular document filtering - require meaningful semantic overlap
                        threshold = 0.25
                        # If query has phrases but document has no phrase matches, be moderately stricter
                        if total_phrases > 0 and content_phrase_matches == 0 and filename_phrase_matches == 0:
                            threshold = 0.4  # Moderately higher threshold when no phrase matches

                        if overlap_ratio > threshold:
                            relevant_sources_with_scores.append((source, overlap_ratio))

            except Exception as e:
                logger.warning(f"Error filtering source: {e}")
                # Don't include source if we can't determine relevance
                continue

        # Sort by relevance score (highest first)
        relevant_sources_with_scores.sort(key=lambda x: x[1], reverse=True)
        relevant_sources = [source for source, score in relevant_sources_with_scores]

        # Log the sorted relevance scores for debugging
        if relevant_sources_with_scores:
            logger.info(f"Sources sorted by relevance (highest first):")
            for i, (source, score) in enumerate(relevant_sources_with_scores[:5]):  # Show top 5
                filename = source.get("metadata", {}).get("file_name", "Unknown")
                logger.info(f"  {i+1}. {filename}: {score:.3f}")
        else:
            logger.info("No relevant sources found after filtering")

        # Deduplicate sources based on content and filename
        seen_sources = set()
        deduplicated_sources = []

        for source in relevant_sources:
            # Create a unique identifier for the source
            content_hash = hash(source.get("content", "")[:100])  # Use first 100 chars
            filename = source.get("metadata", {}).get("file_name", "")
            source_id = (content_hash, filename)

            if source_id not in seen_sources:
                seen_sources.add(source_id)
                deduplicated_sources.append(source)
            else:
                logger.info(f"Removing duplicate source: {filename}")

        logger.info(f"Source relevance filtering: {len(sources)} -> {len(relevant_sources)} -> {len(deduplicated_sources)} sources (after deduplication)")
        return deduplicated_sources

    def _extract_key_phrases(self, query: str) -> List[str]:
        """Extract key phrases from the query for more accurate semantic matching."""
        query = query.lower().strip()

        # Common multi-word phrases that should be matched as units
        key_phrases = []

        # Extract 2-3 word phrases that are likely to be meaningful units
        words = query.split()

        # Look for common technical phrase patterns
        technical_patterns = [
            "virtual machine", "machine learning", "artificial intelligence", "deep learning",
            "neural network", "data science", "cloud computing", "web development",
            "software engineering", "database management", "user interface", "api development",
            "mobile app", "frontend development", "backend development", "full stack",
            "explain java", "java programming", "java language", "java code", "java syntax",
            "what is java", "java basics", "java tutorial", "java guide","java"
        ]

        # Check if any technical patterns exist in the query
        for pattern in technical_patterns:
            if pattern in query:
                key_phrases.append(pattern)

        # Extract 2-word combinations for other meaningful phrases
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"
            # Skip if it's already covered by technical patterns
            if phrase not in key_phrases and len(phrase) > 4:  # Avoid very short phrases
                key_phrases.append(phrase)

        # Extract 3-word combinations for more specific phrases
        for i in range(len(words) - 2):
            phrase = f"{words[i]} {words[i+1]} {words[i+2]}"
            if phrase not in key_phrases and len(phrase) > 8:  # Avoid short 3-word phrases
                key_phrases.append(phrase)

        return key_phrases

    def _is_person_name_query(self, query: str) -> bool:
        """Detect if the query is asking about a person's name."""
        # Common patterns for person name queries
        person_indicators = [
            "tell me about",
            "who is",
            "about",
            "information about",
            "details about",
            "find",
            "search for"
        ]

        # Check if query contains person indicators
        query_lower = query.lower()
        has_person_indicator = any(indicator in query_lower for indicator in person_indicators)

        # Check if query contains what looks like a person's name (two capitalized words)
        words = query.split()
        has_name_pattern = False
        for i in range(len(words) - 1):
            if (words[i].istitle() and words[i+1].istitle() and
                len(words[i]) > 1 and len(words[i+1]) > 1):
                has_name_pattern = True
                break

        # Also check for common name patterns without indicators
        if len(words) == 2 and all(word.istitle() and len(word) > 1 for word in words):
            has_name_pattern = True

        return has_person_indicator or has_name_pattern

    def _create_document_search_tool(self, workspace_ids: List[str], tenant_id: str, user_id: str):
        """Create a document search tool function for the Deep Research Agent."""
        async def document_search(query: str, user_id: str, tenant_id: str, workspace_ids: List[str]):
            try:
                # Execute document retrieval and vector search
                logger.info(f"Deep Research document search for: {query[:50]}...")
                doc_result = await self._execute_document_retrieval(query, workspace_ids)
                vector_result = await self._execute_vector_search(query, workspace_ids)

                # Combine results
                combined_content = []
                sources = []

                if doc_result and not doc_result.startswith("Error"):
                    combined_content.append(doc_result)
                    # Parse document sources from the string result
                    doc_sources = self._extract_document_content(doc_result)
                    # Convert to the format expected by Deep Research Agent
                    for doc in doc_sources:
                        sources.append({
                            "type": "document",
                            "title": doc.get("filename", "Document"),
                            "content": doc.get("content", ""),
                            "url": None,
                            "metadata": {
                                "source": "document",
                                "tool": "document_retrieval",
                                "relevance": doc.get("relevance", 0.0),
                                "source_type": "document",
                                "filename": doc.get("filename", "Document")
                            }
                        })

                if vector_result and not vector_result.startswith("Error"):
                    combined_content.append(vector_result)
                    # Parse vector search sources from the string result
                    vector_sources = self._extract_document_content(vector_result)
                    # Convert to the format expected by Deep Research Agent
                    for doc in vector_sources:
                        sources.append({
                            "type": "document",
                            "title": doc.get("filename", "Document"),
                            "content": doc.get("content", ""),
                            "url": None,
                            "metadata": {
                                "source": "vector_search",
                                "tool": "vector_search",
                                "relevance": doc.get("relevance", 0.0),
                                "source_type": "document",
                                "filename": doc.get("filename", "Document")
                            }
                        })

                logger.info(f"Deep Research document search completed: {len(sources)} sources found")
                return {
                    "summary": "\n\n".join(combined_content) if combined_content else "No relevant documents found.",
                    "sources": sources
                }
            except Exception as e:
                logger.error(f"Error in document search tool: {e}")
                return {
                    "summary": f"Error in document search: {str(e)}",
                    "sources": []
                }

        return document_search

    def _create_web_search_tool(self,question: str):
        """Create a web search tool function for the Deep Research Agent."""
        async def web_search(query: str):
            logger.info(f"Deep Research web search for: {question}...")
            try:
                result = await self._execute_web_search(question)
                if result and not result.get("content", "").startswith("Error"):
                    # Parse web sources
                    web_sources = self._parse_web_search_sources(result.get("content", ""))
                    return {
                        "summary": result.get("content", ""),
                        "sources": web_sources
                    }
                else:
                    return {
                        "summary": "No web search results found.",
                        "sources": []
                    }
            except Exception as e:
                logger.error(f"Error in web search tool: {e}")
                return {
                    "summary": f"Error in web search: {str(e)}",
                    "sources": []
                }

        return web_search

    def _create_deepseek_synthesis_tool(self):
        """Create a DeepSeek synthesis tool function for the Deep Research Agent."""
        async def deepseek_synthesis(synthesis_prompt: str):
            try:
                logger.info("Starting DeepSeek synthesis tool")

                # Use Azure DeepSeek R1 directly for synthesis
                from openai import AsyncAzureOpenAI
                import os

                # Validate environment variables
                api_key = os.getenv("DEEPSEEK_API_KEY")
                if not api_key:
                    logger.error("DEEPSEEK_API_KEY not found in environment")
                    raise ValueError("DeepSeek API key not configured")

                # Initialize Azure DeepSeek client
                client = AsyncAzureOpenAI(
                    api_key=api_key,
                    api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                    azure_endpoint=os.getenv("AZURE_OPENAI_API_ENDPOINT"),
                    azure_deployment=os.getenv("AZURE_DEEPSEEK_R1_DEPLOYMENT", "deepseek-r1")
                )

                logger.info(f"Calling DeepSeek R1 with prompt length: {len(synthesis_prompt)}")

                # Call DeepSeek R1 for synthesis
                response = await client.chat.completions.create(
                    model=os.getenv("AZURE_DEEPSEEK_R1_DEPLOYMENT", "deepseek-r1"),
                    messages=[
                        {
                            "role": "user",
                            "content": synthesis_prompt
                        }
                    ],
                    max_tokens=4000,
                    temperature=0.1
                )

                # Extract answer and thinking
                full_response = response.choices[0].message.content
                logger.info(f"DeepSeek R1 response length: {len(full_response) if full_response else 0}")

                if not full_response:
                    logger.error("DeepSeek R1 returned empty response")
                    raise ValueError("Empty response from DeepSeek R1")

                # DeepSeek R1 format: <think>...</think> followed by answer
                if "<think>" in full_response and "</think>" in full_response:
                    think_start = full_response.find("<think>") + 7
                    think_end = full_response.find("</think>")
                    thinking = full_response[think_start:think_end].strip()
                    answer = full_response[think_end + 8:].strip()
                    logger.info(f"Extracted thinking length: {len(thinking)}, answer length: {len(answer)}")
                else:
                    thinking = ""
                    answer = full_response.strip()
                    logger.info(f"No thinking tags found, using full response as answer: {len(answer)}")

                if not answer or len(answer.strip()) < 50:
                    logger.warning(f"DeepSeek synthesis returned very short answer: {len(answer)} chars")
                    raise ValueError("DeepSeek synthesis returned insufficient content")

                return answer, thinking

            except Exception as e:
                logger.error(f"Error in DeepSeek synthesis tool: {e}")
                # Fallback to simple synthesis
                fallback_answer = f"Based on the research conducted, here is a comprehensive analysis of the query. The research found relevant information that can be used to provide insights. However, due to technical limitations with DeepSeek synthesis ({str(e)}), a detailed synthesis could not be completed. Please refer to the research findings above for specific details."
                return fallback_answer, f"DeepSeek synthesis failed: {str(e)}"

        return deepseek_synthesis
