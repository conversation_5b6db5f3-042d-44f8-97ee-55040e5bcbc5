import { NextResponse } from "next/server";
import db from "@/lib/shared-db";

export const dynamic = "force-dynamic";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get("userId");
  const tenantId = searchParams.get("tenantId");
  try {
    const chats = await db.chat.findMany({
      where: {
        userId,
        tenantId,
      },
      include: {
        messages: true, // Include messages so we can filter
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter to keep only chats with no messages
    const chatsWithNoMessages = chats.filter(
      (chat) => chat?.messages?.length === 0
    );
    if (chatsWithNoMessages.length > 0) {
      return NextResponse.json(
        { id: chatsWithNoMessages[0]?.id },
        { status: 200 }
      );
    } else {
      const chat = await db.chat.create({
        data: {
          title: "New Chat",
          userId,
          tenantId,
        },
      });
      return NextResponse.json({ id: chat?.id }, { status: 200 });
    }
  } catch (error) {
    console.error("[CHAT_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
