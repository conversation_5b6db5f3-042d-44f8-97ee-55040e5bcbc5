"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  CheckCircle2,
  Loader2,
  Mail,
  ShieldAlert,
  XCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { verifyEmail, resendVerificationEmail } from "@/services/src/auth";
import toast from "react-hot-toast";

export default function VerifyEmailPage({
  params,
}: {
  params: { token: string };
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const router = useRouter();

  // Verify token when component mounts
  useEffect(() => {
    const validateToken = async () => {
      try {
        const response = await verifyEmail(params.token);

        if (response?.error) {
          setStatus("error");
          setMessage(response.error);
        } else {
          setStatus("success");
          setMessage("Your email has been verified successfully!");
          if (response.email) {
            setEmail(response.email);
          }

          // Redirect to sign in after 3 seconds on success

          router.push("/sign-in");
        }
      } catch (error) {
        console.error("Email verification error:", error);
        setStatus("error");
        setMessage("Could not verify your email. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    if (params.token && router) {
      validateToken();
    }
  }, [params.token, router]);

  const handleResendVerification = async () => {
    if (!email) {
      toast.error("No email available to resend verification");
      return;
    }

    setIsSubmitting(true);
    toast.loading("Sending verification email...");

    try {
      const response = await resendVerificationEmail(email);

      toast.dismiss();
      setIsSubmitting(false);

      if (response?.error) {
        toast.error(response.error);
        return;
      }

      toast.success("Verification email sent successfully!");
    } catch (error) {
      toast.dismiss();
      setIsSubmitting(false);
      console.error("Resend verification error:", error);
      toast.error("Failed to resend verification email. Please try again.");
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-center">Verifying your email...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Success state
  if (status === "success") {
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <CheckCircle2 className="h-10 w-10 text-green-500" />
              </div>
              <CardTitle className="text-2xl text-center">
                Email Verified
              </CardTitle>
              <CardDescription className="text-center">
                {message}
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center text-sm text-muted-foreground">
              You will be redirected to the sign in page in a few seconds.
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href="/sign-in">Sign In Now</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // Error state
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              <XCircle className="h-10 w-10 text-destructive" />
            </div>
            <CardTitle className="text-2xl text-center">
              Verification Failed
            </CardTitle>
            <CardDescription className="text-center">{message}</CardDescription>
          </CardHeader>
          <CardContent>
            {email && (
              <div className="text-center mb-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Would you like us to send a new verification email to{" "}
                  <strong>{email}</strong>?
                </p>
                <Button
                  variant="outline"
                  onClick={handleResendVerification}
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <Loader2 className="animate-spin mr-2" />
                  ) : (
                    <Mail className="mr-2 h-4 w-4" />
                  )}
                  Resend Verification Email
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button
              asChild
              className="w-full"
              variant={email ? "default" : "outline"}
            >
              <Link href="/sign-in">Back to Sign In</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
