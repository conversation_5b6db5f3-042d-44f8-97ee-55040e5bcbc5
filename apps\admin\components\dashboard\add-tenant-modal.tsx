"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "../ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { Building2, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface AddTenantModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string;
  price?: number;
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
}

interface TenantFormData {
  tenantName: string;
  planId: string;
  adminEmail: string;
  customQuotas: {
    additionalStorageGB: string; // ✅ Changed to string to allow empty values
    customTokenLimit: string; // ✅ Changed to string to allow empty values
  };
  enabledFeatures: {
    ragQuery: boolean;
    webSearch: boolean;
    documentUpload: boolean;
    chatCompletion: boolean;
    embeddingGeneration: boolean;
    textSummarization: boolean;
  };
}

interface FormErrors {
  tenantName?: string;
  planId?: string;
  adminEmail?: string;
  additionalStorageGB?: string; // ✅ Added validation error for storage
  customTokenLimit?: string; // ✅ Added validation error for token limit
  general?: string;
}

export function AddTenantModal({ open, onOpenChange }: AddTenantModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<TenantFormData>({
    tenantName: "",
    planId: "",
    adminEmail: "",
    customQuotas: {
      additionalStorageGB: "", // ✅ Default to empty string
      customTokenLimit: "", // ✅ Default to empty string
    },
    enabledFeatures: {
      ragQuery: true,
      webSearch: true,
      documentUpload: true,
      chatCompletion: false,
      embeddingGeneration: false,
      textSummarization: false,
    },
  });

  // Fetch available plans when modal opens
  useEffect(() => {
    if (open) {
      fetchPlans();
    }
  }, [open]);

  const fetchPlans = async () => {
    setLoadingPlans(true);
    try {
      const response = await fetch('/api/plans');
      if (response.ok) {
        const plansData = await response.json();
        setPlans(plansData);
      } else {
        toast({
          title: "Error",
          description: "Failed to load plans",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching plans:", error);
      toast({
        title: "Error",
        description: "Failed to load plans",
        variant: "destructive",
      });
    } finally {
      setLoadingPlans(false);
    }
  };

  // ✅ Validation functions for numeric inputs
  const validateStorageInput = (value: string): string | undefined => {
    if (value === "") return undefined; // Empty is valid
    const num = parseFloat(value);
    if (isNaN(num)) return "Must be a valid number";
    if (num < 0) return "Cannot be negative";
    if (num > 1000) return "Cannot exceed 1000 GB";
    return undefined;
  };

  const validateTokenLimitInput = (value: string): string | undefined => {
    if (value === "") return undefined; // Empty is valid
    const num = parseInt(value);
    if (isNaN(num)) return "Must be a valid whole number";
    if (num < 0) return "Cannot be negative";
    if (num > 10000000) return "Cannot exceed 10,000,000 tokens";
    return undefined;
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setErrors(prev => ({ ...prev, [field]: undefined, general: undefined }));

    if (field.startsWith('customQuotas.')) {
      const quotaField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        customQuotas: {
          ...prev.customQuotas,
          [quotaField]: value
        }
      }));

      // ✅ Real-time validation for numeric fields
      if (field === "customQuotas.additionalStorageGB") {
        const error = validateStorageInput(value as string);
        setErrors(prev => ({
          ...prev,
          additionalStorageGB: error
        }));
      } else if (field === "customQuotas.customTokenLimit") {
        const error = validateTokenLimitInput(value as string);
        setErrors(prev => ({
          ...prev,
          customTokenLimit: error
        }));
      }
    } else if (field.startsWith('enabledFeatures.')) {
      const featureField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        enabledFeatures: {
          ...prev.enabledFeatures,
          [featureField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.tenantName.trim()) {
      newErrors.tenantName = "Tenant name is required";
    }

    if (!formData.planId) {
      newErrors.planId = "Please select a plan";
    }

    // Only validate email format if email is provided (it's now optional)
    if (formData.adminEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.adminEmail)) {
        newErrors.adminEmail = "Please enter a valid email address";
      }
    }

    // ✅ Validate numeric fields
    const storageError = validateStorageInput(formData.customQuotas.additionalStorageGB);
    if (storageError) {
      newErrors.additionalStorageGB = storageError;
    }

    const tokenError = validateTokenLimitInput(formData.customQuotas.customTokenLimit);
    if (tokenError) {
      newErrors.customTokenLimit = tokenError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // ✅ Convert string values to numbers for API submission
      const submissionData = {
        ...formData,
        customQuotas: {
          additionalStorageGB: formData.customQuotas.additionalStorageGB === "" ?
            null : parseFloat(formData.customQuotas.additionalStorageGB),
          customTokenLimit: formData.customQuotas.customTokenLimit === "" ?
            null : parseInt(formData.customQuotas.customTokenLimit)
        }
      };

      const response = await fetch('/api/tenants', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (response.ok) {
        // Reset form
        setFormData({
          tenantName: "",
          planId: "",
          adminEmail: "",
          customQuotas: {
            additionalStorageGB: "", // ✅ Reset to empty string
            customTokenLimit: "", // ✅ Reset to empty string
          },
          enabledFeatures: {
            ragQuery: true,
            webSearch: true,
            documentUpload: true,
            chatCompletion: false,
            embeddingGeneration: false,
            textSummarization: false,
          },
        });

        // Close modal
        onOpenChange(false);

        // Show success message based on whether admin user was created
        toast({
          title: "Success",
          description: result.message || `Tenant "${result.tenant.name}" created successfully`,
        });

        // Navigate to tenants page to show the new tenant, or refresh if already there
        if (window.location.pathname === "/tenants") {
          router.refresh();
        } else {
          router.push("/tenants");
        }

      } else {
        setErrors({ general: result.error || "Failed to create tenant" });
      }

    } catch (error) {
      console.error("Error creating tenant:", error);
      setErrors({ general: "Failed to create tenant. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = formData.tenantName.trim() && formData.planId;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Add New Tenant
          </DialogTitle>
          <DialogDescription>
            Create a new tenant account with admin user and plan assignment.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {errors.general && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-700 dark:text-red-300">{errors.general}</span>
              </div>
            </div>
          )}

          {/* Required Fields Section */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-foreground border-b pb-2">Basic Information</h3>

            <div className="grid grid-cols-1 gap-4">
              {/* Tenant Name */}
              <div className="space-y-2">
                <Label htmlFor="tenant-name">Tenant Name *</Label>
                <Input
                  id="tenant-name"
                  placeholder="Enter tenant name"
                  value={formData.tenantName}
                  onChange={(e) => handleInputChange("tenantName", e.target.value)}
                  disabled={isLoading}
                  required
                  className={errors.tenantName ? "border-red-500" : ""}
                />
                {errors.tenantName && (
                  <p className="text-xs text-red-600">{errors.tenantName}</p>
                )}
              </div>

              {/* Plan Selection */}
              <div className="space-y-2">
                <Label htmlFor="plan-select">Plan *</Label>
                <Select
                  value={formData.planId}
                  onValueChange={(value) => handleInputChange("planId", value)}
                  disabled={isLoading || loadingPlans}
                >
                  <SelectTrigger className={errors.planId ? "border-red-500" : ""}>
                    <SelectValue placeholder={loadingPlans ? "Loading plans..." : "Select a plan"} />
                  </SelectTrigger>
                  <SelectContent>
                    {plans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{plan.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {plan.includedUsers} users • {plan.vectorStoreGB}GB storage •
                            {plan.price ? ` CHF ${plan.price}/month` : ' Custom pricing'}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.planId && (
                  <p className="text-xs text-red-600">{errors.planId}</p>
                )}
              </div>

              {/* Admin Email */}
              <div className="space-y-2">
                <Label htmlFor="admin-email">Add Admin Email (Optional)</Label>
                <Input
                  id="admin-email"
                  type="email"
                  placeholder="<EMAIL> (leave empty to add later)"
                  value={formData.adminEmail}
                  onChange={(e) => handleInputChange("adminEmail", e.target.value)}
                  disabled={isLoading}
                  className={errors.adminEmail ? "border-red-500" : ""}
                />
                {errors.adminEmail && (
                  <p className="text-xs text-red-600">{errors.adminEmail}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  If provided, this email will be used to create the tenant admin account. If left empty, admin users can be added later.
                </p>
              </div>
            </div>
          </div>

          {/* Optional Fields Section */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-foreground border-b pb-2">Optional Configuration</h3>

            {/* Initial Quota / Usage Limits */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">Initial Quota / Usage Limits</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="additional-storage">Additional Storage (GB)</Label>
                  <Input
                    id="additional-storage"
                    type="text"
                    placeholder="Enter GB amount (optional)"
                    value={formData.customQuotas.additionalStorageGB}
                    onChange={(e) => handleInputChange("customQuotas.additionalStorageGB", e.target.value)}
                    disabled={isLoading}
                    className={errors.additionalStorageGB ? "border-red-500" : ""}
                  />
                  {errors.additionalStorageGB && (
                    <p className="text-xs text-red-600">{errors.additionalStorageGB}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Additional storage beyond the plan's included amount. Leave empty for plan default.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="custom-token-limit">Custom Token Limit</Label>
                  <Input
                    id="custom-token-limit"
                    type="text"
                    placeholder="Enter token limit (optional)"
                    value={formData.customQuotas.customTokenLimit}
                    onChange={(e) => handleInputChange("customQuotas.customTokenLimit", e.target.value)}
                    disabled={isLoading}
                    className={errors.customTokenLimit ? "border-red-500" : ""}
                  />
                  {errors.customTokenLimit && (
                    <p className="text-xs text-red-600">{errors.customTokenLimit}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Custom monthly token limit. Leave empty for plan default.
                  </p>
                </div>
              </div>
            </div>

            {/* Enable Features */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">Enable Features</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-rag-query"
                      checked={formData.enabledFeatures.ragQuery}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.ragQuery", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-rag-query" className="text-sm font-normal">
                      RAG Query
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-web-search"
                      checked={formData.enabledFeatures.webSearch}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.webSearch", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-web-search" className="text-sm font-normal">
                      Web Search
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-document-upload"
                      checked={formData.enabledFeatures.documentUpload}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.documentUpload", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-document-upload" className="text-sm font-normal">
                      Document Upload
                    </Label>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-chat-completion"
                      checked={formData.enabledFeatures.chatCompletion}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.chatCompletion", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-chat-completion" className="text-sm font-normal">
                      Chat Completion
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-embedding-generation"
                      checked={formData.enabledFeatures.embeddingGeneration}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.embeddingGeneration", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-embedding-generation" className="text-sm font-normal">
                      Embedding Generation
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="feature-text-summarization"
                      checked={formData.enabledFeatures.textSummarization}
                      onCheckedChange={(checked) => handleInputChange("enabledFeatures.textSummarization", checked as boolean)}
                      disabled={isLoading}
                    />
                    <Label htmlFor="feature-text-summarization" className="text-sm font-normal">
                      Text Summarization
                    </Label>
                  </div>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Select which platform features to enable for this tenant.
              </p>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isFormValid || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Tenant...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Save & Create
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
