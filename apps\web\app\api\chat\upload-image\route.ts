import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { 
  BlobServiceClient, 
  generateBlobSASQueryParameters, 
  BlobSASPermissions, 
  StorageSharedKeyCredential 
} from "@azure/storage-blob";
import { FileSizeLimits, getFileCategory } from "@/lib/constant/supported-extensions";
import {
  isFileSupported,
  getFileCategoryByExtension,
  getDefaultMimeType
} from "@/lib/utils/file-utils";

// Helper function to truncate filename to ensure Azure Video Indexer compatibility
function truncateFilename(filename: string, maxLength: number = 50): string {
  if (filename.length <= maxLength) return filename;

  const extension = filename.substring(filename.lastIndexOf('.'));
  const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
  const maxNameLength = maxLength - extension.length;

  if (maxNameLength <= 0) return filename.substring(0, maxLength);

  return nameWithoutExt.substring(0, maxNameLength) + extension;
}
import { withPermission } from "@/lib/permission-middleware";

const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

console.log("Connection_String:", connectionString ? "SET" : "NOT SET", "Container:", containerName);

// Test account info extraction at startup
const testAccountInfo = extractAccountInfo(connectionString);
console.log("Account Info Test:", {
  accountName: testAccountInfo.accountName ? "SET" : "NOT SET",
  accountKey: testAccountInfo.accountKey ? "SET" : "NOT SET"
});

// Helper function to extract account info from connection string
function extractAccountInfo(connectionString: string): { accountName: string | null; accountKey: string | null } {
  try {
    const parts = connectionString.split(';').reduce((acc, part) => {
      const [key, value] = part.split('=', 2);
      if (key && value) {
        acc[key.toLowerCase()] = value;
      }
      return acc;
    }, {} as Record<string, string>);

    return {
      accountName: parts.accountname || null,
      accountKey: parts.accountkey || null
    };
  } catch (error) {
    console.error("Error extracting account info:", error);
    return { accountName: null, accountKey: null };
  }
}

// Helper function to generate SAS URL for media files (audio/video)
function generateSasUrlForMedia(blobName: string): string | null {
  try {
    console.log(`Starting SAS generation for blob: ${blobName}`);

    const { accountName, accountKey } = extractAccountInfo(connectionString);
    console.log(`Extracted account info - Name: ${accountName ? 'SET' : 'NOT SET'}, Key: ${accountKey ? 'SET' : 'NOT SET'}`);

    if (!accountName || !accountKey) {
      throw new Error("Could not extract account name/key for SAS generation");
    }

    // Create shared key credential
    const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);
    console.log("Created shared key credential");

    // Generate SAS token with read permissions, expires in 1 hour
    const expiryDate = new Date();
    expiryDate.setHours(expiryDate.getHours() + 1);
    console.log(`SAS token will expire at: ${expiryDate.toISOString()}`);

    const sasToken = generateBlobSASQueryParameters({
      containerName,
      blobName,
      permissions: BlobSASPermissions.parse("r"), // Read permission only
      expiresOn: expiryDate,
    }, sharedKeyCredential).toString();

    console.log(`SAS token generated: ${sasToken.substring(0, 50)}...`);

    const sasUrl = `https://${accountName}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`;
    console.log(`Complete SAS URL:`,sasUrl);

    return sasUrl;
  } catch (error) {
    console.error("Error generating SAS URL:", error);
    console.error("Error details:", error);
    return null;
  }
}

// Helper function to check if file is audio
function isAudioFile(mimeType: string, fileName: string): boolean {
  // Check MIME type first
  if (mimeType && mimeType.startsWith('audio/')) {
    return true;
  }

  // Fallback to file extension
  const audioExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac', '.wma'];
  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return audioExtensions.includes(fileExtension);
}

// Helper function to check if file is video
function isVideoFile(mimeType: string, fileName: string): boolean {
  // Check MIME type first
  if (mimeType && mimeType.startsWith('video/')) {
    return true;
  }

  // Fallback to file extension
  const videoExtensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv', '.flv', '.wmv', '.m4v', '.3gp'];
  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return videoExtensions.includes(fileExtension);
}

// Helper function to check if file needs SAS URL (audio or video)
function needsSasUrl(mimeType: string, fileName: string): boolean {
  return isAudioFile(mimeType, fileName) || isVideoFile(mimeType, fileName);
}

async function uploadImage(request: Request): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    const tenantId = formData.get("tenantId") as string | null;

    if (!file || !tenantId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate file type using enhanced logic that handles empty MIME types
    if (!isFileSupported(file)) {
      return NextResponse.json(
        { error: "Invalid file type. Supported formats: Images (JPG, PNG, WebP), Documents (PDF, TXT, CSV, Markdown, Excel), and Audio files (MP3, WAV, M4A, OGG, FLAC)." },
        { status: 400 }
      );
    }

    // Validate file size based on file category - handle empty MIME types
    let fileCategory: keyof typeof FileSizeLimits;
    if (file.type && file.type.trim() !== '') {
      fileCategory = getFileCategory(file.type);
    } else {
      fileCategory = getFileCategoryByExtension(file.name);
    }
    const maxSize = FileSizeLimits[fileCategory];
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));

    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File size too large. Maximum size for ${fileCategory} files is ${maxSizeMB}MB.` },
        { status: 400 }
      );
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path for chat files with filename truncation
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);

    // Truncate original filename to ensure Azure Video Indexer compatibility
    // Account for timestamp, randomId, and path structure
    const truncatedFilename = truncateFilename(file.name, 40);
    const blobName = `chat-files/${tenantId}/${timestamp}-${truncatedFilename}`;
    
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Upload file - set appropriate content type
    const contentType = file.type || getDefaultMimeType(file.name);
    await blockBlobClient.uploadData(arrayBuffer, {
      blobHTTPHeaders: {
        blobContentType: contentType,
        blobCacheControl: "public, max-age=********", // Cache for 1 year
      },
    });

    // Generate unique ID for the file
    const fileId = `file_${timestamp}_${randomId}`;

    // Determine the final URL to return
    let finalUrl = blockBlobClient.url;

    // Generate SAS URL for media files (audio/video) to enable Azure Video Indexer access
    console.log(`\n🔍 File Type Analysis for: ${file.name}`);
    console.log(`  Original MIME type: ${file.type}`);
    console.log(`  Detected content type: ${contentType}`);
    console.log(`  File extension: ${file.name.substring(file.name.lastIndexOf('.'))}`);
    console.log(`  isAudioFile result: ${isAudioFile(contentType, file.name)}`);
    console.log(`  isVideoFile result: ${isVideoFile(contentType, file.name)}`);
    console.log(`  needsSasUrl result: ${needsSasUrl(contentType, file.name)}`);

    if (needsSasUrl(contentType, file.name)) {
      const fileType = isAudioFile(contentType, file.name) ? 'audio' : 'video';
      console.log(`\n✅ ${fileType.toUpperCase()} file detected: ${file.name}`);
      console.log(`  File type: ${fileType}`);
      console.log(`  Blob name: ${blobName}`);
      console.log(`  Generating SAS URL...`);

      const sasUrl = generateSasUrlForMedia(blobName);

      if (sasUrl) {
        finalUrl = sasUrl;
        console.log(`✅ SAS URL generated successfully for ${fileType} file!`);
        console.log(`  Original URL: ${blockBlobClient.url}`);
        console.log(`  SAS URL: ${sasUrl.substring(0, 100)}...`);
        console.log(`  SAS token present: ${sasUrl.includes('?') && sasUrl.includes('sig=') ? 'YES' : 'NO'}`);
      } else {
        console.error(`❌ Failed to generate SAS URL for ${fileType} file: ${file.name}`);
        console.error(`  Will use regular URL as fallback`);
        // Continue with regular URL as fallback
      }
    } else {
      console.log(`ℹ️  Non-media file detected: ${file.name}`);
      console.log(`  Will use regular blob URL (no SAS needed)`);
    }

    // Prepare response data
    const responseData = {
      id: fileId,
      url: finalUrl, // Use SAS URL for audio files, regular URL for others
      name: file.name,
      type: contentType,
      size: file.size,
      message: "File uploaded successfully",
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}

// Export the POST handler with permission middleware
export const POST = withPermission(uploadImage, "CREATE", "FILE");