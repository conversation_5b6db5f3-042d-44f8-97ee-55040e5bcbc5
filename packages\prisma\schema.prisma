generator client {
    provider      = "prisma-client-js"
    binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
    provider = "mongodb"
    url      = env("DATABASE_URL")
}

enum MembershipRole {
    OWNER
    MEMBER
    ADMIN
    CUSTOM
}

enum LLMProvider {
    AZURE_OPENAI
    AZURE_DEEPSEEK
    OPENAI
    DEEPSEEK
}

enum VectorDBProvider {
    LANCEDB
    PINECONE
    CHROMADB
}

enum EmbeddingProvider {
    OPENAI
}

enum VectorizationStatus {
    PENDING
    PROCESSING
    COMPLETED
    FAILED
}

enum LLMScope {
    INTERNAL_ONLY // Only internal documents/knowledge base
    EXTERNAL_ONLY // Only external web search
    MCP_ONLY // Only MCP (Model Context Protocol) tools and external services
    HYBRID // Internal documents + web search + MCP servers
    FULL_ACCESS // Full LLM capabilities including deep research and advanced features
}

model Account {
    id                 String  @id @default(auto()) @map("_id") @db.ObjectId
    userId             String  @db.ObjectId
    type               String
    provider           String
    providerAccountId  String
    refresh_token      String?
    access_token       String?
    expires_at         Int?
    token_type         String?
    scope              String?
    id_token           String?
    session_state      String?
    oauth_token_secret String?
    oauth_token        String?
    user               User    @relation(fields: [userId], references: [id])

    @@unique([provider, providerAccountId])
    @@index([userId])
}

model Session {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    sessionToken String   @unique
    userId       String   @db.ObjectId
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
}

model User {
    id                    String    @id @default(auto()) @map("_id") @db.ObjectId
    name                  String? /// @encrypted <- annotate fields to encrypt
    email                 String    @unique /// @encrypted <- annotate fields to encrypt
    emailHash             String    @unique
    password              String?
    emailVerified         DateTime?
    image                 String?
    passwordResetRequired Boolean   @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    accounts               Account[]
    sessions               Session[]
    membership             Membership[]
    sentInvitations        Invitation[]      @relation("SentInvitations")
    workspaceMember        WorkspaceMember[]
    Chat                   Chat[]
    Message                Message[]
    ChatGroup              ChatGroup[]
    TokenUsage             TokenUsage[]
    APIRequest             APIRequest[]
    Integration            Integration[]
    GroupMember            GroupMember[]
    ThreadShare            ThreadShare[]
    Comment                Comment[]
    notifications          Notification[]
    triggeredNotifications Notification[]    @relation("NotificationTrigger")
    PageMember             PageMember[]
    MCPServer              MCPServer[]
}

model VerificationToken {
    id         String   @id @default(auto()) @map("_id") @db.ObjectId
    identifier String
    token      String   @unique
    expires    DateTime
    type       String?

    @@unique([identifier, token])
}

model Tenant {
    id                        String              @id @default(auto()) @map("_id") @db.ObjectId
    name                      String
    slug                      String
    image                     String?
    description               String?
    url                       String?
    llmScope                  String[]            @default(["INTERNAL_ONLY"]) // Organization-level LLM scope control as array
    status                    String?             @default("active") // Tenant status: active, inactive, suspended
    fileUploadLimitDuringChat Int                 @default(5) // Maximum number of files that can be uploaded during AI chat sessions
    createdAt                 DateTime            @default(now())
    updatedAt                 DateTime            @updatedAt
    isOnboarded               Boolean             @default(false)
    stripeCustomerId          String? // Stripe customer ID
    Membership                Membership[]
    workspaces                Workspace[]
    llmSettings               LLMSettings[]
    vectorDbSettings          VectorDBSettings[]
    embeddingSettings         EmbeddingSettings[]
    Invitation                Invitation[]
    Integration               Integration[]
    TokenUsage                TokenUsage[]
    APIRequest                APIRequest[]
    Subscription              Subscription[]
    VectorStoreUsage          VectorStoreUsage[]
    ChatGroup                 ChatGroup[]
    Chat                      Chat[]
    customRoles               CustomRole[]
    groups                    Group[]
    WebSearchUsage            WebSearchUsage[]
    ThreadShare               ThreadShare[]
    Comment                   Comment[]
    Notification              Notification[]
    builtInRoleConfigs        BuiltInRoleConfig[]
    MCPServer                 MCPServer[]

    @@unique([slug, id])
    @@index([slug])
}

model Membership {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    role         MembershipRole
    tenantId     String         @db.ObjectId
    userId       String         @db.ObjectId
    user         User           @relation(fields: [userId], references: [id])
    tenant       Tenant         @relation(fields: [tenantId], references: [id])
    customRoleId String?        @db.ObjectId
    customRole   CustomRole?    @relation(fields: [customRoleId], references: [id])

    createdAt       DateTime          @default(now())
    updatedAt       DateTime          @updatedAt
    workspaceMember WorkspaceMember[]

    @@unique([tenantId, userId]) // Prevents duplicate memberships
    @@index([tenantId])
    @@index([userId])
    @@index([customRoleId])
}

model Workspace {
    id                String                @id @default(auto()) @map("_id") @db.ObjectId
    name              String
    slug              String
    description       String?
    initials          String?
    oneDriveFolderId  String?
    gDriveFolderId    String?
    createdAt         DateTime              @default(now())
    updatedAt         DateTime              @updatedAt
    tenantId          String                @db.ObjectId
    tenant            Tenant                @relation(fields: [tenantId], references: [id])
    pages             Page[]
    folders           Folder[]
    files             File[]
    LLMSettings       LLMSettings[]
    VectorDBSettings  VectorDBSettings[]
    EmbeddingSettings EmbeddingSettings[]
    workspaceMember   WorkspaceMember[]
    customRoles       CustomRoleWorkspace[]
    groupWorkspaces   GroupWorkspace[]

    @@unique([slug, tenantId])
    @@index([tenantId])
}

model Page {
    id                   String  @id @default(auto()) @map("_id") @db.ObjectId
    name                 String
    content              String?
    oneDriveFolderId     String?
    gDriveFolderId       String?
    sharePointSiteId     String?
    sharePointDriveId    String?
    sharePointFolderPath String? // Path to the synced SharePoint folder

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String       @db.ObjectId
    workspace   Workspace    @relation(fields: [workspaceId], references: [id])
    folders     Folder[]
    files       File[]
    pageMembers PageMember[]

    @@index([workspaceId])
    @@index([sharePointSiteId, sharePointDriveId])
}

model Folder {
    id               String  @id @default(auto()) @map("_id") @db.ObjectId
    name             String
    oneDriveFolderId String?
    gDriveFolderId   String?
    parentId         String? @db.ObjectId

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String    @db.ObjectId
    pageId      String?   @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])
    page        Page?     @relation(fields: [pageId], references: [id])

    parentRelations FolderHierarchy[] @relation("FolderParents")
    childRelations  FolderHierarchy[] @relation("FolderChildren")
    files           File[]

    @@index([workspaceId])
}

model FolderHierarchy {
    id       String  @id @default(auto()) @map("_id") @db.ObjectId
    parentId String  @db.ObjectId
    childId  String  @db.ObjectId
    pageId   String? @db.ObjectId

    parent Folder @relation("FolderParents", fields: [parentId], references: [id], onDelete: Cascade)
    child  Folder @relation("FolderChildren", fields: [childId], references: [id], onDelete: Cascade)

    @@unique([parentId, childId]) // Prevents duplicate relationships
}

model File {
    id                  String              @id @default(auto()) @map("_id") @db.ObjectId
    name                String
    type                String
    extension           String?
    gDriveFileId        String?
    oneDriveFileId      String?
    size                String?
    url                 String?
    metadata            Json?
    content             String?
    vectorizationStatus VectorizationStatus @default(PENDING)
    vectorizedAt        DateTime?
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    workspaceId         String              @db.ObjectId
    parentId            String?             @db.ObjectId
    pageId              String?             @db.ObjectId
    folderId            String?             @db.ObjectId
    workspace           Workspace           @relation(fields: [workspaceId], references: [id])
    page                Page?               @relation(fields: [pageId], references: [id])
    folder              Folder?             @relation(fields: [folderId], references: [id])

    @@index([workspaceId])
}

model LLMSettings {
    id                    String      @id @default(auto()) @map("_id") @db.ObjectId
    provider              LLMProvider
    apiKey                String?
    // Azure OpenAI fields
    azureOpenAIModel      String?
    azureOpenAIApiVersion String?
    azureOpenAIDeployment String?
    azureOpenAIEndpoint   String?
    // OpenAI fields
    baseUrl               String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model VectorDBSettings {
    id          String           @id @default(auto()) @map("_id") @db.ObjectId
    provider    VectorDBProvider
    // LanceDB fields
    localUri    String?
    // Pinecone fields
    apiKey      String?
    environment String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model EmbeddingSettings {
    id            String            @id @default(auto()) @map("_id") @db.ObjectId
    provider      EmbeddingProvider
    // OpenAI Embeddings fields
    apiKey        String?
    embedderModel String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Invitation {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    email        String /// @encrypted <- annotate fields to encrypt
    emailHash    String
    role         MembershipRole
    customRoleId String?        @db.ObjectId
    tenantId     String         @db.ObjectId
    inviterId    String         @db.ObjectId
    token        String         @unique
    expires      DateTime
    accepted     Boolean        @default(false)

    tenant     Tenant      @relation(fields: [tenantId], references: [id])
    inviter    User        @relation("SentInvitations", fields: [inviterId], references: [id])
    customRole CustomRole? @relation(fields: [customRoleId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([inviterId])
    @@index([email])
    @@index([customRoleId])
}

model WorkspaceMember {
    id   String         @id @default(auto()) @map("_id") @db.ObjectId
    role MembershipRole

    workspaceId  String      @db.ObjectId
    userId       String      @db.ObjectId
    membershipId String      @db.ObjectId
    customRoleId String?     @db.ObjectId
    workspace    Workspace   @relation(fields: [workspaceId], references: [id])
    user         User        @relation(fields: [userId], references: [id])
    membership   Membership? @relation(fields: [membershipId], references: [id], onDelete: Cascade)
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([workspaceId, userId])
    @@index([workspaceId])
    @@index([userId])
    @@index([customRoleId])
}

enum PageMemberRole {
    MEMBER // Read access
    EDITOR // Read/Write access
    ADMIN // Full access including member management
}

enum PageMemberSource {
    MANUAL // Manually added by page admin
    SHAREPOINT // Automatically added via SharePoint access
    WORKSPACE // Inherited from workspace membership
}

model PageMember {
    id     String           @id @default(auto()) @map("_id") @db.ObjectId
    role   PageMemberRole
    source PageMemberSource @default(MANUAL)

    pageId   String @db.ObjectId
    userId   String @db.ObjectId
    tenantId String @db.ObjectId

    // Relations
    page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)
    user User @relation(fields: [userId], references: [id])

    // SharePoint metadata (when source is SHAREPOINT)
    sharePointPermissionLevel String? // Original SharePoint permission level
    lastSharePointCheck       DateTime?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([pageId, userId])
    @@index([pageId])
    @@index([userId])
    @@index([tenantId])
    @@index([source])
}

model ChatGroup {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    isArchived  Boolean @default(false)

    userId   String @db.ObjectId
    user     User   @relation(fields: [userId], references: [id])
    tenantId String @db.ObjectId
    teant    Tenant @relation(fields: [tenantId], references: [id])

    chats     Chat[]
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
}

model Chat {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    title       String?
    description String?
    isArchived  Boolean @default(false)

    userId   String     @db.ObjectId
    user     User       @relation(fields: [userId], references: [id])
    groupId  String?    @db.ObjectId
    group    ChatGroup? @relation(fields: [groupId], references: [id])
    tenantId String     @db.ObjectId
    teant    Tenant     @relation(fields: [tenantId], references: [id])

    messages        Message[]
    createdAt       DateTime         @default(now())
    updatedAt       DateTime         @updatedAt
    ThreadShare     ThreadShare[]
    Notification    Notification[]
    mcpChatSessions MCPChatSession[]

    @@index([userId])
    @@index([groupId])
}

model Message {
    id       String @id @default(auto()) @map("_id") @db.ObjectId
    content  String /// @encrypted <- annotate fields to encrypt
    role     String // 'user' or 'assistant'
    metadata Json? // For storing additional message-specific data
    sources  Json? // For storing document sources/citations

    // New field for tracking regenerated messages
    originalMessageId   String?   @db.ObjectId // If this is a regenerated message, points to the original message
    regeneratedMessages Message[] @relation("RegeneratedMessages") // Messages that are regenerations of this one
    originalMessage     Message?  @relation("RegeneratedMessages", fields: [originalMessageId], references: [id], onDelete: NoAction, onUpdate: NoAction)

    chatId String @db.ObjectId
    chat   Chat   @relation(fields: [chatId], references: [id], onDelete: Cascade)
    userId String @db.ObjectId
    user   User   @relation(fields: [userId], references: [id])

    createdAt    DateTime       @default(now())
    updatedAt    DateTime       @updatedAt
    Comment      Comment[]
    Notification Notification[]

    @@index([chatId])
    @@index([userId])
}

enum IntegrationType {
    OUTLOOK
    GOOGLE
}

model Integration {
    id           String          @id @default(auto()) @map("_id") @db.ObjectId
    platform     IntegrationType
    accountId    String
    accountName  String?
    accessToken  String
    page         Json?
    refreshToken String?
    config       Json?
    tenantId     String          @db.ObjectId
    tenant       Tenant          @relation(fields: [tenantId], references: [id])
    userId       String          @db.ObjectId
    user         User            @relation(fields: [userId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([tenantId])
    @@map("integration")
}

model TokenUsage {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String?  @db.ObjectId
    user         User?    @relation(fields: [userId], references: [id])
    inputTokens  Int
    outputTokens Int
    timestamp    DateTime @default(now())
    requestType  String // e.g., "chat", "embedding", "completion"
    modelUsed    String // The specific model used for this request
    cost         Float // Estimated cost in USD
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

model APIRequest {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String   @db.ObjectId
    user         User     @relation(fields: [userId], references: [id])
    endpoint     String // The API endpoint called
    method       String // HTTP method used
    statusCode   Int // Response status code
    timestamp    DateTime @default(now())
    duration     Int // Request duration in milliseconds
    success      Boolean // Whether the request was successful
    errorMessage String? // Error message if request failed
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

enum PlanType {
    STARTER
    BUSINESS
    CUSTOM
    ENTERPRISE
}

model Plan {
    id                      String         @id @default(auto()) @map("_id") @db.ObjectId
    name                    String
    type                    PlanType
    description             String?
    stripePriceId           String? // Monthly price ID for the base plan
    stripeYearlyPriceId     String? // Yearly price ID for the base plan
    webSearchLimit          Int            @default(50) // Daily web search limit
    includedUsers           Int
    additionalUserFee       Float // For display purposes only
    stripeUserPriceId       String? // Monthly price ID for additional users
    stripeUserYearlyPriceId String? // Yearly price ID for additional users
    vectorStoreGB           Int
    price                   Float? // Base price of the plan (for display purposes only)
    isActive                Boolean        @default(true)
    createdAt               DateTime       @default(now())
    updatedAt               DateTime       @updatedAt
    subscriptions           Subscription[]
}

// Define a type for storage tier items in the subscription
type StorageTierItem {
    id            String
    size          Int
    quantity      Int
    price         Float
    stripePriceId String?
}

model Subscription {
    id                   String            @id @default(auto()) @map("_id") @db.ObjectId
    tenantId             String            @db.ObjectId
    tenant               Tenant            @relation(fields: [tenantId], references: [id])
    planId               String            @db.ObjectId
    plan                 Plan              @relation(fields: [planId], references: [id])
    startDate            DateTime          @default(now())
    endDate              DateTime?
    isActive             Boolean           @default(true)
    isOnTrial            Boolean           @default(false) // Whether the subscription is on a trial period
    trialEndDate         DateTime? // When the trial period ends
    additionalUsers      Int               @default(0)
    additionalStorageGB  Int               @default(0) // Additional vector storage in GB (for backward compatibility)
    storageTierItems     StorageTierItem[] // Array of storage tier objects
    stripeSubscriptionId String? // Main Stripe subscription ID
    stripeCustomerId     String? // Stripe customer ID
    billingInterval      String? // "month" or "year"
    createdAt            DateTime          @default(now())
    updatedAt            DateTime          @updatedAt

    @@index([tenantId])
    @@index([planId])
}

model StorageTier {
    id                  String   @id @default(auto()) @map("_id") @db.ObjectId
    name                String // e.g., "10GB", "50GB", "100GB"
    sizeGB              Int // Size in GB
    price               Float // Price for display purposes
    stripePriceId       String // Monthly price ID
    stripeYearlyPriceId String // Yearly price ID
    isActive            Boolean  @default(true)
    createdAt           DateTime @default(now())
    updatedAt           DateTime @updatedAt
}

model VectorStoreUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    usageGB   Float
    timestamp DateTime @default(now())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([timestamp])
}

enum PermissionAction {
    CREATE
    READ
    UPDATE
    DELETE
}

enum PermissionResource {
    WORKSPACE
    PAGE
    FOLDER
    FILE
    MEMBER
}

model Permission {
    id          String             @id @default(auto()) @map("_id") @db.ObjectId
    action      PermissionAction
    resource    PermissionResource
    description String?
    createdAt   DateTime           @default(now())
    updatedAt   DateTime           @updatedAt

    // Relations
    customRoles  CustomRolePermission[]
    builtInRoles BuiltInRolePermission[]

    @@unique([action, resource])
}

model CustomRole {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    isDefault   Boolean  @default(false)
    tenantId    String   @db.ObjectId
    tenant      Tenant   @relation(fields: [tenantId], references: [id])
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    permissions     CustomRolePermission[]
    memberships     Membership[]
    WorkspaceMember WorkspaceMember[]
    Invitation      Invitation[]
    groups          Group[]
    workspaces      CustomRoleWorkspace[]
    groupWorkspaces GroupWorkspace[]

    @@unique([name, tenantId])
    @@index([tenantId])
}

model CustomRoleWorkspace {
    id           String     @id @default(auto()) @map("_id") @db.ObjectId
    customRoleId String     @db.ObjectId
    workspaceId  String     @db.ObjectId
    customRole   CustomRole @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
    workspace    Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt

    @@unique([customRoleId, workspaceId])
    @@index([customRoleId])
    @@index([workspaceId])
}

model CustomRolePermission {
    id           String     @id @default(auto()) @map("_id") @db.ObjectId
    customRoleId String     @db.ObjectId
    permissionId String     @db.ObjectId
    customRole   CustomRole @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
    permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt

    @@unique([customRoleId, permissionId])
    @@index([customRoleId])
    @@index([permissionId])
}

model BuiltInRoleConfig {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    roleType  String // ADMIN, MEMBER
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    permissions BuiltInRolePermission[]

    @@unique([roleType, tenantId])
    @@index([tenantId])
}

model BuiltInRolePermission {
    id                  String            @id @default(auto()) @map("_id") @db.ObjectId
    builtInRoleConfigId String            @db.ObjectId
    permissionId        String            @db.ObjectId
    builtInRoleConfig   BuiltInRoleConfig @relation(fields: [builtInRoleConfigId], references: [id], onDelete: Cascade)
    permission          Permission        @relation(fields: [permissionId], references: [id], onDelete: Cascade)
    createdAt           DateTime          @default(now())
    updatedAt           DateTime          @updatedAt

    @@unique([builtInRoleConfigId, permissionId])
    @@index([builtInRoleConfigId])
    @@index([permissionId])
}

model Group {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    tenantId        String           @db.ObjectId
    tenant          Tenant           @relation(fields: [tenantId], references: [id])
    groupMembers    GroupMember[]
    groupWorkspaces GroupWorkspace[]

    // Custom role relation for workspace access
    customRoleId String?     @db.ObjectId
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    @@unique([name, tenantId])
    @@index([tenantId])
    @@index([customRoleId])
}

model GroupMember {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    groupId String @db.ObjectId
    userId  String @db.ObjectId
    group   Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
    user    User   @relation(fields: [userId], references: [id])

    @@unique([groupId, userId])
    @@index([groupId])
    @@index([userId])
}

model GroupWorkspace {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    groupId     String    @db.ObjectId
    workspaceId String    @db.ObjectId
    group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

    // Optional custom role for this specific group-workspace association
    customRoleId String?     @db.ObjectId
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    @@unique([groupId, workspaceId])
    @@index([groupId])
    @@index([workspaceId])
    @@index([customRoleId])
}

model WebSearchUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    userId    String   @db.ObjectId
    query     String
    cached    Boolean  @default(false)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([userId])
    @@index([createdAt])
}

// Collaborative Features Models

model ThreadShare {
    id          String    @id @default(auto()) @map("_id") @db.ObjectId
    chatId      String    @db.ObjectId
    chat        Chat      @relation(fields: [chatId], references: [id], onDelete: Cascade)
    shareToken  String    @unique // URL-safe token for sharing
    isPublic    Boolean   @default(false) // If true, anyone with link can view
    expiresAt   DateTime? // Optional expiration date
    createdById String    @db.ObjectId
    createdBy   User      @relation(fields: [createdById], references: [id])
    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    createdAt   DateTime  @default(now())
    updatedAt   DateTime  @updatedAt

    @@index([chatId])
    @@index([tenantId])
}

enum CommentStatus {
    ACTIVE
    RESOLVED
    DELETED
}

model Comment {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    content      String
    messageId    String         @db.ObjectId
    message      Message        @relation(fields: [messageId], references: [id], onDelete: Cascade)
    authorId     String         @db.ObjectId
    author       User           @relation(fields: [authorId], references: [id])
    parentId     String?        @db.ObjectId // For threaded comments (1-level only)
    parent       Comment?       @relation("CommentThread", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
    replies      Comment[]      @relation("CommentThread")
    status       CommentStatus  @default(ACTIVE)
    mentions     Json? // Array of mentioned user IDs
    tenantId     String         @db.ObjectId
    tenant       Tenant         @relation(fields: [tenantId], references: [id])
    createdAt    DateTime       @default(now())
    updatedAt    DateTime       @updatedAt
    Notification Notification[]

    @@index([messageId])
    @@index([authorId])
    @@index([parentId])
    @@index([tenantId])
    @@index([status])
}

enum NotificationType {
    MENTION
    COMMENT_REPLY
    THREAD_SHARED
}

enum NotificationStatus {
    UNREAD
    READ
    DISMISSED
}

model Notification {
    id          String             @id @default(auto()) @map("_id") @db.ObjectId
    type        NotificationType
    title       String
    content     String // Changed from 'message' to avoid conflict
    status      NotificationStatus @default(UNREAD)
    userId      String             @db.ObjectId
    user        User               @relation(fields: [userId], references: [id])
    triggeredBy String?            @db.ObjectId // User who triggered the notification
    triggerUser User?              @relation("NotificationTrigger", fields: [triggeredBy], references: [id])

    // Related entities
    chatId         String?  @db.ObjectId
    chat           Chat?    @relation(fields: [chatId], references: [id], onDelete: Cascade)
    messageId      String?  @db.ObjectId
    relatedMessage Message? @relation(fields: [messageId], references: [id], onDelete: Cascade)
    commentId      String?  @db.ObjectId
    comment        Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

    metadata  Json? // Additional notification data
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([status])
    @@index([type])
    @@index([tenantId])
    @@index([createdAt])
}

// MCP (Model Context Protocol) Server Integration Models

enum MCPServerStatus {
    ACTIVE
    INACTIVE
    ERROR
    TESTING
}

enum MCPServerType {
    STDIO // Command-line based MCP servers (npx, python scripts, etc.)
    HTTP // HTTP-based MCP servers with SSE support
}

model MCPServer {
    id          String          @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    serverType  MCPServerType   @default(STDIO) // Type of MCP server (stdio or http)
    command     String? // MCP server command (e.g., "npx -y @upstash/context7-mcp@latest") - required for stdio
    args        Json? // Additional command arguments as JSON array - for stdio servers
    env         Json? // Environment variables as JSON object - for stdio servers
    url         String? // HTTP URL for HTTP-based servers - required for http
    headers     Json? // HTTP headers as JSON object - for http servers (e.g., Authorization)
    status      MCPServerStatus @default(INACTIVE)
    lastError   String? // Last error message if status is ERROR

    // Ownership and access control
    userId   String @db.ObjectId
    user     User   @relation(fields: [userId], references: [id])
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id])

    // Configuration
    timeout     Int     @default(30000) // Timeout in milliseconds
    autoRestart Boolean @default(true) // Auto-restart on failure
    isPublic    Boolean @default(false) // If true, available to all tenant users

    // Relationships
    mcpChatSessions MCPChatSession[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([name, tenantId]) // Unique name per tenant
    @@index([userId])
    @@index([tenantId])
    @@index([status])
}

model MCPChatSession {
    id String @id @default(auto()) @map("_id") @db.ObjectId

    // Relationships
    chatId      String    @db.ObjectId
    chat        Chat      @relation(fields: [chatId], references: [id], onDelete: Cascade)
    mcpServerId String    @db.ObjectId
    mcpServer   MCPServer @relation(fields: [mcpServerId], references: [id], onDelete: Cascade)

    // Session data
    sessionData Json? // Store MCP session-specific data
    isActive    Boolean @default(true)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([chatId, mcpServerId]) // One session per MCP server per chat
    @@index([chatId])
    @@index([mcpServerId])
}
