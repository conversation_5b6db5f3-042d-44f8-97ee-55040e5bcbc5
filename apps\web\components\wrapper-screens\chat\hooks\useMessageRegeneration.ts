import { useState } from "react";
import { toast } from "react-hot-toast";
import { Message } from "../types";
import { useLanguage } from "@/lib/language-context";
import { createMessage, updateMessage } from "@/services/src/message";
import {
  createTemporaryMessage,
  addTemporaryMessage,
  updateStreamingMessage,
} from "../utils/message-utils";
import {
  copilotKitChatService,
  CopilotKitChatQuery,
} from "@/services/copilotkit-chat";

export const useMessageRegeneration = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  chatId: string | null,
  userId: string,
  tenantId: string,
  userName: string,
  headers: Record<string, string>,
  includeWebResults: boolean,
  updateDisplayIndex: (
    messageId: string,
    newIndex: number,
    isManuallySet: boolean
  ) => void,
  imageContext: string,
  selectedSearchMode: any
) => {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);

  const handleRegenerate = async (messageIndex?: number) => {
    if (!chatId || messages.length < 2) return;
    try {
      // Find the target assistant message to regenerate
      let assistantMessage: Message | null = null;
      let lastUserMessage = "";
      let targetAssistantIndex = -1;

      if (messageIndex !== undefined) {
        // Use the specific message index provided
        const targetMessage = messages[messageIndex];

        if (targetMessage && targetMessage.role === "assistant") {
          assistantMessage = targetMessage;
          targetAssistantIndex = messageIndex;

          // Find the user message that prompted this assistant message
          for (let i = messageIndex - 1; i >= 0; i--) {
            console.log({ messages });
            if (messages[i].role === "user") {
              lastUserMessage = messages[i].content;
              break;
            }
          }
        }
      } else {
        // Fallback to the last assistant message (existing behavior)
        let lastAssistantIndex = messages.length - 1;
        if (messages[lastAssistantIndex].role !== "assistant") {
          // If the last message is not from assistant, find the last assistant message
          while (
            lastAssistantIndex >= 0 &&
            messages[lastAssistantIndex].role !== "assistant"
          ) {
            lastAssistantIndex--;
          }
        }

        if (lastAssistantIndex >= 0) {
          assistantMessage = messages[lastAssistantIndex];
          targetAssistantIndex = lastAssistantIndex;

          // Find the user message that prompted this assistant message
          for (let i = lastAssistantIndex - 1; i >= 0; i--) {
            if (messages[i].role === "user") {
              lastUserMessage = messages[i].content;
              break;
            }
          }
        }
      }

      if (!assistantMessage || !lastUserMessage || targetAssistantIndex < 0) {
        toast.error(t("chat.regenerateError"));
        setIsLoading(false);
        return;
      }

      // TypeScript assertion: assistantMessage is guaranteed to be non-null after the check above
      const nonNullAssistantMessage = assistantMessage as Message;

      // Show toast notification
      setIsLoading(true);
      toast.loading(t("chat.regenerating"));

      // Make a direct API call to get a new response
      try {
        // Build the context messages up to the target assistant message
        const contextMessages = messages
          .slice(0, targetAssistantIndex)
          .map((msg) => ({
            id: msg.id || Math.random().toString(),
            role: msg.role,
            content: msg.content,
            createdAt: new Date(),
          }));

        // Add the user message that prompted the assistant response
        contextMessages.push({
          id: Math.random().toString(),
          role: "user",
          content: lastUserMessage,
          createdAt: new Date(),
        });

        // Find the original user message that prompted this assistant response to get its images
        let userMessageWithImages: Message | null = null;
        for (let i = targetAssistantIndex - 1; i >= 0; i--) {
          if (messages[i].role === "user") {
            userMessageWithImages = messages[i];
            break;
          }
        }

        // Extract images from the user message (check both images and metadata.images)
        const imageAttachments =
          userMessageWithImages?.images ||
          userMessageWithImages?.metadata?.images ||
          [];

        // Prepare CopilotKit query for regeneration
        const query: CopilotKitChatQuery = {
          question: lastUserMessage,
          stream: true,
          search_mode: selectedSearchMode,
          include_web_results: includeWebResults,
          images:
            imageAttachments.length > 0
              ? imageAttachments
                  .map((img) =>
                    typeof img === "string"
                      ? img
                      : img.data || img.preview || ""
                  )
                  .filter(Boolean)
              : undefined,
          image_context: imageContext,
        };

        // Set up CopilotKit streaming response handling
        let streamedContent = "";
        let regeneratedSources: any[] = [];
        let currentToolsUsed: string[] = [];
        let responseTime = 0;
        let thinkingContent = "";

        // Create a temporary message for streaming
        const tempMessage = createTemporaryMessage(nonNullAssistantMessage);

        // Add the temporary message to the UI as a nested regenerated message
        setMessages((prevMessages) =>
          addTemporaryMessage(
            prevMessages,
            nonNullAssistantMessage,
            tempMessage
          )
        );

        // Remove the loading toast now that streaming is set up
        toast.remove();

        // Update the display index to show the streaming message immediately
        if (nonNullAssistantMessage.id) {
          // Count existing regenerated messages and add 1 for the new temporary message
          const editedCount =
            nonNullAssistantMessage.editedMessages?.length || 0;
          const regeneratedCount =
            (nonNullAssistantMessage.regeneratedMessages?.length || 0) + 1;
          const streamingDisplayIndex = editedCount + regeneratedCount;

          // Use our combined update function to update the display index immediately
          // This ensures the user sees the streaming message right away
          updateDisplayIndex(
            nonNullAssistantMessage.id,
            streamingDisplayIndex,
            false // Not manually set - this allows the streaming message to be shown automatically
          );
        }

        // Remove the loading toast after the streaming setup is complete
        toast.remove();

        // Stream the response using CopilotKit
        try {
          for await (const chunk of copilotKitChatService.streamChat(
            query,
            userId,
            tenantId,
            userName
          )) {
            if (chunk.error) {
              throw new Error(chunk.error);
            }

            // Handle different chunk types
            if (chunk.sources) {
              regeneratedSources = chunk.sources;
            }

            if (chunk.tools_used) {
              currentToolsUsed = chunk.tools_used;
            }

            if (chunk.thinking) {
              thinkingContent = chunk.thinking;
            }

            if (chunk.answer_chunk) {
              streamedContent += chunk.answer_chunk;
            } else if (chunk.answer) {
              streamedContent = chunk.answer;
            }

            if (chunk.elapsed_time) {
              responseTime = chunk.elapsed_time;
            }

            // Update the temporary message with the streaming content
            setMessages((prevMessages) =>
              updateStreamingMessage(
                prevMessages,
                nonNullAssistantMessage,
                streamedContent,
                regeneratedSources
              )
            );

            if (chunk.done) {
              break;
            }
          }

          // Clean up the final content
          let finalContent = streamedContent;
          if (finalContent.startsWith("Answer:")) {
            finalContent = finalContent.replace("Answer:", "").trim();
          }

          if (finalContent.startsWith("ERROR:")) {
            finalContent =
              "Error: " + finalContent.replace("ERROR:", "").trim();
          }

          // Create a new message in the database for the regenerated response
          if (chatId && nonNullAssistantMessage.id) {
            try {
              // Create a new message with a reference to the original
              const newMessage = await createMessage({
                chatId,
                userId,
                content: finalContent,
                role: "assistant",
                metadata: {
                  regeneratedResponse: true,
                  isSelected: true,
                  tools_used: currentToolsUsed,
                  elapsed_time: responseTime,
                  thinking: thinkingContent,
                  imageContext,
                },
                originalMessageId: nonNullAssistantMessage.id,
                sources: regeneratedSources,
              });

              if (newMessage && !newMessage.error) {
                // Update the original message to mark it as having regenerated versions
                await updateMessage({
                  chatId,
                  messageId: nonNullAssistantMessage.id,
                  metadata: {
                    ...nonNullAssistantMessage.metadata,
                    originalResponse: true,
                  },
                });

                // Update the UI with the final regenerated message
                setMessages((prevMessages) => {
                  return prevMessages.map((msg) => {
                    // Find the original message
                    if (
                      msg.id === nonNullAssistantMessage.id &&
                      msg.regeneratedMessages
                    ) {
                      // Find and update the temporary message in the regeneratedMessages array
                      const updatedRegeneratedMessages =
                        msg.regeneratedMessages.map((regMsg) => {
                          // Find the temporary message by checking for missing ID
                          if (
                            !regMsg.id &&
                            regMsg.metadata?.regeneratedResponse
                          ) {
                            // Update the temporary message with the final ID and content
                            return {
                              ...regMsg,
                              id: newMessage.id,
                              content: finalContent,
                              sources: regeneratedSources,
                              metadata: {
                                ...regMsg.metadata,
                                isStreaming: false, // Remove streaming flag from completed message
                                tools_used: currentToolsUsed,
                                elapsed_time: responseTime,
                                thinking: thinkingContent,
                              },
                            };
                          }
                          return regMsg;
                        });

                      // Return the updated original message with streaming flag removed
                      return {
                        ...msg,
                        metadata: {
                          ...msg.metadata,
                          isStreaming: false, // Remove streaming flag
                        },
                        regeneratedMessages: updatedRegeneratedMessages,
                      };
                    }
                    return msg;
                  });
                });

                // Update the display index to show the newest regenerated message
                if (nonNullAssistantMessage.id) {
                  // Count how many regenerated messages this message has now
                  const editedCount =
                    nonNullAssistantMessage.editedMessages?.length || 0;
                  const regeneratedCount =
                    nonNullAssistantMessage.regeneratedMessages?.length || 0;
                  const finalDisplayIndex = editedCount + regeneratedCount;

                  // Use our combined update function to update the display index
                  updateDisplayIndex(
                    nonNullAssistantMessage.id,
                    finalDisplayIndex,
                    false // Not manually set
                  );
                }
              }
            } catch (error) {
              console.error("Error saving regenerated message:", error);
            }
          }
        } catch (apiError) {
          console.error("API error during regeneration:", apiError);
          toast.error(t("chat.regenerateError"));

          // Remove the temporary message from the regeneratedMessages array if there was an error
          setMessages((prevMessages) => {
            return prevMessages.map((msg) => {
              if (
                msg.id === nonNullAssistantMessage.id &&
                msg.regeneratedMessages
              ) {
                // Filter out the temporary streaming message from regeneratedMessages
                const filteredRegeneratedMessages =
                  msg.regeneratedMessages.filter(
                    (regMsg) =>
                      !(
                        regMsg.metadata?.regeneratedResponse &&
                        !regMsg.id &&
                        regMsg.metadata?.isStreaming
                      )
                  );

                // Return the updated original message with streaming flag removed
                return {
                  ...msg,
                  metadata: {
                    ...msg.metadata,
                    isStreaming: false, // Remove streaming flag
                  },
                  regeneratedMessages: filteredRegeneratedMessages,
                };
              }
              return msg;
            });
          });
        } finally {
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error regenerating response:", error);
        toast.error(t("chat.regenerateError"));
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error regenerating response:", error);
      toast.error(t("chat.regenerateError"));
      setIsLoading(false);
    }
  };

  return {
    handleRegenerate,
    isLoading,
  };
};
