import { NextRequest, NextResponse } from "next/server";
import { google } from "googleapis";
import db from "@/lib/shared-db";
import { workspaceChatService } from "@/services/workspace-chat";
import { getFileExtension } from "@/lib/utils/file-utils";
import { BlobServiceClient } from "@azure/storage-blob";
import { getServerSession } from "next-auth";
import jwt from "jsonwebtoken";
import { authOptions } from "@/lib/next-auth";
import { SupportedExtensions } from "@/lib/constant/supported-extensions";

interface SyncOptions {
  folderId: string;
  tenantId: string;
  userId: string;
  workspaceSlug?: string;
  pageId?: string;
  workspaceId?: string;
  slug: string;
  recursive: boolean;
  checkSubFiles: boolean;
  parentFolderId?: string; // Added to track parent folder ID
}

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

// Helper function to determine document type based on file extension
const getDocumentType = (filename: string): string => {
  const extension = getFileExtension(filename);

  switch (extension) {
    case 'md':
    case 'mdx':
    case 'markdown':
      return 'markitdown'; // Use markitdown for better markdown processing
    case 'pdf':
      return 'pdf';
    case 'txt':
      return 'text';
    case 'csv':
      return 'csv';
    case 'docx':
    case 'doc':
      return 'word';
    case 'xlsx':
    case 'xls':
      return 'excel';
    case 'pptx':
    case 'ppt':
      return 'powerpoint';
    default:
      return 'auto'; // Let the backend auto-detect
  }
};

const getGoogleDriveClient = async (tenantId: string, userId: string) => {
  const integration = await db.integration.findFirst({
    where: { tenantId, platform: "GOOGLE", userId },
  });

  if (!integration) {
    throw new Error("Google integration not found");
  }

  const oauth2Client = new google.auth.OAuth2(
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${APP_URL}/api/integration/google/callback`
  );

  oauth2Client.setCredentials({
    access_token: integration.accessToken,
    refresh_token: integration.refreshToken,
  });

  // Refresh access token if expired
  try {
    const { credentials } = await oauth2Client.refreshAccessToken();
    oauth2Client.setCredentials(credentials);

    // Optionally, update the database with the new access token
    await db.integration.update({
      where: { id: integration?.id },
      data: { accessToken: credentials?.access_token },
    });
  } catch (error) {
    console.error("Failed to refresh access token:", error);
  }

  return google.drive({ version: "v3", auth: oauth2Client });
};

async function listFolderContents(drive: any, folderId: string) {
  try {
    const response = await drive.files.list({
      q: `'${folderId}' in parents and trashed = false`,
      fields: "files(id, name, mimeType, modifiedTime, size, webContentLink)",
      pageSize: 1000, // Increase page size to handle larger folders
    });
    return response.data.files;
  } catch (error) {
    console.error("Error listing folder contents:", error);
    throw error;
  }
}

async function getFileContent(drive: any, fileId: string, mimeType: string) {
  try {
    // For Google Docs, Sheets, etc., export as PDF
    if (mimeType.includes("google-apps")) {
      const response = await drive.files.export(
        {
          fileId,
          mimeType: "application/pdf",
        },
        { responseType: "arraybuffer" }
      );
      return response.data;
    } else {
      // For regular files, get the content directly
      const response = await drive.files.get(
        {
          fileId,
          alt: "media",
        },
        { responseType: "arraybuffer" }
      );
      return response.data;
    }
  } catch (error) {
    console.error(`Error getting content for file ${fileId}:`, error);
    return null;
  }
}

async function uploadToBlobStorage(
  fileBuffer: ArrayBuffer,
  fileName: string,
  tenantSlug: string,
  workspaceSlug: string
): Promise<string> {
  try {
    // Create blob service client
    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path with tenant and workspace structure
    const blobName = `${tenantSlug}/${workspaceSlug}/${Date.now()}-${fileName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Upload file
    await blockBlobClient.uploadData(fileBuffer);

    // Return the blob URL
    return blockBlobClient.url;
  } catch (error) {
    console.error("Error uploading to blob storage:", error);
    throw new Error("Failed to upload file to Azure Blob Storage");
  }
}

async function syncFolder(drive: any, options: SyncOptions, token) {
  const {
    folderId,
    tenantId,
    slug,
    recursive,
    workspaceId,
    pageId,
    parentFolderId,
    userId,
  } = options;
  let syncedFiles = 0;

  try {
    // Get folder info from Google Drive
    const folderInfo = await drive.files.get({
      fileId: folderId,
      fields: "id,name,mimeType",
    });

    // Get folder contents from Google Drive
    const driveContents = await listFolderContents(drive, folderId);

    let localPage = {};
    // Get or create the local folder
    let localFolder = await db.folder.findFirst({
      where: { gDriveFolderId: folderId, workspaceId },
      include: { files: true },
    });
    if (!localFolder) {
      localPage = await db.page.findFirst({
        where: { gDriveFolderId: folderId, workspaceId },
        include: { files: true },
      });
    }
    // Create local folder if it doesn't exist
    if (!localFolder && !localPage) {
      // First, check if parent folder exists in our database
      let parentFolder: any = null;
      if (parentFolderId) {
        parentFolder = await db.folder.findFirst({
          where: { gDriveFolderId: parentFolderId, workspaceId },
        });
      }

      // Create the folder without setting parentId directly
      localFolder = await db.folder.create({
        data: {
          name: folderInfo.data.name,
          workspaceId,
          pageId,
          gDriveFolderId: folderId,
          // Don't set parentId here as it's causing the MongoDB ObjectID error
        },
      });

      // If parent folder exists, create the relationship using folderHierarchy
      if (parentFolder) {
        try {
          // Create folder hierarchy relationship
          await db.folderHierarchy.create({
            data: {
              parentId: parentFolder.id,
              childId: localFolder?.id,
              pageId,
            },
          });
        } catch (hierarchyError) {
          console.error(
            `Error creating folder hierarchy relationship:`,
            hierarchyError
          );
          // Continue even if hierarchy creation fails
        }
      }
    }

    // Process each item in the folder
    for (const item of driveContents) {
      if (item.mimeType === "application/vnd.google-apps.folder" && recursive) {
        // Recursively sync subfolders with parent reference
        const subFolderResult = await syncFolder(
          drive,
          {
            ...options,
            folderId: item.id,
            parentFolderId: folderId, // Pass current folder as parent
          },
          token
        );

        syncedFiles += subFolderResult.syncedFiles;
      } else {
        // Handle files
        // Check if file exists locally by Google Drive ID
        const localFile = await db.file.findFirst({
          where: { gDriveFileId: item.id, workspaceId },
        });

        if (!localFile) {
          // Determine file extension from MIME type or filename
          let extension = item.name.split(".").pop() || "";
          if (!extension && item.mimeType) {
            const mimeMap = {
              "application/pdf": "pdf",
              "text/plain": "txt",
              "text/csv": "csv",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                "docx",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                "xlsx",
              "application/vnd.openxmlformats-officedocument.presentationml.presentation":
                "pptx",
              "application/vnd.google-apps.document": "gdoc",
              "application/vnd.google-apps.spreadsheet": "gsheet",
              "application/vnd.google-apps.presentation": "gslides",
            };
            extension = mimeMap[item.mimeType] || "";
          }

          // Get tenant info for blob storage path
          const tenant = await db.tenant.findUnique({
            where: { id: tenantId },
          });

          let fileUrl =
            item.webContentLink ||
            `https://drive.google.com/file/d/${item.id}/view`;

          // For supported file types, try to download and upload to blob storage
          const supportedTypes = SupportedExtensions;
          if (
            supportedTypes.includes(extension.toLowerCase()) ||
            item.mimeType.includes("google-apps")
          ) {
            try {
              // Download file content from Google Drive
              const fileContent = await getFileContent(
                drive,
                item.id,
                item.mimeType
              );

              if (fileContent) {
                // Upload to Azure Blob Storage
                fileUrl = await uploadToBlobStorage(
                  fileContent,
                  item.name,
                  tenant?.slug || "unknown",
                  options.workspaceSlug || "default"
                );
              }
            } catch (downloadError) {
              console.error(
                `Error downloading/uploading file ${item.id}:`,
                downloadError
              );
              // Continue with Google Drive URL if download/upload fails
            }
          }

          // Create new file record
          const newFile = await db.file.create({
            data: {
              name: item.name,
              size: `${item.size || "0"}`,
              extension,
              gDriveFileId: item.id,
              workspaceId,
              pageId: localFolder?.pageId ?? pageId,
              ...(localFolder?.id ? { folderId: localFolder?.id } : {}),
              ...(localPage ? { parentId: pageId } : {}),

              // Only set folderId, not parentId which could cause ObjectID issues
              type: "file",
              url: fileUrl,
            },
          });

          // Only index supported files
          if (
            fileUrl.includes("blob.core.windows.net") ||
            supportedTypes.includes(extension.toLowerCase())
          ) {
            try {
              // Determine document type for better processing
              const documentType = getDocumentType(newFile.name);

              // For indexable files, try to get content
              await workspaceChatService.uploadForIndexing(
                {
                  document_path: newFile.url,
                  document_type: documentType,
                  userId,
                  workspaceSlug: slug,
                  tenantId,
                  file_id: newFile?.id,
                },
                {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                }
              );
            } catch (indexError) {
              console.error(`Error indexing file ${newFile.id}:`, indexError);
              // Continue even if indexing fails
            }
          }

          syncedFiles++;
        } else if (
          new Date(item.modifiedTime) > (localFile.updatedAt || new Date(0))
        ) {
          // Update existing file if drive version is newer
          let fileUrl =
            item.webContentLink ||
            localFile.url ||
            `https://drive.google.com/file/d/${item.id}/view`;

          // For supported file types, try to download and upload to blob storage if not already in blob storage
          if (!localFile.url?.includes("blob.core.windows.net")) {
            const extension =
              localFile.extension || item.name.split(".").pop() || "";
            const supportedTypes = SupportedExtensions;

            if (
              supportedTypes.includes(extension.toLowerCase()) ||
              item.mimeType?.includes("google-apps")
            ) {
              try {
                // Get tenant info for blob storage path
                const tenant = await db.tenant.findUnique({
                  where: { id: tenantId },
                });

                // Download file content from Google Drive
                const fileContent = await getFileContent(
                  drive,
                  item.id,
                  item.mimeType
                );

                if (fileContent) {
                  // Upload to Azure Blob Storage
                  fileUrl = await uploadToBlobStorage(
                    fileContent,
                    item.name,
                    tenant?.slug || "unknown",
                    options.workspaceSlug || "default"
                  );
                }
              } catch (downloadError) {
                console.error(
                  `Error updating file ${item.id} to blob storage:`,
                  downloadError
                );
                // Continue with existing URL if download/upload fails
              }
            }
          }

          await db.file.update({
            where: { id: localFile.id },
            data: {
              size: `${item.size || "0"}`,
              url: fileUrl,
            },
          });
          syncedFiles++;
        }
      }
    }

    return {
      synced: true,
      syncedFiles,
      folderId,
      localFolderId: localFolder?.id,
    };
  } catch (error) {
    console.error(`Error syncing folder ${folderId}:`, error);
    return {
      synced: false,
      syncedFiles,
      error: error.message || "Unknown error syncing folder",
      folderId,
    };
  }
}

export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const options: SyncOptions = await req.json();
    const drive = await getGoogleDriveClient(options.tenantId, session.userId);
    const workspace = await db.workspace.findFirst({
      where: { slug: decodeURI(options?.workspaceSlug ?? "") },
    });

    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    if (!workspace) {
      return NextResponse.json(
        { synced: false, error: "Workspace not found" },
        { status: 404 }
      );
    }

    // If this is a root folder sync, update the workspace's Google Drive folder ID
    if (!options.parentFolderId && options.folderId) {
      await db.workspace.update({
        where: { id: workspace.id },
        data: { gDriveFolderId: options.folderId },
      });
    }

    const result = await syncFolder(
      drive,
      {
        ...options,
        userId: session.userId,
        workspaceId: workspace?.id,
      },
      token
    );

    // If sync was successful, return success response
    if (result.synced) {
      return NextResponse.json({
        synced: true,
        syncedFiles: result?.syncedFiles,
        folderId: result?.folderId,
        localFolderId: result?.localFolderId,
      });
    } else {
      // If sync failed for this folder but we want to continue with others
      return NextResponse.json(
        {
          synced: false,
          error: result.error || "Failed to sync folder",
          folderId: result?.folderId,
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Sync error:", error);
    return NextResponse.json(
      {
        synced: false,
        error: error.message || "Failed to sync with Google Drive",
      },
      { status: 500 }
    );
  }
}
