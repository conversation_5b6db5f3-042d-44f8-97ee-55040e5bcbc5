"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Alert<PERSON>riangle,
  RefreshCw,
  Server,
  Wifi,
  Clock,
  Shield,
  HelpCircle,
} from "lucide-react";

interface MCPErrorGuideProps {
  errorType: string;
  errorMessage: string;
  toolName?: string;
  serverName?: string;
  onRetry?: () => void;
  className?: string;
}

export function MCPErrorGuide({
  errorType,
  errorMessage,
  toolName,
  serverName,
  onRetry,
  className,
}: MCPErrorGuideProps) {
  const getErrorInfo = () => {
    switch (errorType) {
      case "ClosedResourceError":
        return {
          icon: <Wifi className="h-5 w-5 text-red-500" />,
          title: "Connection Lost",
          description: "The MCP server connection was closed unexpectedly.",
          solutions: [
            "The server process may have crashed or been terminated",
            "Network connection issues may have occurred",
            "Server timeout may have been reached",
          ],
          actions: [
            "Reconnect to the MCP server",
            "Check server logs for errors",
            "Verify server configuration",
            "Restart the server if necessary",
          ],
        };
      case "ConnectionError":
        return {
          icon: <Server className="h-5 w-5 text-red-500" />,
          title: "Connection Failed",
          description: "Unable to establish connection to the MCP server.",
          solutions: [
            "Server may not be running",
            "Incorrect server configuration",
            "Network connectivity issues",
          ],
          actions: [
            "Verify server is running",
            "Check server command and arguments",
            "Test network connectivity",
            "Review server configuration",
          ],
        };
      case "TimeoutError":
        return {
          icon: <Clock className="h-5 w-5 text-orange-500" />,
          title: "Request Timeout",
          description: "The tool execution took too long to complete.",
          solutions: [
            "Server is overloaded or unresponsive",
            "Tool execution is taking longer than expected",
            "Network latency issues",
          ],
          actions: [
            "Try again with a simpler request",
            "Check server performance",
            "Increase timeout settings",
            "Contact server administrator",
          ],
        };
      case "JSONDecodeError":
        return {
          icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
          title: "Invalid Response",
          description: "The server returned malformed data.",
          solutions: [
            "Server implementation issue",
            "Corrupted response data",
            "Version compatibility problems",
          ],
          actions: [
            "Check server implementation",
            "Verify MCP protocol version",
            "Review server logs",
            "Update server software",
          ],
        };
      case "PermissionError":
        return {
          icon: <Shield className="h-5 w-5 text-red-500" />,
          title: "Permission Denied",
          description: "Insufficient permissions to execute the tool.",
          solutions: [
            "Missing authentication credentials",
            "Insufficient user permissions",
            "Tool access restrictions",
          ],
          actions: [
            "Check authentication settings",
            "Verify user permissions",
            "Contact administrator",
            "Review tool access policies",
          ],
        };
      case "ServerUnhealthy":
        return {
          icon: <Server className="h-5 w-5 text-orange-500" />,
          title: "Server Not Responding",
          description:
            "The MCP server failed health check before tool execution.",
          solutions: [
            "Server process may have stopped",
            "Server became unresponsive",
            "Connection was lost silently",
          ],
          actions: [
            "Reconnect to the MCP server",
            "Check server process status",
            "Restart the server",
            "Verify server configuration",
          ],
        };
      case "ReconnectionFailed":
        return {
          icon: <RefreshCw className="h-5 w-5 text-red-500" />,
          title: "Reconnection Failed",
          description:
            "Automatic reconnection to the MCP server was unsuccessful.",
          solutions: [
            "Server configuration changed",
            "Server is permanently down",
            "Network connectivity issues",
          ],
          actions: [
            "Manually reconnect to server",
            "Check server configuration",
            "Verify server is running",
            "Contact server administrator",
          ],
        };
      default:
        return {
          icon: <HelpCircle className="h-5 w-5 text-gray-500" />,
          title: "Unknown Error",
          description: "An unexpected error occurred.",
          solutions: [
            "Unexpected server behavior",
            "Unknown error condition",
            "Possible bug or edge case",
          ],
          actions: [
            "Try the operation again",
            "Check server logs",
            "Report the issue",
            "Contact support",
          ],
        };
    }
  };

  const errorInfo = getErrorInfo();

  return (
    <Card className={`border-l-4 border-l-red-500 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          {errorInfo.icon}
          <div>
            <CardTitle className="text-base font-semibold text-red-700">
              {errorInfo.title}
            </CardTitle>
            <p className="text-sm text-red-600 mt-1">{errorInfo.description}</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Details */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="destructive" className="text-xs">
              {errorType}
            </Badge>
            {toolName && (
              <Badge variant="outline" className="text-xs">
                Tool: {toolName}
              </Badge>
            )}
            {serverName && (
              <Badge variant="outline" className="text-xs">
                Server: {serverName}
              </Badge>
            )}
          </div>
          <p className="text-sm text-red-700">{errorMessage}</p>
        </div>

        {/* Possible Causes */}
        <div>
          <h4 className="text-sm font-medium mb-2">Possible Causes:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            {errorInfo.solutions.map((solution, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-red-400 mt-1">•</span>
                <span>{solution}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Recommended Actions */}
        <div>
          <h4 className="text-sm font-medium mb-2">Recommended Actions:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            {errorInfo.actions.map((action, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-blue-400 mt-1">→</span>
                <span>{action}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Copy error details to clipboard
              const errorDetails = `Error Type: ${errorType}\nMessage: ${errorMessage}\nTool: ${
                toolName || "N/A"
              }\nServer: ${serverName || "N/A"}`;
              navigator.clipboard.writeText(errorDetails);
            }}
            className="text-xs"
          >
            Copy Error Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
