'use client'
import React, { useEffect, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

// Define Textarea component since it might not exist
const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-transparent focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = "Textarea";

interface MessageEditProps {
  content: string;
  isLoading: boolean;
  onSave: () => void;
  onCancel: () => void;
  onContentChange: (content: string) => void;
}

export const MessageEdit: React.FC<MessageEditProps> = ({
  content,
  isLoading,
  onSave,
  onCancel,
  onContentChange,
}) => {
  const { t } = useLanguage();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-focus and select all text when editing starts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.select();
    }
  }, []);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      onSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-3 w-full">
      <div className="relative w-full">
        <Textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onContentChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={t("chat.enterMessage")}
          className="min-h-[100px] resize-none bg-transparent pr-4 border-none outline-none focus:outline-none focus:ring-0 focus:border-transparent w-full"
          disabled={isLoading}
        />
      </div>
      
      <div className="flex items-center gap-2 justify-end pb-1 pr-1">
        <Button
          variant="outline"
          size="sm"
          onClick={onCancel}
          disabled={isLoading}
          className="h-8 rounded-full"
        >
          Cancel
        </Button>
        <Button
          size="sm"
          onClick={onSave}
          disabled={isLoading}
          className="h-8 rounded-full"
        >
          {isLoading && (
            <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
          )}
          Save
        </Button>
      </div>
    </div>
  );
};
