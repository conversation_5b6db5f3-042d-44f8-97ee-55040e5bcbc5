import os
import requests
from typing import List, Optional
from langchain_core.embeddings import Embeddings
import logging

logger = logging.getLogger(__name__)

class AzureVisionEmbeddings(Embeddings):
    """
    Custom Azure Vision embeddings implementation using the embed-v-4-0 model.
    This implementation follows the Azure Vision API endpoint format.
    """

    def __init__(
        self,
        azure_endpoint: Optional[str] = None,
        api_key: Optional[str] = None,
        deployment_name: Optional[str] = None,
        api_version: str = "2023-12-01-preview",
        model: str = "embed-v-4-0"
    ):
        """
        Initialize Azure Vision embeddings.

        Args:
            azure_endpoint: Azure endpoint URL
            api_key: Azure API key
            deployment_name: Azure deployment name
            api_version: API version
            model: Model name (default: embed-v-4-0)
        """
        self.azure_endpoint = azure_endpoint or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT")
        self.api_key = api_key or os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")
        self.deployment_name = deployment_name or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0")
        self.api_version = api_version
        self.model = model

        # Enable text normalization for case-insensitive embeddings
        self.normalize_text = True

        if not self.azure_endpoint:
            raise ValueError("Azure endpoint is required")
        if not self.api_key:
            raise ValueError("API key is required")
        if not self.deployment_name:
            raise ValueError("Deployment name is required")

    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for case-insensitive embeddings.

        This method applies consistent text normalization to ensure that
        semantically similar text with different cases produces similar embeddings.
        """
        if not text or not isinstance(text, str):
            return text

        # Convert to lowercase for case-insensitive matching
        normalized = text.lower()

        # Remove extra whitespace while preserving structure
        normalized = ' '.join(normalized.split())

        return normalized

    def _get_endpoint_url(self) -> str:
        """Construct the Azure Vision API endpoint URL."""
        return f"{self.azure_endpoint}/openai/deployments/{self.deployment_name}/embeddings?api-version={self.api_version}"

    def _get_headers(self) -> dict:
        """Get request headers."""
        return {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }

    def _embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of texts or image URLs using Azure Vision API.
        Handles batch processing with Azure OpenAI's limit of 96 texts per request.

        Args:
            texts: List of texts or image URLs to embed

        Returns:
            List of embeddings
        """
        # Validate input to prevent tokenization issues
        if not isinstance(texts, list):
            logger.error(f"Expected list of texts, got {type(texts)}: {texts}")
            raise ValueError(f"Expected list of texts, got {type(texts)}")

        # Create a copy to avoid modifying the original list
        validated_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                logger.error(f"Text at index {i} is not a string: {type(text)} - {text}")
                if isinstance(text, list):
                    logger.error("Text appears to be tokenized - this will cause embedding API errors")
                    # Check if this looks like token IDs (list of integers)
                    if all(isinstance(x, int) for x in text):
                        logger.error(f"Detected token IDs at index {i}: {text}")
                        raise ValueError(f"Text at index {i} appears to be tokenized (list of token IDs) instead of a string. This indicates a bug in the query processing pipeline.")
                    raise ValueError(f"Text at index {i} appears to be tokenized (list of tokens) instead of a string")
                text = str(text)

            # Apply text normalization if enabled
            if self.normalize_text:
                text = self._normalize_text(text)

            validated_texts.append(text)

        logger.info(f"Embedding {len(validated_texts)} documents using Azure Vision embed-v-4-0")

        # Azure OpenAI has a limit of 96 texts per request
        batch_size = 96
        all_embeddings = []

        # Process texts in batches
        for i in range(0, len(validated_texts), batch_size):
            batch = validated_texts[i:i + batch_size]
            logger.debug(f"Processing batch {i//batch_size + 1}/{(len(validated_texts) + batch_size - 1)//batch_size} with {len(batch)} texts")

            batch_embeddings = self._embed_batch(batch)
            all_embeddings.extend(batch_embeddings)

        if len(all_embeddings) != len(texts):
            raise ValueError(f"Expected {len(texts)} embeddings, got {len(all_embeddings)}")

        logger.debug(f"Successfully embedded {len(all_embeddings)} texts")
        return all_embeddings

    def _embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a single batch of texts (max 96 texts).

        Args:
            texts: List of texts to embed (max 96)

        Returns:
            List of embeddings for the batch
        """
        if len(texts) > 96:
            raise ValueError(f"Batch size too large: {len(texts)} > 96")

        endpoint = self._get_endpoint_url()
        headers = self._get_headers()

        # Prepare the payload following the reference format
        data = {
            "model": self.model,
            "input": texts
        }

        try:
            logger.debug(f"Sending batch request to Azure Vision API: {endpoint}")
            logger.debug(f"Batch size: {len(texts)}")

            response = requests.post(endpoint, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            logger.debug(f"Azure Vision API response received for batch")

            # Extract embeddings from response
            embeddings = []
            if "data" in result:
                for item in result["data"]:
                    if "embedding" in item:
                        embeddings.append(item["embedding"])

            if len(embeddings) != len(texts):
                raise ValueError(f"Expected {len(texts)} embeddings, got {len(embeddings)}")

            logger.debug(f"Successfully extracted {len(embeddings)} embeddings from batch")
            return embeddings

        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Azure Vision embeddings API for batch: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response text: {e.response.text}")
                # Log the request data that caused the error
                logger.error(f"Request data that caused error: {data}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Azure Vision embeddings batch: {e}")
            logger.error(f"Request data: {data}")
            raise

    def embed_image(self, image_url: str) -> List[float]:
        """
        Embed a single image URL.

        Args:
            image_url: Image URL (data URL or HTTP URL) to embed

        Returns:
            Single embedding vector
        """
        logger.info("Embedding image using Azure Vision embed-v-4-0")
        embeddings = self._embed_texts([image_url])
        return embeddings[0]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents.

        Args:
            texts: List of document texts to embed

        Returns:
            List of embeddings
        """
        logger.info(f"Embedding {len(texts)} documents using Azure Vision embed-v-4-0")
        return self._embed_texts(texts)

    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query text.

        Args:
            text: Query text to embed

        Returns:
            Single embedding vector
        """
        # Validate input to prevent tokenization issues
        if not isinstance(text, str):
            logger.error(f"Expected string for query, got {type(text)}: {text}")
            if isinstance(text, list):
                logger.error("Query appears to be tokenized - this will cause embedding API errors")
                raise ValueError("Query appears to be tokenized (list of tokens) instead of a string")
            text = str(text)

        # Apply text normalization if enabled
        if self.normalize_text:
            original_text = text
            text = self._normalize_text(text)
            logger.debug(f"Query normalization: '{original_text[:50]}...' -> '{text[:50]}...'")

        logger.info(f"Embedding query using Azure Vision embed-v-4-0: '{text[:100]}...'")
        embeddings = self._embed_texts([text])
        return embeddings[0]

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Async version of embed_documents.
        Note: This implementation is synchronous but wrapped for async compatibility.
        """
        return self.embed_documents(texts)

    async def aembed_query(self, text: str) -> List[float]:
        """
        Async version of embed_query.
        Note: This implementation is synchronous but wrapped for async compatibility.
        """
        return self.embed_query(text)
