name: <PERSON><PERSON>po Deployment

on:
  push:
    branches:
      - dev
      - main

    paths:
      - 'apps/terraform/**'
      - 'apps/web/**'
      - 'apps/admin/**'
      - 'apps/api/**'

permissions:
  id-token: write
  contents: read

jobs:
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      run_nextjs_only: ${{ steps.set-env.outputs.run_nextjs_only }}
    steps:
      - name: Set Environment Based on Branch
        id: set-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            echo "environment=dev" >> $GITHUB_ENV
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "run_nextjs_only=false" >> $GITHUB_ENV
            echo "run_nextjs_only=false" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/qa" ]]; then
            echo "environment=qa" >> $GITHUB_ENV
            echo "environment=qa" >> $GITHUB_OUTPUT
            echo "run_nextjs_only=true" >> $GITHUB_ENV
            echo "run_nextjs_only=true" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=prod" >> $GITHUB_ENV
            echo "environment=prod" >> $GITHUB_OUTPUT
            echo "run_nextjs_only=false" >> $GITHUB_ENV
            echo "run_nextjs_only=false" >> $GITHUB_OUTPUT
          fi

  check_changes:
    runs-on: ubuntu-latest
    outputs:
      terraform: ${{ steps.filter.outputs.terraform }}
      nextjs: ${{ steps.filter.outputs.nextjs }}
      fastapi: ${{ steps.filter.outputs.fastapi }}
      admin: ${{ steps.filter.outputs.admin }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Check for Changes
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            terraform:
              - 'apps/terraform/**'
            nextjs:
              - 'apps/web/**'
            fastapi:
              - 'apps/api/**'
            admin:
              - 'apps/admin/**'

  terraform:
    needs: [determine-environment, check_changes]
    runs-on: ubuntu-latest
    environment: ${{ needs.determine-environment.outputs.environment }}
    if: needs.check_changes.outputs.terraform == 'true' && needs.determine-environment.outputs.run_nextjs_only == 'false'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.7

      - name: Deploy Terraform
        env:
          ARM_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
          ARM_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        run: |
          cd apps/terraform
          # Initialize Terraform with environment-specific state file
          # If environment is dev, use terraform.tfstate, otherwise use prod.tfstate
          if [[ "${{ needs.determine-environment.outputs.environment }}" == "dev" ]]; then
            terraform init -backend-config="key=terraform.tfstate"
          else
            terraform init -backend-config="key=prod.tfstate"
          fi

          terraform plan \
            -var "client_id=${{ secrets.AZURE_CLIENT_ID }}" \
            -var "client_secret=${{ secrets.AZURE_CLIENT_SECRET }}" \
            -var "tenant_id=${{ secrets.AZURE_TENANT_ID }}" \
            -var "subscription_id=${{ secrets.AZURE_SUBSCRIPTION_ID }}" \
            -var-file=${{ needs.determine-environment.outputs.environment }}.tfvars
          terraform apply \
            -var "client_id=${{ secrets.AZURE_CLIENT_ID }}" \
            -var "client_secret=${{ secrets.AZURE_CLIENT_SECRET }}" \
            -var "tenant_id=${{ secrets.AZURE_TENANT_ID }}" \
            -var "subscription_id=${{ secrets.AZURE_SUBSCRIPTION_ID }}" \
            -var-file=${{ needs.determine-environment.outputs.environment }}.tfvars -auto-approve

  deploy_nextjs:
    needs: [determine-environment, check_changes]
    runs-on: ubuntu-latest
    environment: ${{ needs.determine-environment.outputs.environment }}
    if: needs.determine-environment.outputs.run_nextjs_only == 'true' || needs.check_changes.outputs.nextjs == 'true'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: |
          pnpm install
          cd apps/web
          npm install

      - name: Deploy to Azure SWA
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          action: "upload"
          production_branch: dev
          app_location: "apps/web"
          api_location: ""
          output_location: ""
        env:
          STRIPE_SECRET_KEY: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.STRIPE_SECRET_KEY_DEV || vars.STRIPE_SECRET_KEY_PROD }}
          NEXT_PUBLIC_DOCS_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_DOCS_URL_DEV || vars.NEXT_PUBLIC_DOCS_URL_PROD }}
          STRIPE_PUBLISHABLE_KEY: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.STRIPE_PUBLISHABLE_KEY_DEV || vars.STRIPE_PUBLISHABLE_KEY_PROD }}
          STRIPE_WEBHOOK_SECRET: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.STRIPE_WEBHOOK_SECRET_DEV || vars.STRIPE_WEBHOOK_SECRET_PROD }}
          NEXT_ENCRYPTION_CLOAK_KEY: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_ENCRYPTION_CLOAK_KEY_DEV || vars.NEXT_ENCRYPTION_CLOAK_KEY_PROD }}
          NEXT_PUBLIC_API_BASE_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_API_BASE_URL_DEV || vars.NEXT_PUBLIC_API_BASE_URL_PROD }}
          NEXT_ENCRYPTION_CLOAK_KEYCHAIN: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN_DEV || vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN_PROD }}
          NEXT_PUBLIC_MICROSOFT_CLIENT_ID: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_MICROSOFT_CLIENT_ID_DEV || vars.NEXT_PUBLIC_MICROSOFT_CLIENT_ID_PROD }}
          NEXT_PUBLIC_GOOGLE_CLIENT_ID: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_GOOGLE_CLIENT_ID_DEV || vars.NEXT_PUBLIC_GOOGLE_CLIENT_ID_PROD }}
          NEXT_PUBLIC_API_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_API_URL_DEV || vars.NEXT_PUBLIC_API_URL_PROD }}
          NEXT_PUBLIC_WS_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_WS_URL_DEV || vars.NEXT_PUBLIC_WS_URL_PROD }}
          NEXT_PUBLIC_SEND_EMAIL_FROM: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_SEND_EMAIL_FROM_DEV || vars.NEXT_PUBLIC_SEND_EMAIL_FROM_PROD }}
          NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING_DEV || vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING_PROD }}
          NEXT_PUBLIC_DATADOG_ENABLED: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_DATADOG_ENABLED_DEV || vars.NEXT_PUBLIC_DATADOG_ENABLED_PROD }}
          NEXT_PUBLIC_DATADOG_APPLICATION_ID: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_DATADOG_APPLICATION_ID_DEV || vars.NEXT_PUBLIC_DATADOG_APPLICATION_ID_PROD }}
          NEXT_PUBLIC_DATADOG_CLIENT_TOKEN: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN_DEV || vars.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN_PROD }}
          NEXT_PUBLIC_DATADOG_SITE: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_DATADOG_SITE_DEV || vars.NEXT_PUBLIC_DATADOG_SITE_PROD }}
          NEXT_PUBLIC_APP_VERSION: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_APP_VERSION_DEV || vars.NEXT_PUBLIC_APP_VERSION_PROD }}
          DATADOG_API_KEY: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.DATADOG_API_KEY_DEV || vars.DATADOG_API_KEY_PROD }}
  
  deploy_admin:
    needs: [determine-environment, check_changes]
    runs-on: ubuntu-latest
    environment: ${{ needs.determine-environment.outputs.environment }}
    if: needs.check_changes.outputs.admin == 'true' && needs.determine-environment.outputs.run_nextjs_only == 'false'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: |
          pnpm install
          cd apps/admin
          npm install

      - name: Deploy to Azure SWA
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_ADMIN_APPS_API_TOKEN }}
          action: "upload"
          production_branch: dev
          app_location: "apps/admin"
          api_location: ""
          output_location: ""
        env:
          NEXT_ENCRYPTION_CLOAK_KEY: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_ENCRYPTION_CLOAK_KEY_DEV || vars.NEXT_ENCRYPTION_CLOAK_KEY_PROD }}
          NEXT_PUBLIC_API_BASE_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_ADMIN_API_BASE_URL_DEV || vars.NEXT_PUBLIC_ADMIN_API_BASE_URL_PROD }}
          NEXT_ENCRYPTION_CLOAK_KEYCHAIN: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN_DEV || vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN_PROD }}
          NEXT_PUBLIC_WEB_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_WEB_URL_DEV || vars.NEXT_PUBLIC_WEB_URL_PROD }}
          DATABASE_URL: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.DATABASE_URL_DEV || vars.DATABASE_URL_PROD }}
          NEXT_PUBLIC_SEND_EMAIL_FROM: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_SEND_EMAIL_FROM_DEV || vars.NEXT_PUBLIC_SEND_EMAIL_FROM_PROD }}
          NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING: ${{ needs.determine-environment.outputs.environment == 'dev' && vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING_DEV || vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING_PROD }}
          
  deploy_fastapi:
    needs: [determine-environment, check_changes]
    runs-on: ubuntu-latest
    environment: ${{ needs.determine-environment.outputs.environment }}
    if: needs.check_changes.outputs.fastapi == 'true' && needs.determine-environment.outputs.run_nextjs_only == 'false'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Package FastAPI App
        run: |
          cd apps/api
          zip -r fastapi-app.zip . -x "*.git*" "node_modules/*" "__pycache__/*"

      - name: Deploy FastAPI to Azure App Services
        uses: azure/webapps-deploy@v2
        with:
          app-name: "skh-fastapi-${{ needs.determine-environment.outputs.environment }}"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: "apps/api/fastapi-app.zip"
