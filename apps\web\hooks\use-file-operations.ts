import {
  updateFile,
  deleteFile,
  createFile,
  recordVectorStoreUsage,
} from "@/services";
import { useLanguage } from "@/lib/language-context";
import { uploadPipeline } from "@/services/src/upload-pipeline";
import { workspaceChatService } from "@/services/workspace-chat";
import { getCookie } from "@/utils/cookies";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { SupportedExtensions } from "@/lib/constant/supported-extensions";
import { getFileExtension } from "@/lib/utils/file-utils";
import { ParallelProcessor } from "@/lib/utils/parallel-processor";

interface FileOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
  usageSummary: any;
  onFileDeleted?: (fileId: string) => void; // Callback for when file is deleted
  onFileRenamed?: (fileId: string, newName: string) => void; // Callback for when file is renamed
  onFileUploaded?: (newFiles: any[]) => void; // Callback for when files are uploaded
  onUploadProgress?: (progress: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
    currentBatch: string[];
  }) => void; // Callback for upload progress
}

// Helper function to determine document type based on file extension
const getDocumentType = (filename: string): string => {
  const extension = getFileExtension(filename);

  switch (extension) {
    case "md":
    case "mdx":
    case "markdown":
      return "markitdown"; // Use markitdown for better markdown processing
    case "pdf":
      return "pdf";
    case "txt":
      return "text";
    case "csv":
      return "csv";
    case "docx":
    case "doc":
      return "word";
    case "xlsx":
    case "xls":
      return "excel";
    case "pptx":
    case "ppt":
      return "powerpoint";
    default:
      return "auto"; // Let the backend auto-detect
  }
};

export const useFileOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
  usageSummary,
  onFileDeleted,
  onFileRenamed,
  onFileUploaded,
  onUploadProgress,
}: FileOperationsProps) => {
  const userId = getCookie("userId") || "";
  const router = useRouter();
  const { t } = useLanguage();

  // Helper function to truncate filename to ensure Azure Video Indexer compatibility
  const truncateFilename = (
    filename: string,
    maxLength: number = 50
  ): string => {
    if (filename.length <= maxLength) return filename;

    const extension = filename.substring(filename.lastIndexOf("."));
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf("."));
    const maxNameLength = maxLength - extension.length;

    if (maxNameLength <= 0) return filename.substring(0, maxLength);

    return nameWithoutExt.substring(0, maxNameLength) + extension;
  };

  // Helper function to process a single file upload
  const processSingleFile = async (
    file: File,
    folderId: string
  ): Promise<any> => {
    // Truncate filename to ensure final blob name stays under 80 characters
    // Account for timestamp prefix (13 chars) + hyphen (1 char) = 14 chars
    // Leave some buffer for URL encoding, so limit filename to 50 chars
    const truncatedFilename = truncateFilename(file.name, 50);

    // Create a new File object with truncated name if needed
    const fileToUpload =
      file.name !== truncatedFilename
        ? new File([file], truncatedFilename, { type: file.type })
        : file;

    const formData = new FormData();
    formData.append("file", fileToUpload);
    formData.append("tenantId", tenantId);
    formData.append("workspaceSlug", workspaceSlug);
    formData.append("pageId", pageId);

    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
        "x-workspace-slug": workspaceSlug,
      },
    });

    let gDriveFileId = "";
    let oneDriveFileId = "";

    // Use the upload pipeline to handle multiple uploads with retry and revert
    try {
      const uploadResults = await uploadPipeline.uploadFile(file, {
        workspaceSlug,
        tenantId,
      });

      // Extract file IDs from upload results
      uploadResults.forEach((result) => {
        if (result.success) {
          if (result.targetName === "googleDrive") {
            gDriveFileId = result.fileId || "";
          } else if (result.targetName === "outlookDrive") {
            oneDriveFileId = result.fileId || "";
          }
        }
      });
    } catch (error) {
      // Continue with the main upload as fallback
    }

    const data = await response.json();
    if (!response.ok) throw new Error(data.error);

    // Try to record vector store usage, but continue even if it fails
    try {
      const usageResult = await recordVectorStoreUsage(
        tenantId,
        file.size / (1024 * 1024 * 1024)
      );

      if (usageResult === null) {
        // Vector store usage recording failed, but continuing with upload
      }
    } catch (usageError) {
      // Failed to record vector store usage, but continuing with upload
    }

    return {
      name: fileToUpload.name.substring(0, fileToUpload.name.lastIndexOf(".")),
      type: "file" as const,
      ...(folderId && folderId.trim() !== "" ? { folderId } : {}),
      workspaceSlug: workspaceSlug,
      pageId: pageId,
      parentId: folderId && folderId.trim() !== "" ? folderId : pageId,
      extension: fileToUpload.name.split(".").pop() || "",
      size: `${Math.round(file.size / 1024)} KB`,
      oneDriveFileId,
      gDriveFileId,
      createdAt: new Date().toISOString(),
      url: data.url,
    };
  };

  const handleFileUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    folderId: string,
    files: any[],
    setFiles: (files: any[]) => void,
    setIsUploadOpen: (isOpen: boolean) => void
  ) => {
    setIsLoading(true);
    const uploadedFiles = e.target.files;
    if (uploadedFiles && uploadedFiles.length > 0) {
      try {
        // Check vector store usage before uploading
        try {
          // Calculate total size of files to be uploaded in GB
          const totalUploadSizeGB = Array.from(uploadedFiles).reduce(
            (total, file) => total + file.size / (1024 * 1024 * 1024),
            0
          );

          // Check if upload would exceed the limit
          const currentUsageGB = usageSummary.totalUsage || 0;
          const limitGB = usageSummary.limit || 0;

          if (limitGB > 0 && currentUsageGB + totalUploadSizeGB > limitGB) {
            // Inform user about exceeding the limit
            const willExceedBy = (
              currentUsageGB +
              totalUploadSizeGB -
              limitGB
            ).toFixed(2);

            // Ask user for confirmation before proceeding
            const confirmMessage =
              t("subscription.vectorStorageWarning", { willExceedBy }) +
              "\n\n" +
              t("subscription.vectorStorageUsage", {
                currentUsageGB: currentUsageGB.toFixed(2),
                limitGB: limitGB,
              }) +
              "\n\n" +
              t("subscription.vectorStorageContinue");

            const confirmUpload = window.confirm(confirmMessage);

            if (!confirmUpload) {
              setIsLoading(false);
              return;
            }
          }
        } catch (usageError) {
          // Continue with upload even if we can't check usage
        }

        toast.loading("Uploading files...");

        // Convert FileList to Array for parallel processing
        const fileArray = Array.from(uploadedFiles);

        // Create parallel processor for file uploads
        const processor = new ParallelProcessor<File, any>({
          concurrency: 3, // Process 3 files concurrently to balance speed and server load
          maxRetries: 2,
          retryDelay: 1000,
          batchDelay: 500,
          onProgress: (progress) => {
            // Update progress if callback is provided
            if (onUploadProgress) {
              onUploadProgress({
                total: progress.total,
                processed: progress.processed,
                successful: progress.successful,
                failed: progress.failed,
                currentBatch: progress.currentBatch.map((file) => file.name),
              });
            }
          },
          onItemError: (file, error, attempt) => {
            console.warn(
              `Failed to upload ${file.name} (attempt ${attempt}):`,
              error.message
            );
          },
        });

        // Process all files using parallel processor
        const uploadResults = await processor.processAll(fileArray, (file) =>
          processSingleFile(file, folderId)
        );

        // Extract successful uploads
        const newFiles = uploadResults
          .filter((result) => result.success)
          .map((result) => result.result);

        // Log failed uploads
        const failedUploads = uploadResults.filter((result) => !result.success);
        if (failedUploads.length > 0) {
          console.warn(
            `${failedUploads.length} files failed to upload:`,
            failedUploads.map((f) => f.item.name)
          );
        }
        const file = await createFile(
          {
            files: newFiles,
            workspaceSlug,
            tenantId,
          },
          tenantId,
          userId,
          workspaceSlug
        );

        file?.data?.map((item: any) => {
          if (SupportedExtensions?.includes(item?.extension)) {
            const documentType = getDocumentType(
              item.name + "." + item.extension
            );
            workspaceChatService?.uploadForIndexing({
              userId: userId,
              document_path: item.url,
              document_type: documentType,
              workspaceSlug: workspaceSlug,
              tenantId,
              file_id: item?.id,
            });
          }
        });
        toast.remove();
        setIsLoading(false);

        // Handle results based on success/failure counts
        const successCount = uploadResults.filter((r) => r.success).length;
        const failureCount = uploadResults.filter((r) => !r.success).length;

        if (file?.error) {
          toast.error("Failed to upload files. Please try again.");
          return;
        }

        // Update files list with successful uploads
        const createdFiles = file?.data ?? [];
        setFiles([...files, ...createdFiles]);
        setIsUploadOpen(false);
        toast.remove();

        // Call the callback if provided
        if (onFileUploaded && createdFiles.length > 0) {
          onFileUploaded(createdFiles);
        }

        // Show appropriate success/warning message
        if (failureCount === 0) {
          toast.success(`All ${successCount} files uploaded successfully!`);
        } else if (successCount > 0) {
          toast.success(`${successCount} files uploaded successfully!`);
          toast.error(`${failureCount} files failed to upload.`);
        } else {
          toast.error("All files failed to upload. Please try again.");
          return;
        }

        router.refresh();
      } catch (error) {
        toast.remove();
        toast.error("Failed to upload files. Please try again.");
      }
    }
  };

  const handleRenameFile = async (fileId: string, newName: string) => {
    if (!newName) {
      toast.error("File name cannot be empty.");
      return false;
    }

    const loadingToast = toast.loading("Renaming file...");
    setIsLoading(true);

    try {
      const updatedFile = await updateFile(
        {
          id: fileId,
          name: newName,
        },
        tenantId,
        userId,
        workspaceSlug
      );

      // Remove the specific loading toast
      toast.dismiss(loadingToast);
      setIsLoading(false);

      if (updatedFile?.message) {
        toast.success(updatedFile?.message ?? "File renamed successfully!");

        // Call the callback to update UI state instead of reloading
        if (onFileRenamed) {
          onFileRenamed(fileId, newName);
        }

        return true;
      } else {
        toast.error(
          updatedFile?.error ?? "Failed to rename file. Please try again."
        );
        return false;
      }
    } catch (error) {
      // Remove the specific loading toast
      toast.dismiss(loadingToast);
      setIsLoading(false);

      toast.error("Failed to rename file. Please try again.");
      return false;
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    const loadingToast = toast.loading("Deleting file...");
    setIsLoading(true);

    try {
      // Then delete from database and storage
      const deletedFile = await deleteFile(
        fileId,
        tenantId,
        userId,
        workspaceSlug
      );

      // Remove loading toast
      toast.dismiss(loadingToast);

      if (deletedFile?.message) {
        toast.success(deletedFile?.message ?? "File deleted successfully!");
        setIsLoading(false);

        // Call the callback to update UI state instead of reloading
        if (onFileDeleted) {
          onFileDeleted(fileId);
        }

        return true;
      } else {
        toast.error(
          deletedFile?.error ?? "Failed to delete file. Please try again."
        );
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      // Remove loading toast
      toast.dismiss(loadingToast);
      setIsLoading(false);

      toast.error("Failed to delete file. Please try again.");
      return false;
    }
  };

  return {
    handleFileUpload,
    handleRenameFile,
    handleDeleteFile,
  };
};
