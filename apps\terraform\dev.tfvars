resource_group_name           = "skh-dev"
location                      = "Switzerland North"
static_app_location           = "westeurope"
vnet_name                     = "SKH-vnet-dev"
backend_app_service_plan_name = "skh-app-services-dev"
backend_app_service_name      = "skh-fastapi-dev"
swa_name                      = "Skh-nextjs"
database_name                 = "skh-dev"
cosmosdb_password             = "8cqyf3hkiW3DA2Y"
cosmosdb_username             = "azadmin"
cosmosdb_name                 = "skh-dev-qa-cluster"
cosmosdb_location             = "southindia"
cosmosdb_tire                 = "Free"
cosmosdb_size                 = "32"
environment                   = "dev"
storage_account_name          = "devskhstorage"
data_location                 = "Switzerland"
app_service_sku_name          = "B2"
admin_swa_name                = "skh-admin-dev"
