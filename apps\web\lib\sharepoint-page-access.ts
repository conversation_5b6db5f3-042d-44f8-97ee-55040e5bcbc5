import db from "@/lib/shared-db";
import { getAuthenticatedClient } from "@/lib/microsoft-graph";

interface SharePointPageAccess {
  hasAccess: boolean;
  permissionLevel?: string;
  errorMessage?: string;
  requiresIntegration?: boolean;
  cached?: boolean;
  cacheExpiry?: Date;
}

/**
 * Check if user has access to a SharePoint-synced page
 * This function checks both integration status and SharePoint permissions
 */
export async function checkSharePointPageAccess(
  userId: string,
  tenantId: string,
  pageId: string,
  forceRefresh: boolean = false,
  t?: (key: string) => string
): Promise<SharePointPageAccess> {
  try {
    // Get the page to check if it's SharePoint-synced
    const page = await db.page.findUnique({
      where: { id: pageId },
      select: {
        sharePointSiteId: true,
        sharePointDriveId: true,
        sharePointFolderPath: true,
      },
    });

    // If page is not SharePoint-synced, allow access
    if (!page?.sharePointSiteId || !page?.sharePointDriveId) {
      return {
        hasAccess: true,
      };
    }

    // Check if user has any page membership (any source)
    const pageMember = await db.pageMember.findFirst({
      where: {
        userId,
        pageId,
      },
    });

    // If user is already a page member, allow access
    if (pageMember) {
      return {
        hasAccess: true,
        permissionLevel: pageMember.sharePointPermissionLevel || undefined,
        // cached: pageMember.source === "SHAREPOINT",
      };
    }

    // If no membership exists and this is a SharePoint page, check integration
    const integration = await db.integration.findFirst({
      where: {
        tenantId,
        platform: "OUTLOOK",
        userId,
      },
    });

    if (!integration) {
      return {
        hasAccess: false,
        requiresIntegration: true,
        errorMessage: t?.("sharepoint.integrationRequired") ||
          "SharePoint integration required to access this page",
      };
    }

    // If force refresh is not requested, deny access for non-members
    // This prevents automatic SharePoint API calls on every page load
    if (!forceRefresh) {
      return {
        hasAccess: false,
        errorMessage: t?.("sharepoint.accessDenied") ||
          "Access denied. Contact page administrator to be added as a member.",
      };
    }

    // Only perform SharePoint API check when explicitly requested (force refresh)
    let hasAccess = false;
    let permissionLevel: string | undefined;
    let errorMessage: string | undefined;

    try {
      const client = await getAuthenticatedClient(tenantId, userId);

      // Try to access the SharePoint folder/drive
      const endpoint = page.sharePointFolderPath
        ? `/sites/${page.sharePointSiteId}/drives/${page.sharePointDriveId}/root:/${page.sharePointFolderPath}`
        : `/sites/${page.sharePointSiteId}/drives/${page.sharePointDriveId}/root`;

      const response = await client.api(endpoint).get();

      if (response) {
        hasAccess = true;
        permissionLevel = "full control"; // Default permission level

        // Create PageMember record for future access
        await db.pageMember.create({
          data: {
            pageId,
            userId,
            tenantId,
            source: "SHAREPOINT",
            sharePointPermissionLevel: permissionLevel,
            lastSharePointCheck: new Date(),
            role: "ADMIN",
          },
        });
      }
    } catch (error: any) {
      hasAccess = false;
      if (error.code === "Forbidden" || error.code === "Unauthorized") {
        errorMessage = t?.("sharepoint.accessContentDenied") ||
          "You don't have permission to access this SharePoint content";
      } else if (error.code === "NotFound") {
        errorMessage = t?.("sharepoint.contentNotFound") ||
          "SharePoint content not found or has been moved";
      } else {
        errorMessage = t?.("sharepoint.unableToVerifyAccess") ||
          "Unable to verify SharePoint access";
      }
    }

    return {
      hasAccess,
      permissionLevel,
      errorMessage,
      cached: false,
    };
  } catch (error) {
    console.error("Error checking SharePoint page access:", error);

    return {
      hasAccess: false,
      errorMessage: t?.("sharepoint.errorCheckingAccess") ||
        "Error checking SharePoint access",
    };
  }
}

/**
 * Clear SharePoint access cache for a user and page
 */
export async function clearSharePointAccessCache(
  userId: string,
  pageId: string
): Promise<void> {
  try {
    await db.pageMember.deleteMany({
      where: {
        userId,
        pageId,
        source: "SHAREPOINT",
      },
    });
  } catch (error) {
    console.error("Error clearing SharePoint access cache:", error);
  }
}

/**
 * Clear all SharePoint access cache for a user
 */
export async function clearAllSharePointAccessCache(
  userId: string
): Promise<void> {
  try {
    await db.pageMember.deleteMany({
      where: {
        userId,
        source: "SHAREPOINT",
      },
    });
  } catch (error) {
    console.error("Error clearing all SharePoint access cache:", error);
  }
}
