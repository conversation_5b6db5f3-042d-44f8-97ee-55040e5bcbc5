"use client";

import React, { useState } from "react";
import { ImageAttachment } from "../types";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Download,
  ExternalLink,
  FileImage,
  Eye,
  FileText,
  FileSpreadsheet,
  Music,
  File,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { cn } from "@/lib/utils";
import {
  getFileTypeDescription,
  isImageFile,
  isAudioFile,
} from "@/lib/utils/file-utils";
import Image from "next/image";

interface ImagePreviewProps {
  images: ImageAttachment[];
  className?: string;
  maxDisplayImages?: number;
  setVideoContext?: (videoContext: string) => void;
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({
  images,
  className = "",
  maxDisplayImages = 4,
  setVideoContext,
}) => {
  const { t } = useLanguage();
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );
  const [pdfLoading, setPdfLoading] = useState<boolean>(true);

  if (!images || images.length === 0) {
    return null;
  }

  const displayImages = images.slice(0, maxDisplayImages);
  const remainingCount = images.length - maxDisplayImages;

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
    // Reset PDF loading state when opening dialog
    if (isPDF(images[index])) {
      setPdfLoading(true);
    }
  };

  // File type detection functions
  const isPDF = (file: ImageAttachment) => {
    return file.type === "application/pdf";
  };

  const isImage = (file: ImageAttachment) => {
    return isImageFile(file.type);
  };

  const isAudio = (file: ImageAttachment) => {
    return isAudioFile(file.type);
  };

  // Get appropriate icon and colors for file type
  const getFileIcon = (file: ImageAttachment) => {
    if (isImage(file)) {
      return {
        icon: FileImage,
        bgColor: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900",
        iconColor: "text-blue-600 dark:text-blue-400",
      };
    } else if (isPDF(file)) {
      return {
        icon: FileText,
        bgColor: "from-red-50 to-red-100 dark:from-red-950 dark:to-red-900",
        iconColor: "text-red-600 dark:text-red-400",
      };
    } else if (
      file.type.includes("spreadsheet") ||
      file.type.includes("excel") ||
      file.type.includes("csv")
    ) {
      return {
        icon: FileSpreadsheet,
        bgColor:
          "from-green-50 to-green-100 dark:from-green-950 dark:to-green-900",
        iconColor: "text-green-600 dark:text-green-400",
      };
    } else if (
      file.type.includes("wordprocessingml") ||
      file.type.includes("ms-word")
    ) {
      return {
        icon: FileText,
        bgColor: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900",
        iconColor: "text-blue-600 dark:text-blue-400",
      };
    } else if (
      file.type.includes("presentationml") ||
      file.type.includes("ms-powerpoint")
    ) {
      return {
        icon: FileText,
        bgColor:
          "from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900",
        iconColor: "text-orange-600 dark:text-orange-400",
      };
    } else if (isAudio(file)) {
      return {
        icon: Music,
        bgColor:
          "from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900",
        iconColor: "text-purple-600 dark:text-purple-400",
      };
    } else if (file.type.startsWith("text/")) {
      return {
        icon: FileText,
        bgColor: "from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900",
        iconColor: "text-gray-600 dark:text-gray-400",
      };
    }
    return {
      icon: File,
      bgColor: "from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900",
      iconColor: "text-gray-600 dark:text-gray-400",
    };
  };

  function downloadURI(uri: string, name: string) {

    let cleanedUrl = uri.split('?')[0];

    try {
      // Method 1: Direct download using anchor element with original URL (including SAS tokens)
      const link = document.createElement("a");
      link.href = cleanedUrl;
      link.download = name;
      link.target = "_blank";
      link.rel = "noopener noreferrer";

      // Temporarily add to DOM to trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log("Download initiated via direct link");
      return;
    } catch (linkError) {
      console.log("Direct link download failed, trying iframe approach:", linkError);

      try {
        // Method 2: Iframe approach for files that don't support direct download
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.position = 'absolute';
        iframe.style.left = '-9999px';
        iframe.src = cleanedUrl;

        document.body.appendChild(iframe);

        // Clean up iframe after download attempt
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe);
          }
        }, 2000);

        console.log("Download initiated via iframe");
      } catch (iframeError) {
        console.error("Iframe download failed:", iframeError);

        // Method 3: Open in new window as final fallback
        try {
          window.open(cleanedUrl, '_blank', 'noopener,noreferrer');
          console.log("Download initiated via new window");
        } catch (windowError) {
          console.error("All download methods failed:", windowError);
          // Show user-friendly error message
          alert(`Unable to download file: ${name}. Please try right-clicking the file and selecting "Save As".`);
        }
      }
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return t("chat.fileSizeBytes");
    const k = 1024;
    const sizes = [
      t("chat.fileSizeUnits.bytes"),
      t("chat.fileSizeUnits.kb"),
      t("chat.fileSizeUnits.mb"),
      t("chat.fileSizeUnits.gb"),
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={cn("space-y-3", className)}>
      {/* Enhanced File Grid */}
      <div className="grid grid-cols-2 gap-3 max-w-md">
        {displayImages.map((image, index) => (
          <div
            key={image.id}
            className={cn(
              "relative group cursor-pointer bg-gradient-to-br from-muted/50 to-muted",
              "rounded-xl overflow-hidden border border-border/50",
              "hover:border-primary/50 transition-all duration-200 hover:shadow-lg",
              "hover:scale-[1.02] active:scale-[0.98] w-[100px] h-[100px]"
            )}
          >
            <Dialog>
              <DialogTrigger asChild>
                <div
                  className="relative aspect-square"
                  onClick={() => handleImageClick(index)}
                >
                  {isImage(image) ? (
                    <Image
                      src={image.url || image.preview || ""}
                      alt={image.name}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                      loading="lazy"
                    />
                  ) : (
                    (() => {
                      const {
                        icon: IconComponent,
                        bgColor,
                        iconColor,
                      } = getFileIcon(image);
                      const fileTypeLabel = getFileTypeDescription(image.type);
                      return (
                        <div
                          className={`w-full h-full flex items-center justify-center bg-gradient-to-br ${bgColor}`}
                        >
                          <div className="text-center p-2">
                            <IconComponent
                              className={`h-8 w-8 ${iconColor} mx-auto mb-1`}
                            />
                            <span
                              className={`text-xs font-medium ${iconColor} block truncate max-w-[60px]`}
                            >
                              {fileTypeLabel}
                            </span>
                          </div>
                        </div>
                      );
                    })()
                  )}

                  {/* Enhanced Hover overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 transform scale-75 group-hover:scale-100 transition-transform duration-200">
                      <Eye className="h-5 w-5 text-gray-700" />
                    </div>
                  </div>

                  {/* Enhanced remaining count overlay */}
                  {index === maxDisplayImages - 1 && remainingCount > 0 && (
                    <div className="absolute inset-0 bg-gradient-to-br from-black/80 to-black/60 flex items-center justify-center backdrop-blur-sm">
                      <div className="text-center">
                        <div className="bg-white/20 rounded-full p-3 mx-auto mb-2">
                          <FileImage className="h-6 w-6 text-white" />
                        </div>
                        <span className="text-white font-bold text-xl">
                          +{remainingCount}
                        </span>
                        <p className="text-white/80 text-sm mt-1">
                          {t("chat.moreFiles")}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Image info badge */}
                  <div className="absolute top-2 left-2  backdrop-blur-sm  text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {formatFileSize(image.size)}
                  </div>
                </div>
              </DialogTrigger>

              <DialogContent className="max-w-5xl max-h-[95vh] p-0  border-none">
                <div className="relative flex flex-col h-full">
                  {/* Enhanced Full size preview for different file types */}
                  <div className="flex-1 flex items-center justify-center p-4">
                    {isImage(image) ? (
                      <div className="max-w-full max-h-full">
                        <Image
                          src={image.url || image.preview || ""}
                          alt={image.name}
                          width={800}
                          height={600}
                          className="max-w-full max-h-full object-contain rounded-lg"
                          style={{ maxHeight: "70vh" }}
                        />
                      </div>
                    ) : isPDF(image) ? (
                      <div className="w-full h-full min-h-[70vh] bg-gray-50 dark:bg-gray-900 rounded-lg overflow-hidden relative">
                        <iframe
                          src={image.url}
                          title={image.name}
                          className="w-full h-full border-0"
                          style={{ minHeight: "70vh" }}
                          onLoad={() => setPdfLoading(false)}
                          onError={() => {
                            console.error("Error loading PDF:", image.name);
                            setPdfLoading(false);
                          }}
                        />
                        {/* Loading indicator for PDF */}
                        {pdfLoading && (
                          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-2"></div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t("chat.loadingPdf")}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      // Document preview for non-PDF, non-image files
                      (() => {
                        const {
                          icon: IconComponent,
                          bgColor,
                          iconColor,
                        } = getFileIcon(image);
                        const fileTypeLabel = getFileTypeDescription(
                          image.type
                        );
                        return (
                          <div className="w-full max-w-2xl">
                            <div
                              className={`bg-gradient-to-br ${bgColor} rounded-xl p-8 text-center`}
                            >
                              <div className="space-y-4">
                                <IconComponent
                                  className={`h-24 w-24 ${iconColor} mx-auto`}
                                />
                                <div className="space-y-2">
                                  <h3
                                    className={`text-2xl font-semibold ${iconColor}`}
                                  >
                                    {fileTypeLabel}
                                  </h3>
                                  <p
                                    className={`text-lg ${iconColor} opacity-80 truncate`}
                                  >
                                    {image.name}
                                  </p>
                                  <div className="flex justify-center gap-4 text-sm opacity-70">
                                    <span>{formatFileSize(image.size)}</span>
                                    <span>•</span>
                                    <span>
                                      {image.type.split("/")[1]?.toUpperCase()}
                                    </span>
                                  </div>
                                </div>

                                {/* Document-specific information */}
                                <div
                                  className={`bg-white/20 rounded-lg p-4 text-sm ${iconColor}`}
                                >
                                  {isAudio(image) && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.audioFile")}
                                      </p>
                                      <p className="opacity-80">
                                        {t("chat.fileTypes.audioDescription")}
                                      </p>
                                    </div>
                                  )}
                                  {image.type.includes("wordprocessingml") && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.wordDocument")}
                                      </p>
                                      <p className="opacity-80">
                                        {t("chat.fileTypes.wordDescription")}
                                      </p>
                                    </div>
                                  )}
                                  {image.type.includes("spreadsheet") && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.spreadsheet")}
                                      </p>
                                      <p className="opacity-80">
                                        {t(
                                          "chat.fileTypes.spreadsheetDescription"
                                        )}
                                      </p>
                                    </div>
                                  )}
                                  {image.type.includes("presentationml") && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.presentation")}
                                      </p>
                                      <p className="opacity-80">
                                        {t(
                                          "chat.fileTypes.presentationDescription"
                                        )}
                                      </p>
                                    </div>
                                  )}
                                  {image.type.includes("csv") && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.csvData")}
                                      </p>
                                      <p className="opacity-80">
                                        {t("chat.fileTypes.csvDescription")}
                                      </p>
                                    </div>
                                  )}
                                  {image.type.startsWith("text/") && (
                                    <div className="space-y-2">
                                      <p className="font-medium">
                                        {t("chat.fileTypes.textFile")}
                                      </p>
                                      <p className="opacity-80">
                                        {t("chat.fileTypes.textDescription")}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })()
                    )}
                  </div>

                  {/* Enhanced Image info and actions */}
                  <div className=" p-6">
                    <div className="flex items-center justify-between ">
                      <div className="space-y-2 flex-1 mr-4">
                        <div className="flex items-center gap-2">
                          {(() => {
                            const { icon: IconComponent, iconColor } =
                              getFileIcon(image);
                            return (
                              <IconComponent
                                className={`h-5 w-5 ${iconColor}`}
                              />
                            );
                          })()}
                          <p className="font-semibold text-lg truncate">
                            {image.name}
                          </p>
                        </div>
                        <div className="flex items-center gap-4 text-sm">
                          <span className="px-2 py-1 bg-muted rounded-full">
                            {formatFileSize(image.size)}
                          </span>
                          <span className="px-2 py-1 bg-muted rounded-full">
                            {getFileTypeDescription(image.type)}
                          </span>
                          <span className="px-2 py-1 bg-muted rounded-full text-xs">
                            {image.type.split("/")[1]?.toUpperCase()}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => downloadURI(image.url, image.name)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t("chat.download")}
                        </Button>

                        {isImage(image) || isPDF(image) ? (
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => window.open(image.url, "_blank")}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            {t("chat.openInNewTab")}
                          </Button>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        ))}
      </div>

      {/* File list for screen readers */}
      <div className="sr-only">
        <p>{t("chat.filesAttached")}:</p>
        <ul>
          {images.map((image) => (
            <li key={image.id}>
              {image.name} ({formatFileSize(image.size)}) -{" "}
              {getFileTypeDescription(image.type)}
            </li>
          ))}
        </ul>
      </div>

      {/* Enhanced File navigation for multiple files */}
      {images.length > 1 && (
        <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/30 rounded-lg px-3 py-2">
          <div className="flex items-center gap-2">
            <File className="h-3 w-3" />
            <span className="font-medium">
              {images.length} {t("chat.filesCount")}
            </span>
          </div>
          {remainingCount > 0 && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
              {displayImages.length} {t("chat.shown")}
            </span>
          )}
        </div>
      )}
    </div>
  );
};
