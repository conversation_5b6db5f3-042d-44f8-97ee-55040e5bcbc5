"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from "react";
import { LLMScopeSettings } from "@/types/llm-scope";
import { getLLMScopeSettings } from "@/services/src/llm-scope";

interface LLMScopeContextType {
  llmScope: string[];
  llmScopeLoading: boolean;
  lastUpdated: string | null;
  refreshLLMScope: () => Promise<void>;
  setLLMScope: (scope: string[]) => void;
}

const LLMScopeContext = createContext<LLMScopeContextType | undefined>(undefined);

interface LLMScopeProviderProps {
  children: React.ReactNode;
  tenantId: string;
  initialSettings?: LLMScopeSettings | null;
}

export function LLMScopeProvider({ children, tenantId, initialSettings }: LLMScopeProviderProps) {
  const [llmScope, setLlmScopeState] = useState<string[]>(
    initialSettings?.llmScope || ["INTERNAL_ONLY"]
  );
  const [llmScopeLoading, setLlmScopeLoading] = useState(!initialSettings);
  const [lastUpdated, setLastUpdated] = useState<string | null>(
    initialSettings?.updatedAt || null
  );

  // Refs for cleanup and preventing duplicate requests
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  // Function to refresh LLM scope settings
  const refreshLLMScope = useCallback(async () => {
    if (isRefreshingRef.current || !tenantId) return;
    
    isRefreshingRef.current = true;
    
    try {
      setLlmScopeLoading(true);
      const settings = await getLLMScopeSettings(tenantId);
      
      if (settings) {
        // Only update if the settings have actually changed
        const hasChanged = 
          JSON.stringify(settings.llmScope) !== JSON.stringify(llmScope) ||
          settings.updatedAt !== lastUpdated;
          
        if (hasChanged) {
          setLlmScopeState(settings.llmScope);
          setLastUpdated(settings.updatedAt);
          
          // Broadcast change event for other components
          window.dispatchEvent(new CustomEvent('llm-scope-updated', {
            detail: { llmScope: settings.llmScope, updatedAt: settings.updatedAt }
          }));
        }
      }
    } catch (error) {
      console.error("Error refreshing LLM scope settings:", error);
      // Don't reset to default on error to avoid flickering
    } finally {
      setLlmScopeLoading(false);
      isRefreshingRef.current = false;
    }
  }, [tenantId, llmScope, lastUpdated]);

  // Function to manually set LLM scope (for immediate updates)
  const setLLMScope = useCallback((scope: string[]) => {
    setLlmScopeState(scope);
    setLastUpdated(new Date().toISOString());
  }, []);

  // Initial load effect
  useEffect(() => {
    if (!initialSettings && tenantId) {
      refreshLLMScope();
    }
  }, [tenantId, initialSettings, refreshLLMScope]);

  // Polling mechanism for real-time updates (fallback)
  useEffect(() => {
    if (!tenantId) return;

    // Poll every 30 seconds for changes (as fallback)
    pollingIntervalRef.current = setInterval(() => {
      refreshLLMScope();
    }, 30000);

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [tenantId, refreshLLMScope]);

  // Enhanced real-time synchronization with page visibility API
  useEffect(() => {
    if (!tenantId) return;

    // Listen for page visibility changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible - refresh immediately
        refreshLLMScope();
      }
    };

    // Listen for window focus
    const handleFocus = () => {
      refreshLLMScope();
    };

    // Listen for online/offline events
    const handleOnline = () => {
      refreshLLMScope();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('online', handleOnline);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('online', handleOnline);
    };
  }, [tenantId, refreshLLMScope]);

  // Listen for storage events (cross-tab synchronization)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `llm-scope-${tenantId}` && e.newValue) {
        try {
          const newSettings = JSON.parse(e.newValue);
          setLlmScopeState(newSettings.llmScope);
          setLastUpdated(newSettings.updatedAt);
        } catch (error) {
          console.error("Error parsing LLM scope from storage:", error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [tenantId]);

  // Listen for custom events (same-tab synchronization)
  useEffect(() => {
    const handleLLMScopeUpdate = (e: CustomEvent) => {
      const { llmScope: newScope, updatedAt } = e.detail;
      setLlmScopeState(newScope);
      setLastUpdated(updatedAt);
      
      // Store in localStorage for cross-tab sync
      localStorage.setItem(`llm-scope-${tenantId}`, JSON.stringify({
        llmScope: newScope,
        updatedAt
      }));
    };

    window.addEventListener('llm-scope-updated', handleLLMScopeUpdate as EventListener);
    return () => window.removeEventListener('llm-scope-updated', handleLLMScopeUpdate as EventListener);
  }, [tenantId]);



  const value: LLMScopeContextType = {
    llmScope,
    llmScopeLoading,
    lastUpdated,
    refreshLLMScope,
    setLLMScope,
  };

  return (
    <LLMScopeContext.Provider value={value}>
      {children}
    </LLMScopeContext.Provider>
  );
}

export function useLLMScope() {
  const context = useContext(LLMScopeContext);
  if (context === undefined) {
    throw new Error("useLLMScope must be used within a LLMScopeProvider");
  }
  return context;
}
