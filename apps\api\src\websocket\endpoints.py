"""
WebSocket endpoints for real-time notifications
"""
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from typing import Optional
import jwt
import os
from src.websocket.connection_manager import manager
from src.api.deps import get_db

logger = logging.getLogger(__name__)

router = APIRouter()

async def verify_websocket_token(token: str) -> dict:
    """Verify JWT token for WebSocket connection"""
    try:
        # Use the same secret as NextAuth
        secret = os.getenv("NEXTAUTH_SECRET")
        if not secret:
            raise HTTPException(status_code=500, detail="JWT secret not configured")
            
        # Decode the JWT token
        payload = jwt.decode(token, secret, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    tenant_id: Optional[str] = Query(None)
):
    """WebSocket endpoint for real-time notifications"""
    
    # Validate required parameters
    if not token or not user_id or not tenant_id:
        await websocket.close(code=4000, reason="Missing required parameters: token, user_id, tenant_id")
        return
        
    try:
        # Verify the JWT token
        payload = await verify_websocket_token(token)
        
        # Verify that the token user_id matches the provided user_id
        token_user_id = payload.get("userId") or payload.get("sub")
        if token_user_id != user_id:
            await websocket.close(code=4001, reason="Token user_id mismatch")
            return
            
        # Connect the user
        await manager.connect(websocket, user_id, tenant_id)
        
        try:
            # Keep the connection alive and handle incoming messages
            while True:
                # Wait for messages from the client
                data = await websocket.receive_text()
                
                # Handle ping/pong for connection health
                if data == "ping":
                    await manager.send_personal_message({
                        "type": "pong",
                        "timestamp": manager.connection_metadata[websocket]["connected_at"]
                    }, websocket)
                else:
                    # Log other messages for debugging
                    logger.info(f"Received message from user {user_id}: {data}")
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {user_id}")
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {e}")
        finally:
            # Clean up the connection
            manager.disconnect(websocket)
            
    except HTTPException as e:
        await websocket.close(code=4002, reason=f"Authentication failed: {e.detail}")
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close(code=4003, reason="Internal server error")

@router.get("/ws/stats")
async def get_websocket_stats():
    """Get WebSocket connection statistics (for debugging)"""
    return manager.get_connection_stats()

# Utility functions for broadcasting notifications
async def broadcast_mention_notification(notification_data: dict):
    """Broadcast a mention notification"""
    await manager.broadcast_notification(notification_data)

async def broadcast_comment_reply_notification(notification_data: dict):
    """Broadcast a comment reply notification"""
    await manager.broadcast_notification(notification_data)

async def broadcast_thread_shared_notification(notification_data: dict):
    """Broadcast a thread shared notification"""
    await manager.broadcast_notification(notification_data)

async def broadcast_thread_update(thread_data: dict):
    """Broadcast thread updates"""
    await manager.broadcast_thread_update(thread_data)
