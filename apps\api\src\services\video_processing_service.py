"""
Video Processing Service for handling asynchronous video analysis jobs.

This service manages the lifecycle of video processing jobs, including:
- Creating and tracking video processing jobs
- Background processing with Azure Video Indexer
- Webhook handling for job completion
- Job status updates and result delivery
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from bson import ObjectId
from src.services.azure_video_indexer import azure_video_indexer_service

logger = logging.getLogger(__name__)


class VideoProcessingService:
    """Service for managing asynchronous video processing jobs."""

    def __init__(self):
        self.db_client = None  # Will be set by endpoints
        self._processing_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_video_processing_job(
        self,
        video_files: List[Dict[str, Any]],
        user_id: str,
        tenant_id: str,
        chat_id: Optional[str] = None,
        file_id: Optional[str] = None,
        workspace_slug: Optional[str] = None
    ) -> str:
        """
        Create a new video processing job and start background processing.
        
        Args:
            video_files: List of video file information (url, name, type, size)
            user_id: ID of the user requesting the processing
            tenant_id: ID of the tenant
            chat_id: Optional chat ID for context
            
        Returns:
            Job ID for tracking the processing status
        """
        try:
            # Generate unique job ID
            job_id = str(uuid.uuid4())
            
            logger.info(f"Creating video processing job {job_id} for user {user_id}")
            
            # Create job record in database
            job_data = {
                "jobId": job_id,
                "status": "pending",
                "videoFiles": video_files,
                "userId": ObjectId(user_id),
                "tenantId": ObjectId(tenant_id),
                "chatId": ObjectId(chat_id) if chat_id else None,
                "fileId": file_id,  # Store file ID for status updates
                "workspaceSlug": workspace_slug,  # Store workspace slug for indexing
                "createdAt": datetime.now(timezone.utc),
                "updatedAt": datetime.now(timezone.utc)
            }

            await self.db_client.VideoProcessingJob.insert_one(job_data)
            
            # Start background processing
            task = asyncio.create_task(self._process_video_job(job_id))
            self._processing_tasks[job_id] = task
            
            logger.info(f"Video processing job {job_id} created and background processing started")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to create video processing job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of a video processing job.
        Checks Azure Video Indexer status and updates job when processing completes.

        Args:
            job_id: The job ID to check

        Returns:
            Job status information or None if job not found
        """
        try:
            job = await self.db_client.VideoProcessingJob.find_one(
                {"jobId": job_id}
            )

            if not job:
                return None

            current_status = job.get("status", "unknown")
            azure_video_ids = job.get("azureVideoIds", [])

            # If job is still processing, check Azure Video Indexer status
            if current_status == "processing" and azure_video_ids:
                await self._check_and_update_azure_status(job_id, azure_video_ids)

                # Refetch job after potential update
                job = await self.db_client.VideoProcessingJob.find_one(
                    {"jobId": job_id}
                )

            return {
                "jobId": job["jobId"],
                "status": job["status"],
                "videoContext": job.get("videoContext"),
                "errorMessage": job.get("errorMessage"),
                "createdAt": job["createdAt"].isoformat() if job.get("createdAt") else None,
                "updatedAt": job["updatedAt"].isoformat() if job.get("updatedAt") else None,
                "completedAt": job["completedAt"].isoformat() if job.get("completedAt") else None,
                "filesProcessed": len(job["videoFiles"]) if job.get("videoFiles") else 0
            }

        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return None

    async def _check_and_update_azure_status(self, job_id: str, azure_video_ids: list):
        """
        Check Azure Video Indexer status and update job when processing completes.

        Args:
            job_id: The job ID to update
            azure_video_ids: List of Azure Video IDs to check
        """
        try:
            from src.services.azure_video_indexer import azure_video_indexer_service

            all_completed = True
            video_analyses = []

            for video_id in azure_video_ids:
                # Check if video is processed in Azure Video Indexer
                video_index = await azure_video_indexer_service.get_video_index(video_id)

                if not video_index:
                    all_completed = False
                    continue

                state = video_index.get("state", "").lower()

                if state == "processed":
                    # Extract analysis data
                    transcript = azure_video_indexer_service.extract_transcript(video_index)
                    metadata = azure_video_indexer_service.extract_video_metadata(video_index)
                    visual_insights = azure_video_indexer_service.extract_visual_insights(video_index)

                    # Format analysis result
                    video_name = video_index.get("name", f"Video {video_id}")
                    analysis_text = f"Video: {video_name}\n\n"

                    if transcript:
                        analysis_text += f"Transcript:\n{transcript}\n\n"

                    if metadata:
                        analysis_text += f"Metadata:\n{metadata}\n\n"

                    if visual_insights:
                        analysis_text += f"Visual Analysis:\n{visual_insights}\n"

                    video_analyses.append(analysis_text)

                elif state == "failed":
                    logger.error(f"Video processing failed for {video_id}")
                    video_analyses.append(f"Video {video_id}: Processing failed")
                    # Continue processing other videos even if one fails
                else:
                    # Still processing
                    all_completed = False

            # If all videos are completed, update job status
            if all_completed and video_analyses:
                video_context = "\n\n".join(video_analyses)
                await self._complete_job(job_id, video_context, azure_video_ids)

                # Complete file indexing with video analysis results
                await self.complete_file_indexing(job_id, video_analyses)

                # Update file status to COMPLETED
                await self._update_file_status_to_completed(job_id)

                logger.info(f"Video processing job {job_id} completed successfully")
            elif all_completed and not video_analyses:
                # All videos failed
                await self._fail_job(job_id, "All video processing failed")

                # Update file status to FAILED
                await self._update_file_status_to_failed(job_id)

                logger.error(f"Video processing job {job_id} failed - all videos failed processing")

        except Exception as e:
            logger.error(f"Error checking Azure status for job {job_id}: {e}")
            await self._fail_job(job_id, f"Error checking processing status: {str(e)}")
    
    async def _process_video_job(self, job_id: str):
        """
        Background task to process video files for a job.
        
        Args:
            job_id: The job ID to process
        """
        try:
            logger.info(f"Starting background processing for job {job_id}")
            
            # Update job status to processing
            await self._update_job_status(job_id, "processing")
            
            # Get job details
            job = await self.db_client.VideoProcessingJob.find_one(
                {"jobId": job_id}
            )

            if not job:
                logger.error(f"Job {job_id} not found in database")
                return

            video_files = job["videoFiles"]
            azure_video_ids = []
            
            # Start processing each video file and collect Azure Video IDs
            for i, video_file in enumerate(video_files[:3]):  # Limit to first 3 videos
                try:
                    logger.info(f"Starting processing for video {i+1}/{len(video_files[:3])}: {video_file['name']}")

                    # Start video analysis in Azure Video Indexer (returns immediately with video_id)
                    analysis_result = await azure_video_indexer_service.analyze_video(
                        video_file['url'],
                        video_file['name']
                    )

                    if analysis_result.get("error"):
                        logger.error(f"Failed to start video analysis for {video_file['name']}: {analysis_result['error']}")
                        continue

                    # Store Azure Video ID for status polling
                    if analysis_result.get("video_id"):
                        azure_video_ids.append(analysis_result["video_id"])
                        logger.info(f"Video {i+1} started processing with Azure Video ID: {analysis_result['video_id']}")
                    else:
                        logger.error(f"No video_id returned for {video_file['name']}")

                except Exception as e:
                    logger.error(f"Error starting video analysis for {video_file['name']}: {e}")
                    continue

            # Store Azure Video IDs in job for status polling
            if azure_video_ids:
                await self.db_client.VideoProcessingJob.update_one(
                    {"jobId": job_id},
                    {"$set": {
                        "azureVideoIds": azure_video_ids,
                        "updatedAt": datetime.now(timezone.utc)
                    }}
                )
                logger.info(f"Successfully started processing {len(azure_video_ids)} videos for job {job_id}")
            else:
                # No videos started successfully
                await self._fail_job(job_id, "Failed to start processing for any videos")
                logger.error(f"Job {job_id} failed - no videos started processing")
                return

            # Job is now in processing state with Azure Video IDs stored
            # Start automatic background polling of Azure Video Indexer
            logger.info(f"Job {job_id} is now processing. Starting automatic Azure Video Indexer polling...")

            # Start background polling task
            await self._start_background_polling(job_id, azure_video_ids)
                
        except Exception as e:
            logger.error(f"Error in background video processing for job {job_id}: {e}")
            await self._fail_job(job_id, f"Processing failed: {str(e)}")
        finally:
            # Clean up task reference
            if job_id in self._processing_tasks:
                del self._processing_tasks[job_id]

    async def _start_background_polling(self, job_id: str, azure_video_ids: List[str]):
        """
        Start background polling of Azure Video Indexer status.
        This method will periodically check Azure Video Indexer until all videos are processed.
        """
        max_polls = 60  # Poll for up to 60 times (30 minutes with 30-second intervals)
        poll_interval = 30  # 30 seconds between polls
        poll_count = 0

        while poll_count < max_polls:
            poll_count += 1

            try:
                # Check current job status
                job = await self.db_client.VideoProcessingJob.find_one({"jobId": job_id})
                if not job:
                    logger.error(f"Background polling job {job_id} not found, stopping")
                    break

                current_status = job.get("status", "unknown")
                if current_status != "processing":
                    break

                # Check Azure Video Indexer status
                await self._check_and_update_azure_status(job_id, azure_video_ids)

                # Check if job completed after status update
                updated_job = await self.db_client.VideoProcessingJob.find_one({"jobId": job_id})
                if updated_job and updated_job.get("status") != "processing":
                    break

                # Wait before next poll
                await asyncio.sleep(poll_interval)

            except Exception as e:
                logger.error(f"Error during background polling for job {job_id}: {e}")
                # Continue polling despite errors
                await asyncio.sleep(poll_interval)

        if poll_count >= max_polls:
            logger.warning(f"Background polling timeout for job {job_id} - reached maximum polling time")

    async def _update_job_status(self, job_id: str, status: str):
        """Update job status in database."""
        try:
            await self.db_client.VideoProcessingJob.update_one(
                {"jobId": job_id},
                {"$set": {
                    "status": status,
                    "updatedAt": datetime.now(timezone.utc)
                }}
            )
            logger.info(f"Updated job {job_id} status to {status}")
        except Exception as e:
            logger.error(f"Failed to update job {job_id} status: {e}")
    
    async def _complete_job(self, job_id: str, video_context: str, azure_video_ids: List[str]):
        """Mark job as completed with results."""
        try:
            await self.db_client.VideoProcessingJob.update_one(
                {"jobId": job_id},
                {"$set": {
                    "status": "completed",
                    "videoContext": video_context,
                    "azureVideoIds": azure_video_ids,
                    "updatedAt": datetime.now(timezone.utc),
                    "completedAt": datetime.now(timezone.utc)
                }}
            )
            logger.info(f"Job {job_id} completed successfully")
        except Exception as e:
            logger.error(f"Failed to complete job {job_id}: {e}")
    
    async def _fail_job(self, job_id: str, error_message: str):
        """Mark job as failed with error message."""
        try:
            await self.db_client.VideoProcessingJob.update_one(
                {"jobId": job_id},
                {"$set": {
                    "status": "failed",
                    "errorMessage": error_message,
                    "updatedAt": datetime.now(timezone.utc),
                    "completedAt": datetime.now(timezone.utc)
                }}
            )
            logger.error(f"Job {job_id} failed: {error_message}")
        except Exception as e:
            logger.error(f"Failed to update job {job_id} failure status: {e}")
    
    async def handle_webhook_completion(self, video_id: str, webhook_data: Dict[str, Any]):
        """
        Handle webhook notification from Azure Video Indexer when processing completes.
        
        Args:
            video_id: Azure Video Indexer video ID
            webhook_data: Webhook payload data
        """
        try:
            logger.info(f"Handling webhook completion for Azure video ID: {video_id}")
            
            # Find job by Azure video ID
            jobs_cursor = self.db_client.VideoProcessingJob.find({
                "status": "processing",
                "azureVideoIds": video_id
            })
            jobs = await jobs_cursor.to_list(length=None)
            
            if not jobs:
                logger.warning(f"No processing job found for Azure video ID: {video_id}")
                return
            
            # Process webhook for each matching job
            for job in jobs:
                await self._process_webhook_for_job(job["jobId"], video_id, webhook_data)
                
        except Exception as e:
            logger.error(f"Error handling webhook completion for video {video_id}: {e}")
    
    async def _process_webhook_for_job(self, job_id: str, video_id: str, _webhook_data: Dict[str, Any]):
        """Process webhook data for a specific job."""
        try:
            # This method can be extended to handle specific webhook events
            # For now, we'll just log the webhook reception
            logger.info(f"Webhook received for job {job_id}, video {video_id}")

            # The actual processing is handled by the background task
            # Webhooks can be used for additional notifications or status updates

        except Exception as e:
            logger.error(f"Error processing webhook for job {job_id}: {e}")

    async def complete_file_indexing(self, job_id: str, video_analysis_results: List[str]):
        """
        Complete the file indexing process after video processing finishes.

        Args:
            job_id: The video processing job ID
            video_analysis_results: List of processed video content for indexing
        """
        try:
            # Get job details
            job = await self.db_client.VideoProcessingJob.find_one({"jobId": job_id})
            if not job:
                logger.error(f"Job {job_id} not found for file indexing completion")
                return

            file_id = job.get("fileId")
            workspace_slug = job.get("workspaceSlug")

            if not file_id:
                logger.warning(f"No file ID found for job {job_id}, skipping file status update")
                return

            # Index the processed video content
            if video_analysis_results and workspace_slug:
                logger.info(f"Indexing processed video content for file {file_id}")

                # Import workspace RAG manager
                from src.services.workspace_rag import WorkspaceRAGManager
                workspace_rag_manager=WorkspaceRAGManager()
                workspace_rag_manager.db_client = self.db_client

                # Get workspace details
                workspace = await self.db_client.Workspace.find_one({"slug": workspace_slug})
                if not workspace:
                    logger.error(f"Workspace {workspace_slug} not found for indexing")
                    return

                # Create documents from video analysis results
                from langchain.schema.document import Document
                documents = []

                for i, analysis_text in enumerate(video_analysis_results):
                    # Skip failed video analysis results
                    if not analysis_text.strip():
                        continue
                    if "processing failed" in analysis_text.lower():
                        logger.warning(f"Skipping failed video analysis result for job {job_id}, index {i}")
                        continue
                    document = Document(
                        page_content=analysis_text.lower(),
                        metadata={
                            "source": f"video_analysis_{job_id}_{i}",
                            "file_name": f"video_analysis_{i}",
                            "file_type": "video",
                            "transcription_service": "azure_video_indexer",
                            "workspace_id": str(workspace["_id"]),
                            "fileId": file_id,
                            "slug": workspace_slug,
                            "processing_status": "completed"
                        }
                    )
                    documents.append(document)

                if documents:
                    # Get workspace agent and index documents
                    agent = await workspace_rag_manager.get_workspace_agent(workspace["_id"], job.get("tenantId"))
                    if agent:
                        agent.index_documents(documents)
                        logger.info(f"Successfully indexed {len(documents)} video analysis documents")
                    else:
                        logger.error(f"Failed to get workspace agent for indexing")
                else:
                    logger.warning(f"No successful video analysis documents to index for job {job_id}")

            # Update file status to COMPLETED
            try:
                await self.db_client.File.update_one(
                    {"_id": ObjectId(file_id)},
                    {"$set": {
                        "vectorizationStatus": "COMPLETED",
                        "vectorizedAt": datetime.now(timezone.utc)
                    }}
                )
                logger.info(f"Updated file {file_id} status to COMPLETED")
            except Exception as e:
                logger.error(f"Failed to update file status for {file_id}: {e}")

        except Exception as e:
            logger.error(f"Error completing file indexing for job {job_id}: {e}")

            # Update file status to FAILED if something went wrong
            if job and job.get("fileId"):
                try:
                    await self.db_client.File.update_one(
                        {"_id": ObjectId(job["fileId"])},
                        {"$set": {"vectorizationStatus": "FAILED"}}
                    )
                except Exception as update_error:
                    logger.error(f"Failed to update file status to FAILED: {update_error}")

    async def _update_file_status_to_completed(self, job_id: str):
        """Update file status to COMPLETED when video processing finishes successfully."""
        try:
            job = await self.db_client.VideoProcessingJob.find_one({"jobId": job_id})
            if not job:
                logger.warning(f"Job {job_id} not found for file status update")
                return

            file_id = job.get("fileId")
            if not file_id:
                logger.warning(f"No file ID found for job {job_id}, skipping file status update")
                return

            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {
                    "vectorizationStatus": "COMPLETED",
                    "vectorizedAt": datetime.now(timezone.utc)
                }}
            )

            logger.info(f"Updated file {file_id} status to COMPLETED")

        except Exception as e:
            logger.error(f"Error updating file status to COMPLETED for job {job_id}: {e}")

    async def _update_file_status_to_failed(self, job_id: str):
        """Update file status to FAILED when video processing fails."""
        try:
            job = await self.db_client.VideoProcessingJob.find_one({"jobId": job_id})
            if not job:
                logger.warning(f"Job {job_id} not found for file status update")
                return

            file_id = job.get("fileId")
            if not file_id:
                logger.warning(f"No file ID found for job {job_id}, skipping file status update")
                return

            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )

            logger.info(f"Updated file {file_id} status to FAILED")

        except Exception as e:
            logger.error(f"Error updating file status to FAILED for job {job_id}: {e}")


# Global service instance
video_processing_service = VideoProcessingService()
