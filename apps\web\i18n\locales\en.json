{"common": {"create": "Create", "selectAll": "Select All", "deselectAll": "Deselect All", "assign": "Assign", "users": "Users", "add": "Add", "adding": "Adding", "assigning": "Assigning...", "welcome": "Welcome to Swiss Knowledge Hub", "signIn": "Sign In", "signInNow": "Sign In Now", "signOut": "Sign Out", "register": "Register", "forgotPassword": "Forgot Password", "profile": "Profile", "settings": "Settings", "dashboard": "Dashboard", "organizations": "Organizations", "createOrganization": "Create Organization", "organizationSettings": "Organization Settings", "members": "Members", "invitations": "Invitations", "workspaces": "Workspaces", "workspace": "Workspace", "noUsers": "User not Found", "contentImportedAsHtml": "The content will be imported as HTML.", "email": "Email", "password": "Password", "name": "Name", "description": "Description", "save": "Save", "cancel": "Cancel", "uploading": "Uploading...", "yes": "Yes", "no": "No", "loading": "Loading...", "submit": "Submit", "invite": "Invite", "remove": "Remove", "creating": "Creating...", "move": "Move", "moving": "Moving...", "rename": "<PERSON><PERSON>", "edit": "Edit", "search": "Search", "language": "Language", "english": "English", "german": "German", "firstName": "First Name", "lastName": "Last Name", "saving": "Saving...", "saveChanges": "Save Changes", "tryAgain": "Try again", "returnToSignIn": "Return to sign in", "emailSent": "Email sent", "checkEmail": "Check your email", "tryAnotherEmail": "Try another email", "notifications": "Notifications", "yourOrganizations": "Your Organizations", "current": "Current", "optional": "optional", "back": "Back", "you": "You", "actions": "Actions", "user": "User", "error": "Error", "success": "Success", "delete": "Delete", "update": "Update", "filter": "Filter", "filters": "Filters", "sort": "Sort", "next": "Next", "previous": "Previous", "select": "Select", "noPermission": "You don't have permission to perform this action", "accessDenied": "Access Denied", "noPermissionToView": "You don't have permission to view this content", "fetch": "<PERSON>tch", "fetching": "Fetching...", "preview": "Preview", "title": "Title", "content": "Content", "warning": "Warning", "urlRequired": "URL is required", "titleAndContentRequired": "Title and content are required", "all": "All", "selectUser": "Select User", "removing": "Removing...", "clear": "Clear", "searchPlaceholder": "Search...", "role": "Role", "pageInformation": "Page Information", "pageName": "Page Name", "createdAt": "Created At", "sharedThreads": "Shared Threads", "privateShared": "Private Shared", "publicShared": "Public Shared", "unread": "Unread", "comments": "Comments", "mentions": "Mentions", "reply": "Reply", "resolve": "Resolve", "reopen": "Reopen", "updatedAt": "Updated At", "dismiss": "<PERSON><PERSON><PERSON>", "confirm": "Confirm", "member": "Member", "deleting": "Deleting...", "view": "View", "noDescription": "No description", "syncing": "Syncing...", "indexingStatus": {"pending": "Pending", "processing": "Processing", "processingVideo": "Processing Video", "indexed": "Indexed", "failed": "Failed", "startIndexing": "Start Indexing", "retryIndexing": "Retry Indexing"}}, "role": {"member": "Member", "editor": "Editor", "admin": "Admin", "memberDesc": "View only", "editorDesc": "View and edit", "adminDesc": "Full access"}, "auth": {"loginTitle": "Sign in to your account", "registerTitle": "Create a new account", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign up", "resetPassword": "Reset Password", "resetPasswordDescription": "Enter your email to receive a password reset link", "resetEmailSent": "Check your email for reset instructions", "resetEmailMessage": "We've sent an email to {email} with instructions to reset your password. Please check your inbox.", "sendResetInstructions": "Send Reset Instructions", "accountInformation": "Account Information", "agreeToTerms": "You must agree to the terms and conditions.", "passwordsDoNotMatch": "Passwords do not match", "passwordMinLength": "Password must be at least 8 characters.", "nameMinLength": "Name must be at least 2 characters.", "validEmail": "Please enter a valid email address.", "organizationNameMinLength": "Organization name must be at least 2 characters.", "rememberMe": "Remember me", "signingIn": "Signing in...", "signInSuccessful": "Sign in successful!", "registrationFailed": "Registration failed. Please try again.", "resettingPassword": "Resetting password...", "passwordResetSuccess": "Password reset successfully!", "iAgreeToThe": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "continue": "Continue", "completeSignUp": "Complete Sign Up", "newPassword": "New Password", "createNewPassword": "Create a new password", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Confirm your new password", "setNewPasswordBeforeContinuing": "You need to set a new password before continuing.", "verifyEmailBeforeSignIn": "Please verify your email before signing in", "signInFailedCheckCredentials": "Sign in failed. Please check your credentials.", "signInFailedTryAgain": "Sign in failed. Please try again.", "registrationError": "Registration error: {error}"}, "organization": {"createOrganization": "Create Organization", "createDescription": "Add a new organization to collaborate with your team.", "descriptionOptional": "Description (Optional)", "urlOptional": "Website URL (Optional)", "creating": "Creating...", "createSuccess": "Organization created successfully", "createFailed": "Failed to create organization", "switching": "Switching organization...", "switchSuccess": "Organization switched successfully", "switchFailed": "Failed to switch organization", "notFound": "Organization not found", "searchPlaceholder": "Search organization...", "noOrganizationsFound": "No organization found.", "general": "General", "members": "Members", "invitations": "Invitations", "organizationInfo": "Organization Information", "updateDetails": "Update your organization details and profile.", "nameLabel": "Name", "namePlaceholder": "Acme Inc.", "nameDescription": "The name of your organization.", "slugLabel": "Slug", "slugPlaceholder": "acme", "slugDescription": "The URL-friendly identifier for your organization.", "descriptionLabel": "Description", "descriptionPlaceholder": "Brief description of your organization", "descriptionHelp": "Describe what your organization does.", "urlLabel": "Website URL", "urlPlaceholder": "https://example.com", "urlDescription": "Your organization's website address.", "saving": "Saving...", "saveChanges": "Save Changes", "teamMembers": "Team Members", "manageTeamMembers": "Manage your team members and their roles.", "inviteUser": "Invite User", "pendingInvitations": "Pending Invitations", "managePendingInvitations": "Manage invitations sent to new team members.", "noInvitations": "No pending invitations", "whenInviteMembers": "When you invite members, they will appear here.", "creatingOrganization": "Creating organization...", "tellUsAboutOrganization": "Tell us about your organization", "nameMinLength": "Organization name must be at least 2 characters.", "slugMinLength": "Slug must be at least 2 characters.", "validUrl": "Please enter a valid URL", "settings": "Organization Settings", "settingsDescription": "Manage your organization settings and preferences.", "loading": "Loading Organization...", "selectFromSidebar": "If this page doesn't load, please select an organization from the dropdown in the sidebar.", "updateFailed": "Failed to update organization", "updating": "Updating company...", "updateSuccess": "Company updated successfully", "slugFormat": "Slug can only contain lowercase letters, numbers, and hyphens."}, "socialAccount": {"disconnectConfirm": "Are you sure you want to disconnect your {platform} account?", "disconnectWarning": "Disconnecting your account will revoke access to the {platform} authentication.", "connectAccount": "Connect Account", "disconnectAccount": "Disconnect Account", "connected": "Connected", "disconnected": "Disconnected"}, "roles": {"owner": "Owner", "admin": "Admin", "member": "Member", "makeAdmin": "Make Admin", "makeMember": "Make Member", "removeMember": "Remove Member", "confirmRemove": "Are you sure you want to remove {name} from this organization? They will lose access to all resources.", "role": "Role", "organizationRole": "Organization Role", "workspaceRole": "Workspace Role", "organizationRoleTooltip": "The user's role within the entire organization. This determines their global permissions across all workspaces.", "workspaceRoleTooltip": "The user's specific role within this workspace. This determines their permissions for this particular workspace only.", "joined": "Joined", "currentUser": "Current User", "removeTeamMember": "Remove team member", "roleUpdatedSuccess": "Role updated successfully to {role}", "updateRoleFailed": "Failed to update role", "memberRemovedSuccess": "Member removed successfully", "removeMemberFailed": "Failed to remove member", "title": "Roles", "rolesDescription": "Manage built-in and custom roles to control user permissions across your organization", "builtInRoles": "Built-in Roles", "builtInRolesDescription": "Standard roles with predefined permissions that cannot be modified", "builtInRolesTooltip": "These are system-defined roles with fixed permissions. You cannot edit or delete them.", "builtIn": "Built-in", "customRoles": "Custom Roles", "customRolesDescription": "Create and manage custom roles with specific permissions", "customRolesTooltip": "Create custom roles with specific permissions tailored to your organization's needs.", "createRole": "Create Role", "editRole": "Edit Role", "basicInfo": "Basic Information", "assignWorkspaces": "Assign Workspaces", "selectWorkspaces": "Select Workspaces", "roleCreatedSuccess": "Role created successfully", "roleCreateFailed": "Failed to create role", "roleUpdateFailed": "Failed to update role", "roleNotFound": "Role not found", "createRoleDescription": "Create a new custom role with specific permissions", "editRoleDescription": "Edit this custom role and its permissions", "deleteRoleTitle": "Delete Role", "deleteRoleDescription": "This action cannot be undone. This will permanently delete this role.", "noRoles": "No custom roles created yet", "noRolesDescription": "Create your first custom role to define specific permissions for your team members", "noDescription": "No description provided", "noRolesFound": "No custom roles found", "deleteRole": "Delete Role", "permission": "permission", "permissionsCount": "permissions", "permissions": "Permissions", "customRole": "Custom Role", "selectCustomRole": "Select Custom Role", "selectRole": "Select Role", "ownerOnly": "You do not have permission to manage roles. Please contact the workspace owner.", "name": "Name", "description": "Description", "custom": "Custom", "builtInRoleNames": {"owner": "Owner", "admin": "Admin", "member": "Member"}, "builtInRoleDescriptions": {"owner": "Full access to all organization features and settings", "admin": "Manage workspaces, users, and most organization settings", "member": "Basic access to assigned workspaces"}, "editRolePermissions": "Edit {role} permissions", "editRoleTitle": "Edit {role} Role", "roleConfiguration": "{role} Role Configuration", "selectPermissionsFor": "Select the permissions that users with the {role} role should have.", "permissionsTitle": "Permissions", "permissionsGroup": "{resource} Permissions", "ownerRoleCannotBeEdited": "Owner role cannot be edited", "roleUpdatedSuccessfully": "{role} role updated successfully", "failedToUpdateRole": "Failed to update role", "savingChanges": "Saving...", "saveChanges": "Save Changes", "tooltipMessages": {"ownersHaveAllPermissions": "Owners have all permissions by default", "loadingPermissionDetails": "Loading permission details...", "permissionDetailsUnavailable": "Permission details unavailable", "roleHasNoPermissions": "{role} role has no permissions assigned", "roleHasAllPermissions": "{role} role has all {count} permissions", "permissionsIncluding": "{count} permissions including {permissions}{more}"}, "permissionSummary": {"allPermissions": "All permissions", "loadingPermissions": "Loading permissions...", "failedToLoad": "Failed to load", "noPermissionsAssigned": "No permissions assigned"}, "resources": {"WORKSPACE": "Workspace", "PAGE": "Page", "FOLDER": "Folder", "FILE": "File"}, "actions": {"CREATE": "Create", "READ": "Read", "UPDATE": "Update", "DELETE": "Delete"}, "hierarchy": {"info": "Hierarchy Info", "title": "Permission Hierarchy: Workspace > Page > Folder > Files", "inheritance": "Inheritance: Higher-level permissions are inherited downward", "restriction": "Restriction: No workspace access = No access to its contents", "required": "Required: Access to parent entities is mandatory", "cascadingRemoval": "Cascading Removal: Removing access at any level removes access to all nested items", "workspaceRemovalNote": "Removing workspace access automatically removes access to all pages, folders, and files within it."}, "tooltips": {"hierarchyInfo": "Permissions are inherited from workspace to page to folder to files", "missingParentPermissions": "Missing parent permissions", "missingParentPermission": "Missing parent permission"}, "errorMessages": {"noRoleSelectedForDeletion": "No role selected for deletion", "roleDeletedSuccessfully": "Role deleted successfully", "failedToDeleteRole": "Failed to delete role", "failedToLoadRolesAndPermissions": "Failed to load roles and permissions", "parentPermissionRequired": "You must grant {action} access to {parentResource} first", "permissionGrantedWithCascade": "Granted {action} access to {resource} and all nested items", "permissionRemovedWithCascade": "Removed {action} access from {resource} and all its nested items", "cannotGrantWithoutParent": "Cannot grant {action} access to {resource} without {action} access to {parentResource}", "allPermissionsGrantedWithCascade": "Granted all {resource} permissions and cascaded to nested items", "allPermissionsRemovedWithCascade": "Removed all {resource} permissions and nested items"}}, "changelog": {"title": "What's New", "newFeatures": "New Features", "improvements": "Improvements", "bugFixes": "Bug Fixes", "announcement": "Announcement", "release": "Release", "hotfix": "Hotfix", "maintenance": "Maintenance", "priority": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "type": {"release": "Release", "hotfix": "Hotfix", "maintenance": "Maintenance", "announcement": "Announcement"}, "viewChangelog": "View Changelog (New updates available)", "markAsRead": "<PERSON> <PERSON>", "dismissAll": "Dismiss All", "noChangelogs": "No new updates", "noChangelogsDescription": "You're all caught up! Check back later for new features and improvements.", "publishedOn": "Published on", "version": "Version", "commit": "Commit", "deployment": "Deployment", "page": {"title": "Changelog", "subtitle": "Stay up to date with new features, improvements, and bug fixes", "search": "Search...", "filterByCategory": "Filter by category", "allPublications": "All Publications", "newFeature": "New feature", "bugFix": "Bug fix", "improvement": "Improvement", "newFeatureLabel": "NEW FEATURE", "bugFixLabel": "BUG FIX", "improvementLabel": "IMPROVEMENT", "announcementLabel": "ANNOUNCEMENT", "today": "Today", "dayAgo": "1 day ago", "daysAgo": "{{count}} days ago", "weekAgo": "1 week ago", "weeksAgo": "{{count}} weeks ago", "monthAgo": "1 month ago", "monthsAgo": "{{count}} months ago", "yearAgo": "1 year ago", "yearsAgo": "{{count}} years ago", "author": "by {{author}}", "systemAuthor": "System", "commitLabel": "Commit:", "deploymentLabel": "Deployment:", "somethingWentWrong": "Something went wrong", "tryAgain": "Try Again", "noMatchingUpdates": "No matching updates found", "noUpdatesAvailable": "No updates available", "tryAdjustingFilters": "Try adjusting your search or filter criteria.", "checkBackLater": "Check back later for new updates and announcements.", "previous": "Previous", "next": "Next", "pageOf": "Page {{current}} of {{total}}"}}, "vectordb": {"providerConfiguration": "Provider Configuration", "provider": "Vector Database Provider", "selectProvider": "Select a provider", "mongodbVector": "MongoDB Vector", "chooseProvider": "Choose your vector database provider.", "connectionString": "Connection String", "connectionStringPlaceholder": "********************************:port", "connectionStringDescription": "Your MongoDB connection string including credentials.", "databaseName": "Database Name", "databaseNamePlaceholder": "your_database_name", "databaseNameDescription": "The name of your MongoDB database.", "collectionName": "Collection Name", "collectionNamePlaceholder": "your_collection_name", "collectionNameDescription": "The name of your MongoDB collection for vector storage.", "saveChanges": "Save changes", "updateSuccess": "VectorDB settings updated successfully", "updateError": "Error updating VectorDB settings", "providerRequired": "Provider is required", "connectionStringRequired": "Connection string is required", "databaseNameRequired": "Database name is required", "collectionNameRequired": "Collection name is required"}, "workspace": {"page": "Page", "contentImportedAsHtml": "The content will be imported as HTML.", "deleteFile": "Delete File", "deleteFolder": "Delete Folder", "deleteWorkspace": "Delete Workspace", "deleteWorkspaceConfirm": "Are you sure you want to delete this workspace?", "deleteWorkspaceWarning": "This action cannot be undone. This will permanently delete the workspace \"{name}\" and all of its pages, folders and documents.", "deleteWorkspaceTypeConfirm": "Please type \"{name}\" to confirm deletion:", "deleteWorkspacePermanently": "Delete Permanently", "deletePageConfirm": "Are you sure you want to delete this page?", "deletePageWarning": "This action cannot be undone. This will permanently delete the page \"{name}\" and all associated content.", "deleteFolderConfirm": "Are you sure you want to delete this folder?", "deleteFolderWarning": "This action cannot be undone. This will permanently delete the folder \"{name}\" and all contained files and subfolders ({count} items).", "deleteFileConfirm": "Are you sure you want to delete this file?", "deleteFileWarning": "This action cannot be undone. This will permanently delete the file \"{name}\" and its vectorized content.", "confirmDeletion": "Confirm Deletion", "typeToConfirm": "Type to confirm", "deletingWorkspace": "Deleting workspace...", "deletingPage": "Deleting page...", "deletingFolder": "Deleting folder...", "deletingFile": "Deleting file...", "workspaceDeletedSuccess": "Workspace deleted successfully", "failedToDeleteWorkspace": "Failed to delete workspace", "dangerZone": "Danger Zone", "dangerZoneDescription": "Irreversible and destructive actions.", "details": "Workspace Details", "nameRequired": "Workspace name is required", "newFolderName": "New folder name", "create": "Create New Workspace", "pages": "Pages", "creating": "Creating...", "createWorkspace": "Create Workspace", "detailsDescription": "Fill in the information below to create a new workspace.", "description": "Description", "descriptionPlaceholder": "Brief description of the content", "members": "Members", "initialsDescription": "These will be displayed as the workspace icon.", "initialsPlaceholder": "AB", "initials": "Workspace Initials (2 letters)", "namePlaceholder": "Enter workspace name", "workspace": "Workspace", "initialsRequired": "Initials are required", "workspaceMembers": "Workspace Members", "inviteUser": "Invite User", "inviteUserSubtitle": "Invite a user to join your workspace", "addMember": "Add Member", "addRemoveMember": "Add/Remove Member", "userWorkspaceManagement": "User Workspace Management", "userWorkspaceManagementDescription": "Manage user access and permissions across workspaces in your organization", "userAssignments": "User Assignments", "userAssignmentsDescription": "View and manage how users are assigned to workspaces with their respective roles", "addUserToWorkspace": "Add User to Workspace", "addUserToWorkspaceDescription": "Select a user and workspace to add them with a specific role", "removeUserFromWorkspace": "Remove User from Workspace", "removeUserFromWorkspaceDescription": "Are you sure you want to remove this user from the workspace? They will lose access to all resources in this workspace.", "userAddedToWorkspace": "User added to workspace successfully", "errorAddingUser": "Error adding user to workspace", "userRemovedFromWorkspace": "User removed from workspace successfully", "errorRemovingUser": "Error removing user from workspace", "noWorkspaceMembersFound": "No workspace members found matching your filters", "selectWorkspace": "Select Workspace", "allPages": "All Pages", "openPage": "Open Page", "sendingInvitation": "Sending invitation...", "invitationSentSuccess": "Invitation sent successfully", "failedToSendInvitation": "Failed to send invitation", "removingMember": "Removing member...", "memberRemovedSuccess": "Member removed successfully", "failedToRemoveMember": "Failed to remove member. Please try again.", "noWorkspacesYet": "No workspaces yet", "createFirstWorkspace": "Create your first workspace to start organizing your projects and collaborating with your team.", "newPage": "New Page", "searchPages": "Search pages...", "created": "Created", "noPagesFound": "No pages found. Create a new page to get started.", "createNewPage": "Create New <PERSON>", "renamePage": "<PERSON><PERSON>", "enterNewPageName": "Enter a new name for your page.", "enterPageName": "Enter a name for your new page.", "pageName": "Page Name", "enterPageNamePlaceholder": "Enter page name", "syncFolder": "Sync Folder", "syncSharePointFolder": "Sync SharePoint Folder", "syncWithSharePoint": "Sync With SharePoint", "syncWithGoogleDrive": "Sync With Google Drive", "removeSync": "Remove Sync", "revokeAndDelete": "Revoke and Delete", "revokeAndDeleteConfirm": "Are you sure you want to revoke sync and delete all synchronized content?", "revokeAndDeleteWarning": "This will permanently delete all folders and files that were imported from {provider}. Manually uploaded content will be preserved.", "revokeAndDeleteSuccess": "Sync revoked and synchronized content deleted successfully", "failedToRevokeAndDelete": "Failed to revoke sync and delete content", "revokingAndDeleting": "Revoking sync and deleting content...", "newFileName": "New file name", "createAndSync": "Create and Sync", "createNewFolder": "Create New Folder", "folderName": "Folder Name", "close": "Close", "creatingAndSyncingFolder": "Creating and syncing folder...", "folderCreatedAndSyncedSuccess": "Folder created and synced successfully", "errorCreatingFolder": "Error creating folder", "syncingFolder": "Syncing folder...", "folderSyncedSuccess": "Folder synced successfully", "errorSyncingFolder": "Error syncing folder", "connectAccountInSettings": "Please connect your account in integration settings", "removingSync": "Removing sync...", "syncRemovedSuccess": "Sync removed successfully", "failedToRemoveSync": "Failed to remove sync", "checkingSyncStatus": "Checking sync status...", "filesAndFoldersInSync": "All files and folders are in sync with {provider}", "synchronizedFiles": "Synchronized {count} files with {provider}", "folderSyncedSuccessfully": "{provider} folder synced successfully", "failedToSyncFolder": "Failed to sync folder", "googleDrive": "Google Drive", "sharePoint": "SharePoint", "oneDrive": "SharePoint", "googleDriveAndOneDrive": "Google Drive & SharePoint", "sync": "Sync", "pageCreatedSuccess": "Page created successfully!", "failedToCreatePage": "Failed to create page. Please try again.", "pageDeletedSuccess": "Page deleted successfully!", "failedToDeletePage": "Failed to delete page. Please try again.", "loading": "Loading...", "syncedWithCloud": "This page is synced with {provider} and is in read-only mode. To make changes, remove the sync from the Pages section.", "new": "New", "newFolder": "New Folder", "upload": "Upload", "uploadFiles": "Upload Files", "uploadFilesDescription": "Select files from your device to upload to this page.", "cancel": "Cancel", "searchFilesAndFolders": "Search files and folders...", "listView": "List view", "gridView": "Grid view", "tileView": "Tile view", "allItems": "All Items", "folderCount": "{count} folder", "foldersCount": "{count} folders", "fileCount": "{count} file", "filesCount": "{count} files", "name": "Name", "modified": "Modified", "type": "Type", "size": "Size", "actions": "Actions", "folder": "Folder", "file": "File", "rename": "<PERSON><PERSON>", "share": "Share", "delete": "Delete", "readOnly": "(Read-only)", "noItemsFound": "No items found. Create a folder or upload files to get started.", "folders": "Folders", "files": "Files", "openFolder": "Open Folder", "viewFile": "View File", "renameFolder": "<PERSON><PERSON>", "renameFile": "Rename File", "enterFolderName": "Enter a name for your folder.", "enterFileName": "Enter a name for your file.", "createFolder": "Create Folder", "enterFolderNamePlaceholder": "Enter folder name", "enterFileNamePlaceholder": "Enter file name", "loadingFolder": "Loading folder...", "folderLoadingError": "If this message persists, the folder may not exist or there might be an issue loading the data.", "returnToWorkspace": "Return to Workspace", "searchInFolder": "Search in this folder...", "uploadToFolder": "Select files from your device to upload to this folder.", "loadingFile": "Loading...", "back": "Back", "lastModified": "Last modified: {date}", "more": "More", "downloadFile": "Download File", "downloadDocument": "Download Document", "viewRawMarkdown": "View Raw Markdown", "downloadTextFile": "Download Text File", "downloadSpreadsheet": "Download Spreadsheet", "downloadPresentation": "Download Presentation", "downloadJsonFile": "Download JSON File", "downloadHtmlFile": "Download HTML File", "downloadCsvFile": "Download CSV File", "loadingDocumentContent": "Loading document content...", "loadingMarkdownContent": "Loading markdown content...", "loadingTextContent": "Loading text content...", "loadingSpreadsheetContent": "Loading spreadsheet content...", "loadingPresentationContent": "Loading presentation content...", "loadingJsonContent": "Loading JSON content...", "loadingHtmlContent": "Loading HTML content...", "loadingCsvContent": "Loading CSV content...", "documentNotAvailable": "Document not available", "documentCouldNotBeLoaded": "The document could not be loaded", "textFileNotAvailable": "Text file not available", "spreadsheetNotAvailable": "Spreadsheet not available", "presentationNotAvailable": "Presentation not available", "jsonFileNotAvailable": "JSON file not available", "htmlFileNotAvailable": "HTML file not available", "csvFileNotAvailable": "CSV file not available", "fileCouldNotBeLoaded": "The file could not be loaded", "fileTypeCannotBePreviewedTitle": "This file type cannot be previewed", "fileTypeCannotBePreviewedDesc": "The file format {extension} is not supported for preview.", "openInNewTab": "Open in new tab", "redirectingToWorkspace": "Redirecting to workspace page...", "loadingWorkspaceMessage": "Please wait while we load your workspace.", "addRemoveMembers": "Add/Remove members", "viewDetails": "View details", "appName": "Swiss Knowledge Hub", "workspaceDetails": "Workspace Details", "manageWorkspaceDetails": "Manage your workspace details", "updateSuccess": "Workspace updated successfully", "updateFailed": "Failed to update workspace", "errorFetchingDetails": "Error fetching workspace details", "workspaceNotFound": "Workspace not found", "noAccess": "No access", "vectorizationStatusTooltip": "Vectorization status indicates whether the file has been successfully indexed for AI search.", "importFromUrl": "Import from URL", "importFromUrlDescription": "Paste a URL to import content from a web page", "urlImportSuccess": "Content imported successfully", "urlImportAndVectorizationStarted": "Content imported and vectorization started", "urlImportSuccessButVectorizationFailed": "Content imported successfully but vectorization failed", "urlImportError": "Failed to import content", "urlAlreadyImported": "This URL has already been imported to this workspace", "extractImages": "Extract images", "contentCleaningLevel": "Content cleaning level", "cleaningLevelBasic": "Basic (keep all content)", "cleaningLevelMedium": "Medium (remove short paragraphs)", "cleaningLevelAggressive": "Aggressive (remove all non-essential content)", "crawlDepth": "Crawl Depth", "crawlDepth1": "1 (Main page only)", "crawlDepth2": "2 (Main page + linked pages)", "crawlDepth3": "3 (Deep crawl)", "sitemapUrls": "Additional URLs Found", "selectUrlsToImport": "Select which URLs you want to import:", "batchImport": "Import {count} Selected URLs", "batchImportSuccess": "Successfully imported {{count}} pages", "batchImportPartialFailure": "{count} URLs failed to import. Some may have been blocked by robots.txt restrictions.", "batchImportFailure": "All URLs failed to import", "batchImportError": "Error during batch import", "noUrlsSelected": "Please select at least one URL to import", "extractedImages": "Extracted images", "useBackgroundProcessing": "Use background processing", "backgroundProcessingDescription": "Process URL import in the background to avoid waiting", "importStatus": "Import Status", "statusPending": "Pending", "statusProcessing": "Processing", "statusCompleted": "Completed", "statusFailed": "Failed", "retryImport": "Retry Import", "urlImportStarted": "URL import started in the background", "batchImportAndVectorizationSuccess": "{count} URLs imported and vectorization started", "importFormat": "Import Format", "contentImportedAsMarkdown": "Content will be imported as Markdown", "batchImportProgress": "Batch Import Progress", "fetchingAndCrawling": "Fetching & Crawling..."}, "page": {"members": "Page Members", "addMember": "Add Member", "addFirstMember": "Add the first member to get started", "noMembers": "No page members yet", "memberAdded": "Member added successfully", "memberRemoved": "Member removed successfully", "memberRoleUpdated": "Member role updated successfully", "failedToAddMember": "Failed to add member", "failedToRemoveMember": "Failed to remove member", "failedToUpdateRole": "Failed to update member role", "memberRole": "Member Role", "editorRole": "Editor Role", "adminRole": "Admin Role", "memberDesc": "View only", "editorDesc": "View and edit", "adminDesc": "Full access", "makeAdmin": "Make Admin", "makeEditor": "Make Editor", "makeMember": "Make Member", "removeMember": "Remove Member", "membershipSource": "Source", "manualSource": "Manual", "sharePointSource": "SharePoint", "workspaceSource": "Workspace", "sharePointPermission": "SharePoint Permission", "lastChecked": "Last Checked", "neverChecked": "Never", "memberManagement": "Member Management", "manageMembersDescription": "Manage who can access this page and their permission levels"}, "settings": {"organization": {"title": "Organization", "description": "Manage your organization's profile and settings."}, "members": {"title": "Team Members", "description": "Manage your organization's team members."}, "roles": {"title": "Roles", "description": "Manage custom roles and permissions"}, "userWorkspaceManagement": {"title": "User Workspace Management", "description": "Manage user access to workspaces"}, "groups": {"title": "Groups", "description": "Manage groups and workspace access"}, "embedded": {"title": "Embedded Provider", "description": "Configure your embedded provider settings"}, "llm": {"title": "LLM Provider", "description": "Configure your LLM provider settings"}, "llmScope": {"title": "<PERSON><PERSON>", "description": "Control AI assistant data access and capabilities", "saveSettings": "Save Settings"}, "mcp": {"title": "MCP Servers", "description": "Manage Model Context Protocol servers", "active": "Active", "inactive": "Inactive", "toggleStatus": "Toggle server status"}, "vectordb": {"title": "VectorDB Provider", "description": "Configure your vectorDB provider settings"}, "integrations": {"comingSoon": "Coming Soon", "selectSharePointSite": "Select SharePoint Site", "selectSharePointFolder": "Select SharePoint Folder", "title": "Storage Integrations", "description": "Configure your storage integrations"}, "navigationGroups": {"organizationManagement": "Organization Management", "userAccessControl": "User & Access Control", "aiData": "AI & Data", "integrations": "Integrations"}}, "embedded": {"providerConfiguration": "Provider Configuration", "provider": "Provider", "selectProvider": "Select a provider", "apiKey": "API Key", "modelName": "Model Name", "deploymentName": "Deployment Name", "endpoint": "API Endpoint", "enterApiKey": "**********", "enterModelName": "embed-v-4-0", "enterDeploymentName": "******", "enterEndpoint": "https://openai.azure.com/openai/deployments/******/chat/completions", "saving": "Saving...", "saveChanges": "Save Changes", "updateSuccess": "Embedded settings updated successfully", "updateFailed": "Failed to update embedded settings"}, "inviteMember": {"inviteMember": "Invite member", "inviteTeamMember": "Invite team member", "inviteDescription": "Invite a new member to join your organization.", "emailAddress": "Email address", "emailPlaceholder": "<EMAIL>", "selectRolePlaceholder": "Select a role", "sending": "Sending...", "sendInvitation": "Send invitation", "invitationSent": "Invitation sent to {email}", "invitationFailed": "Failed to send invitation. Please try again.", "validEmail": "Please enter a valid email address.", "selectRole": "Please select a role."}, "memberList": {"teamMembers": "Team Members", "manageTeamMembers": "Manage your team members", "noTeamMembers": "No team members yet", "removeTeamMember": "Remove Team Member", "confirmRemove": "Are you sure you want to remove {name} from your tenant? This action cannot be undone.", "removing": "Removing...", "roleChangeSuccess": "Changed {name}'s role to {role}", "roleChangeFailed": "Failed to update member role", "memberRemoveSuccess": "Removed {name} from the tenant", "memberRemoveFailed": "Failed to remove member"}, "chatHistory": {"title": "Chat History", "noHistoryYet": "No chat history yet", "startNewChatPrompt": "Start a new chat to begin your conversation", "startNewChat": "Start New Chat", "renameSuccess": "Chat renamed successfully", "renameFailed": "Failed to rename chat", "deleteSuccess": "<PERSON><PERSON> deleted successfully", "deleteFailed": "Failed to delete chat"}, "theme": {"toggleTheme": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "groups": {"title": "Groups", "groups": "Groups", "groupsDescription": "Organize users into groups and assign roles for streamlined workspace access management", "groupsHelp": "Groups allow you to manage multiple users' permissions at once by assigning roles to the group", "selectGroupToManage": "Select a Group to Manage", "selectGroupDescription": "Choose a group from the list to view and manage its members and workspaces", "createFirstGroupDescription": "Groups help you organize users and manage their workspace access efficiently. Create your first group to get started.", "selectGroupPrompt": "Select a group", "noDescription": "No description", "noGroupsFound": "No groups found", "searchGroups": "Search groups...", "group": "Group", "selectGroup": "Select Group", "noGroup": "No Group", "createGroup": "Create Group", "createNewGroup": "Create New Group", "createGroupDescription": "Create a new group to manage access to workspaces", "groupName": "Group Name", "enterGroupName": "Enter group name", "description": "Description", "enterDescription": "Enter description (optional)", "groupCreated": "Group created successfully", "groupCreateFailed": "Failed to create group", "noGroupsYet": "No groups yet", "createFirstGroup": "Create your first group to manage workspace access", "members": "Members", "workspaces": "Workspaces", "role": "Assign Role", "groupMembers": "Group Members", "addUser": "Add User", "noMembers": "No members in this group yet", "noMembersYet": "No members yet", "addMembersDescription": "Add members to this group to give them access to all assigned workspaces", "assignedWorkspaces": "Assigned Workspaces", "assignWorkspace": "Assign Workspace", "noWorkspaces": "No workspaces assigned to this group yet", "noWorkspacesYet": "No workspaces yet", "assignWorkspacesDescription": "Assign workspaces to this group to give all members access", "addUserToGroup": "Add User to Group", "addUserToGroupDescription": "Add a user to this group. They will gain access to all workspaces assigned to this group.", "userAddedToGroup": "User added to group successfully", "failedToAddUserToGroup": "Failed to add user to group", "removeUserConfirm": "Remove User from Group", "removeUserDescription": "Are you sure you want to remove this user from the group? They will lose access to all workspaces assigned to this group unless they have direct access.", "userRemovedFromGroup": "User removed from group successfully", "failedToRemoveUser": "Failed to remove user from group", "assignWorkspaceToGroup": "Assign Workspace to Group", "assignWorkspaceDescription": "Assign a workspace to this group. All members of the group will gain access to this workspace.", "selectWorkspace": "Select Workspace", "noWorkspacesFound": "No workspaces found", "workspaceAssignedToGroup": "Workspace assigned to group successfully", "failedToAssignWorkspace": "Failed to assign workspace to group", "removeWorkspaceConfirm": "Remove Workspace from Group", "removeWorkspaceDescription": "Are you sure you want to remove this workspace from the group? Group members will lose access to this workspace unless they have direct access.", "workspaceRemovedFromGroup": "Workspace removed from group successfully", "failedToRemoveWorkspace": "Failed to remove workspace from group", "loadingGroups": "Loading groups...", "failedToFetchGroups": "Failed to fetch groups", "manageAccessDescription": "Create and manage groups to control workspace access", "workspaceAccess": "Role Access", "assignRole": "Assign Role", "assignRoleToGroup": "Assign Role to Group", "assignRoleDescription": "Assign a role to this group. All members of the group will inherit the permissions defined in this role.", "selectRole": "Select Role", "noRoleAssigned": "No role assigned", "roleDescription": "Assign a role to this group to define what workspace permissions group members will have", "roleAssignedToGroup": "Role assigned to group successfully", "failedToAssignRole": "Failed to assign role to group", "removeRoleConfirm": "Remove Role from Group", "removeRoleDescription": "Are you sure you want to remove this role from the group? Group members will lose the permissions granted by this role.", "roleRemovedFromGroup": "Role removed from group successfully", "failedToRemoveRole": "Failed to remove role from group", "selectUserWithCustomRole": "Select User with Custom Role", "noUsersWithCustomRoles": "No users with custom roles found", "tryDifferentSearch": "Try a different search term or create a new group", "groupDeletedSuccessfully": "Group deleted successfully", "failedToDeleteGroup": "Failed to delete group", "deleteGroupConfirm": "Delete Group", "deleteGroupDescription": "Are you sure you want to delete '{groupName}'? This action cannot be undone and will remove all group members and role assignments.", "loadingGroup": "Loading group...", "failedToFetchGroup": "Failed to fetch group details", "groupNotFound": "Group Not Found", "groupNotFoundDescription": "The group you're looking for doesn't exist or you don't have permission to view it.", "backToGroups": "Back to Groups"}, "memberSidebar": {"newChat": "New Chat", "chatHistory": "Chat History", "group": "Group", "groups": "Groups", "createGroup": "Create Group", "editGroup": "Edit Group", "deleteGroup": "Delete Group", "confirmDelete": "Are you sure you want to delete this group?", "groupDeleted": "Group deleted successfully", "groupDeleteFailed": "Failed to delete group", "createNewChatGroup": "Create New Chat Group", "createGroupDescription": "Create a new chat group to organize your chats.", "groupName": "Group Name", "enterGroupName": "Enter group name", "groupCreated": "Group created successfully", "groupCreateFailed": "Failed to create group"}, "sidebar": {"dashboard": "Dashboard", "executiveOverview": "Executive Overview", "myHub": "My Hub", "askAi": "Ask AI", "chatHistory": "Chat History", "support": "Support", "upgrade": "Upgrade", "managePages": "Manage Pages", "addWorkspace": "+Add", "help": "Documentation", "toggleSidebar": "Toggle Sidebar", "privacyPolicy": "Privacy Policy", "sharedThreads": "Shared Threads", "mcpServers": "MCP Servers", "noWorkspaces": "No workspaces available", "noWorkspacePermission": "You don't have permission to view workspaces", "createFirstWorkspace": "Create your first workspace to get started"}, "chat": {"askAi": "Ask AI", "enterMessage": "Enter a message", "errorProcessingRequest": "Sorry, there was an error processing your request.", "renaming": "Renaming chat...", "renameSuccess": "Chat renamed successfully", "renameFailed": "Failed to rename chat", "moving": "Moving chat...", "moveSuccess": "<PERSON><PERSON> moved successfully", "moveFailed": "Failed to move chat", "removing": "Removing chat...", "removeSuccess": "Chat removed successfully", "removeFailed": "Failed to remove chat", "moveChatDescription": "Select a group to move this chat to.", "selectGroup": "Select group.", "moveToGroup": "Move to Group", "dragToMove": "Drag to move to a different group", "dropHere": "Drop here to add to this group", "dropToUngroup": "Drop here to remove from group", "openDocument": "Open document", "downloadDocument": "Download", "title": "Title", "messages": "Messages", "lastActivity": "Last Activity", "export": "Export", "deleteChat": "Delete Chat", "deleteChatConfirmation": "Are you sure you want to delete this chat? This action cannot be undone. All messages and data will be permanently removed.", "deleting": "Deleting...", "untitledChat": "Untitled Chat", "ungroupSuccess": "<PERSON><PERSON> removed from group successfully", "like": "Like this response", "dislike": "Dislike this response", "regenerate": "Regenerate response", "sources": "Sources", "copyMessage": "Copy message", "download": "Download", "openInNewTab": "Open", "filesAttached": "Files attached", "moreFiles": "more files", "files": "files", "filesCount": "files", "shown": "shown", "loadingPdf": "Loading PDF...", "fileSizeBytes": "0 Bytes", "fileSizeUnits": {"bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB"}, "fileTypes": {"audioFile": "Audio File", "audioDescription": "This audio file can be processed for transcription and analysis.", "wordDocument": "Word Document", "wordDescription": "Text content will be extracted and analyzed from this document.", "spreadsheet": "Spreadsheet", "spreadsheetDescription": "Data will be analyzed including structure, columns, and content.", "presentation": "Presentation", "presentationDescription": "Slide content and structure will be analyzed.", "csvData": "CSV Data", "csvDescription": "Tabular data will be parsed and analyzed for insights.", "textFile": "Text File", "textDescription": "Full text content will be extracted and analyzed."}, "copiedToClipboard": "Copied to clipboard", "viewSource": "View source", "feedbackLike": "Thank you for your positive feedback!", "feedbackDislike": "Thank you for your feedback. We'll work to improve.", "feedbackError": "Error saving feedback. Please try again.", "regenerating": "Regenerating response...", "regenerateError": "Error regenerating response. Please try again.", "fileProcessingError": "Error processing file.", "uploadingFiles": "Uploading files...", "uploadSuccess": "Files uploaded successfully", "originalResponse": "Original response", "regeneratedResponse": "Regenerated response", "regeneratedFrom": "Regenerated from original", "regenerationHistory": "Response Comparison", "previousResponse": "Previous response", "nextResponse": "Next response", "viewRegeneratedResponses": "View regenerated responses", "viewOriginalResponse": "View original response", "showLatestResponse": "Show latest response", "showOriginalResponse": "Show original response", "page": "Page", "documentPreview": "Document Preview", "edit": "Edit message", "editMessage": "Edit this message", "editLastFiveOnly": "You can only edit the last 5 messages", "editing": "Editing message...", "editSuccess": "Message edited successfully", "editFailed": "Failed to edit message", "editError": "Error editing message. Please try again.", "saveEdit": "Save Edit", "cancelEdit": "Cancel Edit", "originalMessage": "Original message", "editedMessage": "Edited message", "editedFrom": "Edited from original", "editHistory": "Edit History", "previousEdit": "Previous edit", "nextEdit": "Next edit", "viewEditedVersions": "View edited versions", "viewOriginalMessage": "View original message", "showLatestEdit": "Show latest edit", "showOriginalMessage": "Show original message", "generatingResponse": "Generating new response...", "editTriggeredRegeneration": "Edit triggered new AI response", "conversationThread": "Conversation thread", "originalConversation": "Original conversation", "editedConversation": "Edited conversation", "citationsAccordion": "Citations", "relevance": "Relevance", "includeWebResults": "Include Public Web Results", "webSearchTooltip": "Include results from the public web to enhance answers", "webSearchLimitExceeded": "Daily web search limit exceeded. Please try again tomorrow.", "webSearchError": "Web search error", "webSearchErrorDescription": "There was an error performing the web search. Please try again later.", "tryAgainTomorrow": "Please try again tomorrow.", "documentSources": "Documents", "webSources": "Web", "thinking": "Thinking...", "uploadImage": "Upload image", "invalidImageFormat": "Invalid image format. Please use JPG, PNG, or WebP.", "imageTooLarge": "Image size should be less than 10MB.", "tooManyImages": "Maximum 5 images allowed.", "imageProcessingError": "Error processing image.", "dropImagesHere": "Drop images here", "removeImage": "Remove image", "imageUploadHelp": "Drag & drop images or click to upload (JPG, PNG, WebP, max 10MB)", "enterMessageWithImages": "Ask about your images or add a message...", "imagesAttached": "Images attached", "imagesCount": "images", "attachedImages": "Attached Images", "moreImages": "more images", "addFilesAndImages": "Add Files and Images", "dropFilesHere": "Drop files here", "slotsRemaining": "{count} slots remaining", "removeFile": "Remove file", "filesUploaded": "{current} of {max} files uploaded", "limitReached": "Limit reached", "searchModeTooltips": {"internalSearch": "Search internal documents and knowledge base", "webSearch": "Search the web for up-to-date information", "deepResearch": "Comprehensive research-grade analysis", "internalSearchDisabled": "Internal search is disabled by your organization's LLM scope settings", "webSearchDisabled": "Web search is disabled by your organization's LLM scope settings", "deepResearchDisabled": "Deep research is disabled by your organization's LLM scope settings", "mcpOnlyRestriction": "Only MCP tools are allowed by your organization's LLM scope settings"}}, "quickAsk": {"buttonLabel": "Quick Ask", "mcpSearch": "MCP", "internalSearch": "Internal", "webSearch": "Web", "placeholder": "Ask a quick question...", "thinking": "Thinking...", "searching": "Searching for the best answer", "emptyState": "Ask a question to get a quick answer", "emptyStateDescription": "Get instant answers without saving the conversation to your workspace", "openAsChat": "Open as <PERSON><PERSON>", "clearConversation": "Clear Conversation", "tooltips": {"addAttachments": "Add attachments", "webSearch": "Search the web for up-to-date information. Click both Internal and Web for hybrid search.", "webSearchHybrid": "Web search (combined with internal search)", "webSearchDisabled": "Web search is disabled by your organization's LLM scope settings", "internalSearch": "Search internal documents and knowledge base. Click both Internal and Web for hybrid search.", "internalSearchHybrid": "Internal search (combined with web search)", "internalSearchDisabled": "Internal search is disabled by your organization's LLM scope settings", "mcpSearch": "Search MCP servers", "mcpSearchDisabled": "MCP search is disabled by your organization's LLM scope settings", "loadingSettings": "Loading LLM scope settings..."}}, "integration": {"comingSoon": "Coming Soon", "oneDriveDescription": "Instantly Connect your SharePoint sites to sync your files.", "sharePointDescription": "Instantly Connect your SharePoint sites to sync your files.", "googleDriveDescription": "Instantly Connect your Google Drive Workspace to sync your files.", "connected": "Connected", "notConnected": "Not connected", "connect": "Connect", "disconnect": "Disconnect", "configure": "Configure", "manage": "Manage", "selectSite": "Select SharePoint Site", "selectFolder": "Select SharePoint Folder", "selectSharePointSite": "Select SharePoint Site", "selectSharePointFolder": "Select SharePoint Folder", "noSitesFound": "No sites found", "noFoldersFound": "No folders found", "syncing": "Syncing...", "syncWithSharePoint": "Sync with SharePoint", "syncComplete": "Sync complete! {count} files synced.", "selectedSite": "Selected Site", "selectedFolder": "Selected Folder", "syncDisclaimer": "All files and subfolders within the selected folder will be synchronized and available in read-only mode.", "root": "Root"}, "sharepoint": {"integrationRequired": "SharePoint Integration Required", "integrationRequiredMessage": "You need to connect your SharePoint account to access this page. Please go to integration settings to connect your account.", "connectAccount": "Connect SharePoint Account", "checkAccess": "Check Access", "verified": "Access Verified", "cachedAccess": "Using cached SharePoint access", "refresh": "Refresh", "accessDeniedMessage": "You don't have permission to access this SharePoint-synced page.", "needAccess": "Need access to this content?", "contactOwner": "Contact the SharePoint site owner or your administrator to request access.", "lastChecked": "Last checked", "connectNeeded": "Connect Required", "cached": "<PERSON><PERSON><PERSON>", "checking": "Checking...", "accessDenied": "Access denied. Contact page administrator to be added as a member.", "accessContentDenied": "You don't have permission to access this SharePoint content", "contentNotFound": "SharePoint content not found or has been moved", "unableToVerifyAccess": "Unable to verify SharePoint access", "errorCheckingAccess": "Error checking SharePoint access"}, "password": {"currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "placeholder": "••••••••", "requirements": "Password must be at least 8 characters long.", "updateButton": "Update Password", "updating": "Updating password...", "updateSuccess": "Password updated successfully", "updateFailed": "Failed to update password", "currentPasswordMinLength": "Current password must be at least 6 characters.", "newPasswordMinLength": "New password must be at least 8 characters.", "confirmPasswordMinLength": "Confirm password must be at least 8 characters.", "resetPassword": "Reset Password", "createNewPassword": "Create a new password for your account", "createPasswordPlaceholder": "Create a new password", "confirmPasswordPlaceholder": "Confirm your new password", "resetting": "Resetting your password...", "resetSuccess": "Password successfully reset!", "passwordReset": "Password Reset", "resetSuccessMessage": "Your password has been successfully reset.", "redirectMessage": "You will be redirected to the sign in page in a few seconds.", "verifyingLink": "Verifying your reset link...", "invalidLink": "Invalid Link", "linkExpired": "This password reset link is invalid or has expired.", "requestNewLink": "Request New Link", "tokenVerificationFailed": "Could not verify reset token. Please try again."}, "dashboard": {"welcomeMessage": "Welcome to Swiss Knowledge Hub", "getStartedMessage": "To get started, create your first organization to collaborate with your team.", "dashboardTitle": "Dashboard", "organizationCount": "You are a member of {count} organization", "organizationCountPlural": "You are a member of {count} organizations"}, "profile": {"profileInformation": "Profile Information", "updateProfileDetails": "Update your profile information.", "firstNamePlaceholder": "<PERSON>", "lastNamePlaceholder": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "deleteAccount": "Delete Account", "deleteAccountWarning": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.", "confirmDeleteAccount": "Yes, delete my account", "deletingAccount": "Deleting account...", "errorLoading": "Error loading profile. Please try again later.", "profileUpdated": "Profile updated successfully", "updateFailed": "Failed to update profile", "accountDeleted": "Account deleted successfully", "deleteFailed": "Failed to delete account", "dangerZone": "Danger Zone", "areYouSure": "Are you absolutely sure?"}, "verification": {"verifyEmail": "Verify Your Email", "sentEmailTo": "We've sent a verification email to {email}", "checkEmail": "Please check your email to verify your account", "instructions": "Before you can access your account, you need to verify your email address. Please check your inbox and click on the verification link in the email we sent you.", "cantFindEmail": "Can't find the email?", "checkSpam": "Check your spam or junk folder", "checkEmailCorrect": "Make sure you entered the correct email address", "allowTime": "Allow a few minutes for the email to arrive", "resendEmail": "Resend Verification Email", "backToSignIn": "Back to Sign In", "emailRequired": "Email address is required", "sendingEmail": "Sending verification email...", "emailSentSuccess": "Verification email sent successfully!", "resendFailed": "Failed to resend verification email. Please try again.", "registrationSuccessful": "Registration successful! Please check your email to verify your account.", "sentVerificationEmailTo": "We've sent a verification email to <strong>{email}</strong>. Please check your inbox and click the verification link to activate your account.", "clickToResend": "Click here to resend the verification email", "goToSignIn": "Go to Sign In"}, "billing": {"Starter": "Starter", "Business": "Business", "Enterprise": "Enterprise", "Custom": "Custom", "total": "Total", "discountCode": "Discount Code", "enterDiscountCode": "Enter discount code", "apply": "Apply", "discountCodeApplied": "Discount code applied successfully", "descriptionStarter": "Perfect for small teams or individuals getting started.", "descriptionBusiness": "Ideal for growing teams with moderate usage needs.", "descriptionEnterprise": "For professional teams with higher usage requirements.", "descriptionCustom": "Custom solution for large organizations with specific needs.", "title": "Billing & Subscription", "subtitle": "Manage your subscription and billing information", "currentSubscription": "Current Subscription", "currentSubscriptionDesc": "Your current plan and usage information", "trialBadge": "Free Trial", "trialEndsOn": "Trial ends on {date}", "plan": "Plan", "includedUsers": "{count} included users", "manageSubscriptionAddons": "Manage Subscription Add-ons", "adjustSubscriptionAddons": "Adjust users and storage for your subscription", "users": "Users", "storage": "Storage", "totalSubscriptionCost": "Total Subscription Cost", "increasingSubscription": "This will increase your monthly cost by CHF {price}", "decreasingSubscription": "Subscription downgrades are not available.", "additionalStorage": "Additional Storage", "additionalStorageGB": "{count} GB additional storage", "noAdditionalStorage": "No additional storage", "included": "Included", "free": "Free", "totalStorage": "Total Storage", "basePlanStorage": "{storage} GB included in plan", "currentUsage": "Current Usage", "exceededBaseStorage": "Exceeded base plan storage", "addingStorage": "Adding {count} GB storage will increase your monthly cost by CHF {price}", "reducingStorage": "Reducing {count} GB storage will decrease your monthly cost by CHF {price}", "manageStorage": "Manage Vector Storage", "adjustStorage": "Adjust your vector database storage", "updateStorageButton": "Update Storage", "additionalStorageFee": "Additional storage available at tiered pricing", "includedStorage": "{count} GB vector storage included", "includedPlusAdditionalStorage": "{included} GB included + {additional} GB additional storage", "subscriptionUpdateSuccess": "Subscription updated successfully", "subscriptionUpdateFailed": "Failed to update subscription", "includedPlusAdditionalUsers": "{included} included users + {additional} additional users", "vectorStoreUsage": "Vector Store Usage", "usageOf": "{used} GB of {total} GB used", "availablePlans": "Available Plans", "choosePlan": "Choose the plan that best fits your needs", "manageSubscription": "Manage Subscription", "currentPlan": "Current Plan", "contactUs": "Contact Us", "subscribe": "Subscribe", "upgradePlan": "Upgrade Plan", "processing": "Processing...", "priceCHF": "CHF {price}", "perMonth": "per month", "perYear": "per year", "monthly": "Monthly", "yearly": "Yearly", "save": "Save", "savePercent": "Save {percent}", "saveCHF": "Save CHF {amount}", "save2Months": "Save 2 months", "additionalUserFee": "+ CHF {fee}.- per additional user", "includes": "Includes", "customNumberOf": "Custom No. of", "usersLabel": "users", "custom": "Custom", "gbVectorStore": "GB vector store", "allFeatures": "All features", "hostingInSwitzerland": "Hosting in Switzerland", "standardSupport": "Standard support", "additionalUsers": "Additional Users", "additionalUsersDescription": "Your {planName} plan includes {includedUsers} users. Would you like to add more?", "summary": "Summary", "basePlan": "Base plan ({users} users)", "additionalUsersCount": "Additional users ({count})", "pricePerUserCHF": "CHF {price} per user", "totalUsers": "Total ({count} users)", "pricePerMonth": "CHF {price}/month", "pricePerYear": "CHF {price}/year", "confirmAndCheckout": "Confirm & Checkout", "noChangesToUpdate": "No changes to update.", "portalOpenFailed": "Failed to open customer portal. Please try again.", "loginRequiredForUpgrade": "You must be logged in to upgrade your plan", "checkoutSessionFailed": "Failed to create checkout session. Please try again.", "subscriptionUpdated": "Your subscription has been updated successfully.", "subscriptionCanceled": "Subscription update was canceled.", "noTenantSelected": "No tenant selected. Please select a tenant to view billing information.", "adjustSubscription": "Adjust users and storage for your subscription", "updateSubscription": "Update Subscription", "addingUsers": "Adding {count} users will increase your monthly cost by CHF {price}", "removingUsers": "Removing {count} users will decrease your monthly cost by CHF {price}", "customerNotFound": "Your Stripe customer account was not found. Please try again or contact support if the issue persists.", "updateUsers": "Update Users", "updateStorageAction": "Update/Add Storage", "addStorageAction": "Add Storage", "existingAdditionalStorage": "Existing additional storage ({count} GB)", "newTotalStorage": "New total additional storage ({count} GB)", "newStorageBeingAdded": "New storage being added ({count} GB)", "totalAdditionalStorage": "Total additional storage ({count} GB)", "currentAdditionalStorage": "Current Additional Storage", "confirmUpdateUsers": "Confirm User Update", "confirmUpdateStorage": "Confirm Storage Update", "confirmAddUsers": "Are you sure you want to add {count} additional users? This will increase your {interval} cost by CHF {price}.", "confirmRemoveUsers": "Are you sure you want to remove {count} users? This will decrease your {interval} cost by CHF {price}.", "confirmAddStorage": "Are you sure you want to add {count} GB of additional storage? This will increase your {interval} cost by CHF {price}.", "confirmRemoveStorage": "Are you sure you want to remove {count} GB of storage? This will decrease your {interval} cost by CHF {price}.", "confirmUpdate": "Confirm Update", "selectStorageIncrement": "Select Storage Increment to Add", "addSelectedStorage": "Add {size} GB Storage", "addingStorageToExisting": "Adding to existing {existing} GB", "resetStorage": "Reset", "invalidStorageTier": "Invalid storage tier selected.", "noStorageTiersSelected": "No storage tiers selected.", "currentStorageTiers": "Current Storage Tiers", "duplicateStorageTier": "Error: Duplicate storage tier detected. Please try a different configuration.", "usersUpdateSuccess": "Users updated successfully", "usersUpdateFailed": "Failed to update users", "storageUpdateSuccess": "Storage updated successfully", "storageUpdateFailed": "Failed to update storage", "processingRequest": "Processing...", "errorFetchingPrices": "Error fetching prices from Stripe. Using fallback prices.", "downgradeNotAllowed": "You cannot downgrade your plan yourself. Please contact support."}, "invitations": {"noPending": "No pending invitations", "inviteMembers": "When you invite members, they will appear here.", "sent": "<PERSON><PERSON>", "expires": "Expires", "copyLink": "Copy invitation link", "resend": "Resend invitation", "cancel": "Cancel invitation", "cancelInvitation": "Cancel invitation", "cancelConfirmation": "Are you sure you want to cancel the invitation sent to {email}? They will no longer be able to join the organization with this invitation.", "keep": "Keep", "cancelSuccess": "Invitation cancelled successfully", "cancelError": "Failed to cancel invitation", "resendSuccess": "Invitation resent successfully", "resendError": "Failed to resend invitation", "linkCopied": "Invitation link copied to clipboard"}, "invitationAcceptance": {"joinOrganization": "Join {<PERSON><PERSON><PERSON>}", "invitedByAs": "{inviter<PERSON><PERSON>} has invited you to join as a {role}.", "welcomeBack": "Welcome back, {name}! Please sign in to accept this invitation.", "verifyingInvitation": "Verifying invitation...", "invitationError": "Invitation Error", "couldNotVerify": "We couldn't verify your invitation.", "returnToHome": "Return to Home", "yourName": "Your Name", "enterFullName": "Enter your full name", "createPassword": "Create Password", "confirmPassword": "Confirm Password", "accountFound": "Account Found", "accountExistsMessage": "An account with this email already exists. You can accept this invitation directly, or", "accountExistsMessageEnd": "if you prefer.", "signInFirst": "sign in first", "acceptInvitation": "Accept Invitation", "createAccountAndAccept": "Create Account & Accept Invitation", "processing": "Processing", "invitationAcceptedSuccess": "Invitation accepted successfully!", "failedToAccept": "Failed to accept invitation", "notCurrentUser": "Not {name}?", "signOut": "Sign out", "wantToSignInFirst": "Want to sign in first?", "signIn": "Sign in", "alreadyHaveAccount": "Already have an account?", "nameMinLength": "Name must be at least 2 characters", "passwordMinLength": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match"}, "llmSettings": {"providerConfiguration": "Provider Configuration", "provider": "Provider", "selectProvider": "Select a provider", "chooseProvider": "Choose your LLM provider", "apiKey": "API Key", "enterApiKey": "**********", "apiKeyDescription": "Your {provider} API key", "apiKeyRequired": "API Key is required", "modelName": "Model Name", "enterModelName": "DeepSeek-V3", "modelDescription": "The name of the model to use (e.g., gpt-4)", "modelRequired": "Model name is required", "azureEndpoint": "Azure Endpoint", "enterAzureEndpoint": "Enter Azure endpoint URL", "azureEndpointDescription": "Your Azure OpenAI service endpoint URL", "azureDeploymentName": "Azure Deployment Name", "enterDeploymentName": "Enter deployment name", "deploymentDescription": "The name of your Azure OpenAI deployment", "saveSettings": "Save Settings"}, "llmScope": {"configuration": "LLM Scope Configuration", "description": "Control what data sources and capabilities the AI assistant can access for your organization. This setting affects all users and workspaces within your organization.", "refresh": "Refresh", "ownerOnlyMessage": "Only organization owners can modify LLM scope settings. Current scope:", "loadingSettings": "Loading LLM scope settings...", "selectScope": "Select LLM scope", "labels": {"internalOnly": "Internal Only", "externalOnly": "External Only", "mcpOnly": "MCP Tools Only", "hybrid": "Hybrid (Internal + Web)", "fullAccess": "Full LLM Access"}, "descriptions": {"internalOnly": "Access only to internal documents and knowledge base. No external web search.", "externalOnly": "Access only to external web search. No internal documents.", "mcpOnly": "Access only to MCP (Model Context Protocol) tools and external services. No internal documents or web search.", "hybrid": "Access to internal documents, external web search, and MCP servers. No advanced AI features.", "fullAccess": "Full LLM capabilities including deep research, advanced AI features, and all data sources."}, "updateSuccess": "LLM scope settings updated successfully", "updateFailed": "Failed to update LLM scope settings", "updateError": "An error occurred while updating settings", "ownerOnlyError": "Only organization owners can modify LLM scope settings", "saveSettings": "Save Settings", "availableFeatures": "Available Features", "capabilities": {"title": "Capabilities with selected scope:", "internalDocuments": "Internal Documents", "webSearch": "Web Search", "mcpServers": "MCP Servers", "hybridSearch": "Hybrid Search", "fullLLMAccess": "Full LLM Access"}, "restrictions": {"mcpOnlyMessage": "Only MCP tools are allowed by your organization's LLM scope settings"}, "scopeOptions": {"internalOnly": "Internal Only", "fullLLMScope": "Full LLM Scope", "internalOnlyDescription": "Access only to internal documents and knowledge base. No external web search.", "fullAccessDescription": "Full LLM capabilities with configurable access to external resources."}, "features": {"webSearch": "Web Search", "mcpTools": "MCP Tools", "deepSearchResearch": "Deep Search & Research", "selectFeaturesDescription": "Select the features you want to enable for your organization."}}, "integrations": {"googleDriveSync": "Google Drive Sync", "oneDriveSync": "OneDrive Sync", "syncDescription": "Sync your cloud folders and files with your workspace. This will create a local copy of your files and maintain the folder structure.", "syncNow": "Sync Now", "filesSynced": "{count} files synced", "selectFolderFirst": "Please select a folder to sync first"}, "bulkImport": {"members": {"title": "Bulk Import Members", "description": "Import multiple team members at once using a CSV file", "triggerButton": "Bulk Import", "steps": {"upload": "Upload", "preview": "Preview", "import": "Import"}, "buttons": {"cancel": "Cancel", "back": "Back", "import": "Import {count} Members", "importing": "Importing...", "importMore": "Import More", "done": "Done"}, "messages": {"successfullyImported": "Successfully imported {count} member{plural}", "importFailed": "Import failed. Please try again.", "csvParseError": "Found {count} errors in CSV file", "csvReadError": "Error reading CSV file. Please try again."}, "csvUpload": {"title": "Upload CSV File", "description": "Drag and drop your CSV file here, or click to browse", "chooseFile": "Choose CSV File", "supportedFormat": "Supported format: CSV files only", "template": {"title": "Need a template?", "description": "Download our CSV template with the correct format and sample data", "downloadButton": "Download Template"}, "formatRequirements": {"title": "CSV Format Requirements:", "requiredColumns": "Required columns:", "optionalColumns": "Optional columns:", "validRoles": "Valid roles:", "note": "Note:", "customRoleNote": "CUSTOM role will use organization's default custom role settings"}, "customRoles": {"title": "Available Custom Roles:", "usage": "Use \"CUSTOM\" in the role column to assign users to custom roles.", "loading": "Loading custom roles..."}}}, "dialog": {"title": "Bulk Import Group Members", "description": "Add multiple users to groups at once using a CSV file", "triggerButton": "Bulk Import", "steps": {"upload": "Upload", "preview": "Preview", "import": "Import"}, "buttons": {"cancel": "Cancel", "back": "Back", "import": "Import {count} Assignments", "importing": "Importing...", "importMore": "Import More", "done": "Done"}, "messages": {"successfullyAdded": "Successfully added {count} user{plural} to groups", "importFailed": "Import failed. Please try again."}}, "upload": {"title": "Upload CSV File", "description": "Drag and drop your CSV file here, or click to browse", "chooseFile": "Choose CSV File", "supportedFormat": "Supported format: CSV files only", "template": {"title": "Need a template?", "description": "Download our CSV template with the correct format and sample data", "downloadButton": "Download Template"}, "formatRequirements": {"title": "CSV Format Requirements:", "requiredColumns": "Required columns:", "groupIdentifier": "Group identifier:", "groupIdentifierValue": "groupName OR groupId (at least one required)", "note": "Note:", "noteValue": "Users must already be members of the organization to be added to groups"}, "availableGroups": {"title": "Available Groups:", "usage": "Use either the group name or ID in your CSV file."}, "loadingGroups": "Loading groups..."}, "preview": {"stats": {"validAssignments": "Valid Assignments", "warnings": "Warnings", "errors": "Errors"}, "alerts": {"errorsFound": "Found {count} error{plural} that must be fixed:", "warningsFound": "Found {count} warning{plural}:", "moreErrors": "... and {count} more errors", "moreWarnings": "... and {count} more warnings", "allValid": "All {count} group assignment{plural} are valid and ready to import!", "row": "Row"}, "table": {"title": "Group Assignment Preview ({count} assignments)", "headers": {"email": "Email", "groupName": "Group Name", "groupId": "Group ID"}, "usingGroupId": "Using Group ID", "showingFirst": "Showing first 10 of {count} assignments"}, "noData": {"title": "No Valid Data Found", "description": "Please check your CSV file format and try again."}}}, "subscription": {"noActiveSubscription": "No Active Subscription", "noSubscriptionMessage": "You don't have an active subscription. You won't be able to create workspaces or invite members.", "subscribePlan": "Subscribe to a Plan", "workspaceCreationRestricted": "You need an active subscription to create workspaces", "memberInvitationRestricted": "You need an active subscription to invite members", "vectorStorageWarning": "Warning: This upload will exceed your vector database storage limit by approximately {willExceedBy} GB.", "vectorStorageUsage": "Current usage: {currentUsageGB} GB of {limitGB} GB.", "vectorStorageContinue": "Do you want to continue with the upload?", "userLimitReached": "User Limit Reached", "userLimitMessage": "You have reached your plan's user limit ({currentCount}/{limit}). Please upgrade your plan or remove existing members before inviting new ones."}, "api": {"errors": {"failedToUploadFile": "Failed to upload file", "failedToUpdateFile": "Failed to update file", "failedToDeleteFile": "Failed to delete file", "fileNotFound": "File not found", "unauthorized": "Unauthorized - userId is required", "fileIdRequired": "File ID is required", "fileIdAndNameRequired": "File ID and name are required"}}, "privacyPolicy": {"title": "Privacy Policy", "scope": "<PERSON><PERSON>", "scopeContent": "This Privacy Policy applies to the SaaS platform Swiss Knowledge Hub and all related web and API services.", "preamble": "Preamble", "preambleContent": "Swiss Knowledge Hub GmbH (\"we\", \"us\") processes personal data in accordance with the Swiss Federal Act on Data Protection (FADP) and its implementing ordinance.", "controller": "Controller", "controllerContent": "Swiss Knowledge Hub GmbH\nKönizstrasse 161\n3097 Liebefeld, Switzerland\nUID CHE-219.860.\n<EMAIL> | +41 31 318 33 55\nRepresented by <PERSON>, Member of the Board", "purposes": "Purposes of Processing", "purposesContent1": "1. Provision and operation of the platform", "purposesContent2": "2. Management of user accounts and access rights", "purposesContent3": "3. Communication and support", "purposesContent4": "4. Payment processing and subscription management", "purposesContent5": "5. System security, abuse prevention, log analysis", "purposesContent6": "6. Compliance with legal obligations and the establishment, exercise or defence of legal claims", "categories": "Categories of Data", "categoriesContent": "- Master data such as name, business email, company affiliation\n- Authentication data such as hashed passwords, JWT tokens, session IDs\n- Content data such as uploaded or synchronised documents and messages\n- Payment data such as transaction and subscription identifiers\n- Usage and log data such as IP address, timestamps, API calls", "legalBases": "Legal Bases", "legalBasesContent": "- Contract performance or steps prior to entering into a contract\n- Legitimate interests in secure and efficient operation\n- Consent, which can be withdrawn at any time with future effect", "retention": "Retention", "retentionContent": "Data is erased or anonymised once it is no longer required for the stated purposes. Account and content data are removed no later than twelve months after contract termination unless statutory retention duties require longer storage.", "recipients": "Recipients and Processors", "recipientsContent1": "- **Microsoft Azure Switzerland North** – full hosting of application, database, storage and email.", "recipientsContent2": "- **Stripe Payments Europe Ltd. (Ireland) / Stripe Inc. (USA)** – payment processing.", "recipientsContent3": "- **No** additional external providers outside Switzerland other than specified in section 7b. All processors are contractually bound to confidentiality.", "ai": "Use of Artificial Intelligence", "aiContent1": "We employ machine-learning algorithms (LangChain-based retrieval-augmented generation) for semantic search and document analysis.", "aiContent2": "- Processing is confined to Azure data centres in Switzerland.", "aiContent3": "- No automated decisions producing legal or similarly significant effects are made.", "aiContent4": "- Vector embeddings are pseudonymised and stored in encrypted form.", "thirdParty": "Third-Party Integrations (SharePoint, Google Drive)", "thirdPartyContent": "If a customer activates the optional Microsoft SharePoint or Google Drive connector, selected files are synchronised directly via the respective provider. These providers process data under their own privacy terms and may do so in countries outside Switzerland. Configuration of the integrations is the customer's responsibility.", "transfers": "Cross-Border Transfers", "transfersContent": "The platform is operated in Switzerland. Payment-related data are transmitted to Stripe, which may process them in the United States. Transfers rely on the EU Standard Contractual Clauses (SCC 2021) pursuant to Art. 16 FADP.", "security": "Data Security", "securityContent": "- End-to-end TLS encryption for all transmissions\n- Encryption of sensitive database fields\n- Password storage using bcrypt\n- Role-based access control\n- Logging of security-relevant events and regular audits", "rights": "Data Subject Rights", "rightsContent": "Individuals have the right to access, rectification, erasure, restriction of processing, data portability, withdrawal of consent and to lodge a complaint with the Swiss Federal Data Protection and Information Commissioner. Requests must be sent to the address in section 2; proof of identity may be required.", "cookies": "Cookies and Similar Technologies", "cookiesContent1": "We use only essential cookies.", "cookiesContent2": "- Session cookie: HTTP-Only, Secure, SameSite Lax, expires after a maximum of 24 h\n- Language preference cookie: expires with the session\n- Subscription status cookie: expires after 30 days", "cookiesContent5": "No marketing or tracking cookies are deployed.", "minors": "Protection of Minors", "minorsContent": "The platform is intended solely for users aged 16 years or older.", "changes": "Changes to this Policy", "changesContent": "We may amend this policy at any time. The version published on the platform is authoritative. Users will be notified of material changes inside the application or by email.", "disclaimer": "Disclaimer", "disclaimerContent": "Information is reviewed regularly yet may change over time. Liability is excluded to the extent permitted by law.", "contact": "Contact", "contactContent": "<EMAIL> | +41 31 318 33 55\nSwiss Knowledge Hub GmbH, Könizstrasse 161, 3097 Liebefeld, Switzerland", "version": "Version 1.1, April 2025", "overview": "Overview", "detailedPolicy": "Detailed Policy", "dataCollection": "Data Collection", "dataCollectionDesc": "We collect only essential data needed to provide our services, including account information, content data, and usage logs.", "dataSecurity": "Data Security", "dataSecurityDesc": "We employ end-to-end encryption, secure password storage, and role-based access control to protect your data.", "dataProcessing": "Data Processing", "dataProcessingDesc": "All data is processed in Switzerland with limited third-party processors bound by confidentiality agreements.", "cookiesTitle": "Cookies", "cookiesDesc": "We use only essential cookies for session management, language preferences, and subscription status.", "keyPoints": "Key Points", "keyPoint1": "All data is hosted in Microsoft Azure Switzerland North data centers.", "keyPoint2": "Payment processing is handled by Stripe, which may process data in the US.", "keyPoint3": "You have rights to access, rectify, erase, and restrict processing of your data.", "keyPoint4": "The platform is intended for users aged 16 years or older."}, "sharedThreads": {"title": "Shared Threads", "privateShared": "Private Shared", "publicShared": "Public Shared", "all": "All", "private": "Private", "public": "Public", "unread": "Unread", "search": "Search Threads...", "filter": "Filter", "refresh": "Refresh", "noThreadsFound": "No shared threads found", "noThreadsMessage": "Threads shared appear here", "loadingThreads": "Loading Threads...", "comments": "Comments", "mentions": "Mentions", "commentCount": "{count} Comment", "commentsCount": "{count} Comments", "mentionCount": "{count} Mention", "mentionsCount": "{count} Mentions", "newActivity": "New Activity", "sharedBy": "Shared by {name}", "expiresAt": "Expires at {date}", "expired": "Expired", "allThreads": "All Threads", "unreadOnly": "Unread Only", "noThreadsMatchSearch": "No threads match your search criteria", "noUnreadThreads": "No unread threads at the moment", "noPrivateThreads": "No private shared threads available", "noPublicThreads": "No public shared threads available", "noSharedThreads": "No shared threads available", "messages": "messages", "clearSearch": "Clear Search"}, "accessibility": {"dragStart": "Started dragging chat: {chatTitle}", "dragOver": "Dragging {chatTitle} over {groupName}", "dragEnd": "Moved {chatTitle} to {groupName}", "dragCancel": "Drag operation cancelled", "dropZone": "Drop zone for {groupName}", "draggableChat": "Draggable chat item: {chatTitle}. Press space to start dragging."}, "globalSearch": {"title": "Global Search", "titleShort": "Search", "placeholder": "Search files, pages, chats...", "noResults": "No results found for \"{searchTerm}\"", "startTyping": "Start typing to search..."}, "mcpServers": {"title": "MCP Servers", "description": "Configure Model Context Protocol servers for on-demand external integrations", "refresh": "Refresh", "addServer": "Add Server", "searchPlaceholder": "Search servers by name, description, or command...", "allStatus": "All Status", "active": "Active", "inactive": "Inactive", "error": "Error", "testing": "Testing", "showingResults": "Showing {showing} of {total} servers", "searchLabel": "Search", "statusLabel": "Status", "errorMessage": "Error", "noServersConfigured": "No MCP Servers Configured", "noServersDescription": "Configure your first MCP server to enable on-demand external integrations. Servers will start automatically when needed!", "public": "Public", "private": "Private", "viewTools": "View Tools", "totalServers": "Total Servers", "ready": "Ready", "configured": "Configured", "errors": "Errors", "timeout": "Timeout", "autoRestart": "Auto Restart", "arguments": "Arguments", "envVariables": "Env Variables", "deleteServerTitle": "Delete MCP Server", "deleteServerDescription": "Are you sure you want to delete \"{name}\"? This action cannot be undone. The server will be stopped and all associated chat sessions will be removed.", "testConnection": "Test Connection", "statusReady": "Ready", "statusConfigured": "Configured", "statusError": "Error", "statusTesting": "Testing", "statusUnknown": "Unknown", "serverDescriptions": {"githubMcp": "GitHub MCP Server for repository management and code analysis"}, "statusReadyDescription": "Server configured and ready for on-demand use", "statusConfiguredDescription": "Server configured but not currently running", "statusErrorDescription": "Server encountered an error", "statusTestingDescription": "Server connectivity is being tested", "statusUnknownDescription": "Server status is unknown", "editServer": "Edit MCP Server", "addNewServer": "Add New MCP Server", "serverName": "Server Name", "serverNameRequired": "Server name is required", "nameMinLength": "Name must be at least 2 characters", "serverNamePlaceholder": "e.g., GitHub MCP Server for repository management and code analysis", "serverDescription": "Description", "descriptionPlaceholder": "Optional description of what this MCP server provides", "serverType": "Server Type", "selectServerType": "Select server type", "stdioType": "STDIO (Command-line)", "httpType": "HTTP (Web API)", "form": {"addServer": "Add Server", "editServer": "Edit Server", "name": "Name", "namePlaceholder": "Enter server name", "nameRequired": "Name is required", "nameMinLength": "Name must be at least 2 characters", "description": "Description", "descriptionPlaceholder": "Enter server description (optional)", "serverType": "Server Type", "selectServerType": "Select server type", "stdioType": "STDIO (Command-line)", "httpType": "HTTP (Web API)", "stdioDescription": "Command-line based MCP server", "httpDescription": "Web API based MCP server with SSE support", "commandRequired": "Command is required for STDIO servers", "npxNotSupported": "NPX-based servers are not supported in this environment. Please use direct Python scripts or other executable commands.", "npxWarning": "NPX commands are not supported. Use Python scripts or direct executables.", "argumentPlaceholder": "Argument {number}", "urlRequired": "URL is required for HTTP servers", "invalidUrl": "Please enter a valid HTTP or HTTPS URL", "httpHeaders": "HTTP Headers", "addHeader": "<PERSON>d <PERSON>", "httpHeadersDescription": "Add authentication headers like Authorization: Bearer token", "headerNamePlaceholder": "Header name (e.g., Authorization)", "headerValuePlaceholder": "Header value (e.g., Bearer token)", "serverConfiguration": "Server Configuration", "timeoutRequired": "Timeout is required", "timeoutMinError": "Timeout must be at least 1000ms", "timeoutMaxError": "Timeout cannot exceed 300000ms", "timeoutDescription": "How long to wait for server responses (recommended: 30000ms)", "command": "Command", "commandPlaceholder": "e.g. python server.py", "url": "URL", "urlPlaceholder": "e.g. http://localhost:8000", "timeout": "Timeout (seconds)", "timeoutPlaceholder": "30", "autoRestart": "Auto Restart", "autoRestartDescription": "Automatically restart server if it crashes", "isPublic": "Public", "isPublicDescription": "Make server available to all users", "arguments": "Arguments", "envVariables": "Environment Variables", "addArgument": "Add Argument", "addEnvVariable": "Add Environment Variable", "keyPlaceholder": "Variable name", "valuePlaceholder": "Variable value", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "creating": "Creating...", "updating": "Updating...", "httpOnlySupported": "Currently, only HTTP Web API servers are supported", "httpOnlyMessage": "This version supports HTTP Web API servers only."}, "availableTools": "Available Tools", "serverConnected": "Server Connected", "toolsAvailable": "{count} tools available", "loadingTools": "Loading tools...", "tool": "Tool", "noInputSchema": "No input schema available", "noParametersRequired": "No parameters required", "statusIndicator": {"connecting": "Connecting to MCP server...", "healthCheck": "Checking server health...", "reconnecting": "Reconnecting to MCP server...", "reconnectionFailed": "Reconnection failed", "toolCall": "Calling tool: {{toolName}}", "toolCallGeneric": "Executing tool...", "processing": "Processing response...", "completed": "MCP operation completed", "error": "MCP operation failed", "unknown": "MCP status unknown", "processingLabel": "Processing...", "showErrorGuide": "Show Error Guide", "hideErrorGuide": "Hide Error Guide"}}, "image": {"picture": "Picture", "supportedFormats": "We support PNG's / JPEG's under 10MB", "uploadImage": "Upload Image"}, "notifications": {"title": "Notifications", "connectedTitle": "Real-time notifications connected", "disconnectedTitle": "Real-time notifications disconnected", "newCount": "{count} new", "live": "Live", "offline": "Offline", "markAllRead": "<PERSON> all read", "loading": "Loading notifications...", "noNotifications": "No notifications yet", "noNotificationsDescription": "You'll see mentions and updates here"}, "urlImport": {"robotsBlocked": "🚫 Import blocked by robots.txt", "robotsBlockedMessage": "Import blocked: This website's robots.txt file prevents automated access to this URL.", "robotsBlockedTitle": "Access Blocked by robots.txt"}, "deepResearch": {"title": "Deep Research", "researchComplete": "Research Complete", "description": "Analyzing and gathering comprehensive information", "step": "Step {step}", "progress": "Progress", "areasForFurtherResearch": "Areas for Further Research", "researchPlan": "Research Plan", "topic": "Topic", "subtopics": "Subtopics", "questions": "Questions", "identified": "{count} identified", "planned": "{count} planned", "researchSummary": "Research Summary", "iterations": "Iterations", "sources": "Sources", "confidence": "Confidence", "quality": "Quality", "researchSteps": "Research Steps ({count})", "hide": "<PERSON>de", "show": "Show", "areasToExplore": "Areas to explore"}, "chatInput": {"internal": "Internal", "web": "Web", "mcp": "MCP", "deepResearch": "Deep Research"}, "sharedThread": {"threadNotFound": "Thread Not Found", "threadNotFoundDescription": "This shared thread does not exist or has been removed.", "linkExpired": "Link Expired", "linkExpiredDescription": "This shared thread link has expired.", "accessDenied": "Access Denied", "accessDeniedDescription": "You don't have permission to view this shared thread.", "signInToAccess": "Sign In to Access", "errorLoadingThread": "Error Loading Thread", "errorLoadingThreadDescription": "There was an error loading this shared thread. Please try again later.", "title": "Shared Thread", "public": "Public", "private": "Private", "createdBy": "Created by {name}", "copied": "Copied!", "copyLink": "Copy Link", "expires": "Expires {date}", "wantToComment": "Want to comment?", "signIn": "Sign In"}, "commentSidebar": {"title": "Comments", "loadingComments": "Loading comments...", "noCommentsYet": "No comments yet. Be the first to comment!", "showResolved": "Show resolved ({count})", "hideResolved": "Hide resolved ({count})", "replyingTo": "Replying to {name}", "addCommentPlaceholder": "Add a comment... (use @ to mention users)", "editCommentPlaceholder": "Edit your comment...", "pressToSend": "Press Cmd+Enter to send", "mentionedUser": "Mentioned {name}", "sending": "Sending...", "resolved": "Resolved", "reply": "Reply", "resolve": "Resolve", "reopen": "Reopen", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "commentAdded": "Comment added", "commentResolved": "Comment resolved", "commentReopened": "Comment reopened", "commentUpdated": "Comment updated", "commentDeleted": "Comment deleted", "failedToLoadComments": "Failed to load comments", "failedToAddComment": "Failed to add comment", "failedToUpdateComment": "Failed to update comment", "failedToDeleteComment": "Failed to delete comment", "comment": "comment"}, "audioProcessing": {"title": "Audio Processing Status", "pending": "Queued for processing", "processing": "Processing audio...", "completed": "Processing complete", "failed": "Processing failed", "unknown": "Status unknown", "filesProcessed": "Processed {count} file(s)"}}