/**
 * Custom hook for managing CopilotKit chat functionality
 *
 * Provides state management and utilities for the CopilotKit agentic RAG system
 */

import { useState, useCallback, useRef, useEffect } from "react";
import {
  copilotKitChatService,
  CopilotKitChatQuery,
  CopilotKitStreamChunk,
} from "@/services/copilotkit-chat";
import toast from "react-hot-toast";

interface CopilotKitMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: Date;
  sources?: Array<{
    type: string;
    title: string;
    content: string;
    metadata: Record<string, any>;
  }>;
  tools_used?: string[];
  elapsed_time?: number;
  thinking?: string;
  images?: string[];
  isStreaming?: boolean;
  metadata?: {
    includeWebResults?: boolean;
    searchMode?: string;
    hasImages?: boolean;
  };
}

interface UseCopilotKitChatOptions {
  userId: string;
  tenantId: string;
  userName: string;
  onNewMessage?: (message: CopilotKitMessage) => void;
  onError?: (error: Error) => void;
}

export function useCopilotKitChat({
  userId,
  tenantId,
  userName,
  onNewMessage,
  onError,
}: UseCopilotKitChatOptions) {
  const [messages, setMessages] = useState<CopilotKitMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastResponseTime, setLastResponseTime] = useState<number | null>(null);
  const [toolsUsed, setToolsUsed] = useState<string[]>([]);
  const [currentStreamingMessage, setCurrentStreamingMessage] =
    useState<CopilotKitMessage | null>(null);

  const abortControllerRef = useRef<AbortController | null>(null);

  // Add a new message
  const addMessage = useCallback(
    (message: CopilotKitMessage) => {
      setMessages((prev) => [...prev, message]);
      onNewMessage?.(message);
    },
    [onNewMessage]
  );

  // Update the last message
  const updateLastMessage = useCallback(
    (updates: Partial<CopilotKitMessage>) => {
      setMessages((prev) =>
        prev.map((msg, idx) =>
          idx === prev.length - 1 ? { ...msg, ...updates } : msg
        )
      );
    },
    []
  );

  // Send a message with streaming
  const sendMessage = useCallback(
    async (
      content: string,
      options: {
        searchMode?: "internal" | "web" | "hybrid";
        includeWebResults?: boolean;
        images?: string[];
      } = {}
    ) => {
      if (isLoading) return;

      // Create user message
      const userMessage: CopilotKitMessage = {
        id: `user_${Date.now()}`,
        role: "user",
        content,
        createdAt: new Date(),
        images: options.images,
        metadata: {
          includeWebResults: options.includeWebResults,
          searchMode: options.searchMode,
          hasImages: (options.images?.length || 0) > 0,
        },
      };

      addMessage(userMessage);
      setIsLoading(true);

      // Create assistant message placeholder
      const assistantMessage: CopilotKitMessage = {
        id: `assistant_${Date.now()}`,
        role: "assistant",
        content: "",
        createdAt: new Date(),
        sources: [],
        tools_used: [],
      };

      addMessage(assistantMessage);
      setCurrentStreamingMessage(assistantMessage);

      try {
        // Abort any existing request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        // Determine optimal search mode
        const searchMode =
          options.searchMode === "hybrid"
            ? copilotKitChatService.getOptimalSearchMode(content)
            : options.searchMode || "hybrid";

        // Get conversation context for better continuity
        const getConversationContext = () => {
          // Get the last few messages for context (max 2 exchanges)
          const recentMessages = messages.slice(-4); // Last 4 messages (2 user + 2 assistant)

          if (recentMessages.length === 0) return undefined;

          // Format as conversation history
          const conversationHistory = recentMessages
            .map(
              (msg) =>
                `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`
            )
            .join("\n");

          return conversationHistory;
        };

        const previousMessage = getConversationContext();

        // Prepare query
        const query: CopilotKitChatQuery = {
          question: content,
          stream: true,
          search_mode: searchMode,
          include_web_results:
            options.includeWebResults ||
            copilotKitChatService.shouldUseWebSearch(content),
          images: options.images,
          previous_message: previousMessage,
        };

        // Stream the response
        let fullContent = "";
        let currentSources: any[] = [];
        let currentToolsUsed: string[] = [];
        let responseTime = 0;
        let thinkingContent = "";

        for await (const chunk of copilotKitChatService.streamChat(
          query,
          userId,
          tenantId,
          userName
        )) {
          if (abortControllerRef.current?.signal.aborted) {
            break;
          }

          if (chunk.error) {
            throw new Error(chunk.error);
          }

          // Update progress messages
          if (chunk.status && !chunk.done) {
            updateLastMessage({
              content: `🤖 ${chunk.message || chunk.status}...`,
              isStreaming: true,
            });
          }

          // Update tools used
          if (chunk.tools_used) {
            currentToolsUsed = chunk.tools_used;
            setToolsUsed(chunk.tools_used);
          }

          // Handle streaming content chunks (new real-time streaming)
          if (chunk.answer_chunk && chunk.partial_answer) {
            fullContent = chunk.partial_answer;
            updateLastMessage({
              content: fullContent,
              sources: chunk.sources || currentSources,
              tools_used: chunk.tools_used || currentToolsUsed,
              elapsed_time: responseTime,
              thinking: thinkingContent,
              isStreaming: true,
            });
          }

          // Update sources when available
          if (chunk.sources) {
            currentSources = chunk.sources;
          }

          // Handle final response
          if (chunk.done) {
            fullContent = chunk.answer || "";
            currentSources = chunk.sources || [];
            responseTime = chunk.elapsed_time || 0;
            thinkingContent = chunk.thinking || "";

            updateLastMessage({
              content: fullContent,
              sources: currentSources,
              tools_used: currentToolsUsed,
              elapsed_time: responseTime,
              thinking: thinkingContent,
              isStreaming: false,
            });

            setLastResponseTime(responseTime);

            // Show performance notification
            if (responseTime < 5) {
              toast.success(
                `⚡ Response in ${responseTime.toFixed(
                  1
                )}s using ${currentToolsUsed.join(", ")}`
              );
            }

            break;
          }
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";

        updateLastMessage({
          content: `❌ Error: ${errorMessage}`,
        });

        toast.error("Failed to get response from CopilotKit agent");
        onError?.(error instanceof Error ? error : new Error(errorMessage));
      } finally {
        setIsLoading(false);
        setCurrentStreamingMessage(null);
        abortControllerRef.current = null;
      }
    },
    [
      isLoading,
      userId,
      tenantId,
      userName,
      addMessage,
      updateLastMessage,
      onError,
    ]
  );

  // Send a non-streaming message
  const sendMessageSync = useCallback(
    async (
      content: string,
      options: {
        searchMode?: "internal" | "web" | "hybrid";
        includeWebResults?: boolean;
        images?: string[];
      } = {}
    ) => {
      if (isLoading) return null;

      setIsLoading(true);

      try {
        const searchMode =
          options.searchMode === "hybrid"
            ? copilotKitChatService.getOptimalSearchMode(content)
            : options.searchMode || "hybrid";

        // Get conversation context for better continuity
        const getConversationContext = () => {
          // Get the last few messages for context (max 2 exchanges)
          const recentMessages = messages.slice(-4); // Last 4 messages (2 user + 2 assistant)

          if (recentMessages.length === 0) return undefined;

          // Format as conversation history
          const conversationHistory = recentMessages
            .map(
              (msg) =>
                `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`
            )
            .join("\n");

          return conversationHistory;
        };

        const previousMessage = getConversationContext();

        const query: CopilotKitChatQuery = {
          question: content,
          stream: false,
          search_mode: searchMode,
          include_web_results:
            options.includeWebResults ||
            copilotKitChatService.shouldUseWebSearch(content),
          images: options.images,
          previous_message: previousMessage,
        };

        const response = await copilotKitChatService.chat(
          query,
          userId,
          tenantId,
          userName
        );

        // Create user message
        const userMessage: CopilotKitMessage = {
          id: `user_${Date.now()}`,
          role: "user",
          content,
          createdAt: new Date(),
          images: options.images,
          metadata: {
            includeWebResults: options.includeWebResults,
            searchMode: options.searchMode,
            hasImages: (options.images?.length || 0) > 0,
          },
        };

        // Create assistant message
        const assistantMessage: CopilotKitMessage = {
          id: `assistant_${Date.now()}`,
          role: "assistant",
          content: response.answer,
          createdAt: new Date(),
          sources: response.sources,
          tools_used: response.tools_used,
          elapsed_time: response.elapsed_time,
          thinking: response.thinking,
        };

        addMessage(userMessage);
        addMessage(assistantMessage);

        setLastResponseTime(response.elapsed_time);
        setToolsUsed(response.tools_used);

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        toast.error("Failed to get response from CopilotKit agent");
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [isLoading, userId, tenantId, userName, addMessage, onError]
  );

  // Stop current streaming
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
      setCurrentStreamingMessage(null);
    }
  }, []);

  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastResponseTime(null);
    setToolsUsed([]);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    isLoading,
    lastResponseTime,
    toolsUsed,
    currentStreamingMessage,
    sendMessage,
    sendMessageSync,
    stopStreaming,
    clearMessages,
    addMessage,
    updateLastMessage,
  };
}
