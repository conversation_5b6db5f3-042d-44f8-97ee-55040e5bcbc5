"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { FileText, Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { deleteChat, updateChat } from "@/services";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { getLocale } from "@/utils/date-locale";
import { getCookie } from "@/utils/cookies";

interface Message {
  content: string;
  role: "user" | "assistant";
  createdAt: string;
  updatedAt: string;
}

interface Chat {
  id: string;
  title: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}

interface ChatHistoryProps {
  chatHistory: Chat[];
}

export default function ChatHistory({ chatHistory }: ChatHistoryProps) {
  const router = useRouter();
  const { t, language } = useLanguage();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");

  const handleStartEditing = (chat: Chat) => {
    setEditingId(chat.id);
    setEditingTitle(chat.title);
  };

  const handleUpdateTitle = async (chatId: string) => {
    try {
      // Get workspaceSlug from cookies
      const workspaceSlug = getCookie("workspaceSlug") || null;

      const response = await updateChat(
        {
          id: chatId,
          title: editingTitle,
          workspaceSlug, // Add workspaceSlug to data object
        },
        workspaceSlug as any // Pass workspaceSlug as second parameter with proper type
      );
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chatHistory.renameSuccess"));
        router.refresh();
      }
    } catch (error) {
      toast.error(t("chatHistory.renameFailed"));
    } finally {
      setEditingId(null);
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    try {
      // Get userId and tenantId from cookies
      const userId = getCookie("userId") as any;
      const tenantId = getCookie("currentOrganizationId") as any;
      const workspaceSlug = getCookie("workspaceSlug") as any;

      const response = await deleteChat(
        chatId,
        userId,
        tenantId,
        workspaceSlug
      );
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chatHistory.deleteSuccess"));
        router.refresh();
      }
    } catch (error) {
      toast.error(t("chatHistory.deleteFailed"));
    }
  };

  if (!chatHistory || chatHistory.length === 0) {
    return (
      <div className="h-full px-4 sm:px-6 py-8 overflow-x-hidden">
        <h1 className="text-xl sm:text-2xl font-bold tracking-tight mb-6">
          {t("chatHistory.title")}
        </h1>
        <div className="text-center py-12">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <h2 className="text-lg font-semibold mb-2">
            {t("chatHistory.noHistoryYet")}
          </h2>
          <p className="text-muted-foreground mb-4">
            {t("chatHistory.startNewChatPrompt")}
          </p>
          <Button asChild>
            <Link href="/ask-ai">{t("chatHistory.startNewChat")}</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full px-4 sm:px-6 py-8 overflow-x-hidden">
      <h1 className="text-xl sm:text-2xl font-bold tracking-tight mb-6">
        {t("chatHistory.title")}
      </h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {chatHistory.map((chat) => (
          <div key={chat.id}>
            {editingId === chat.id ? (
              <Card className="p-4 hover:shadow-md transition-shadow h-full min-h-[200px] flex flex-col">
                <div className="flex-1">
                  <div className="flex flex-col gap-2 mb-4">
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="w-full px-2 py-1 border rounded text-sm"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleUpdateTitle(chat.id);
                        }
                      }}
                    />
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateTitle(chat.id)}
                        className="flex-1"
                      >
                        {t("common.save")}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingId(null)}
                        className="flex-1"
                      >
                        {t("common.cancel")}
                      </Button>
                    </div>
                  </div>
                  <p className="text-xs line-clamp-2 mb-2">
                    {chat?.messages?.[0]?.content}
                  </p>
                  <p className="text-xs text-gray-400">
                    {formatDistanceToNow(
                      new Date(
                        chat?.messages?.[0]?.updatedAt || chat?.updatedAt,
                      ),
                      {
                        addSuffix: true,
                        locale: getLocale(language),
                      },
                    )}
                  </p>
                </div>
                <div className="flex justify-end pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteChat(chat.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            ) : (
              <Link
                href={`/ask-ai/${chat.id}`}
                className="block hover:text-blue-600 h-full"
              >
                <Card className="p-4 hover:shadow-md transition-shadow h-full min-h-[200px] flex flex-col">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h2 className="text-base font-semibold line-clamp-1 flex-1 mr-2">
                        {chat.title}
                      </h2>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleStartEditing(chat);
                          }}
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDeleteChat(chat.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 line-clamp-3 mb-2">
                      {chat?.messages?.[0]?.content}
                    </p>
                    <p className="text-xs text-gray-400">
                      {formatDistanceToNow(
                        new Date(
                          chat?.messages?.[0]?.updatedAt || chat?.updatedAt,
                        ),
                        {
                          addSuffix: true,
                          locale: getLocale(language),
                        },
                      )}
                    </p>
                  </div>
                </Card>
              </Link>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
