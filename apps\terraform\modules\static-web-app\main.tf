resource "azurerm_application_insights" "app_insights" {
  name                = "${var.swa_name}-insights"
  location            = var.location
  resource_group_name = var.resource_group_name
  application_type    = "web"
}

resource "azurerm_static_web_app" "nextjs_swa" {
  name                = var.swa_name
  resource_group_name = var.resource_group_name
  location            = var.location
  sku_tier            = "Standard" # Hybrid plan requires Standard
  sku_size            = "Standard"

  # Enable hybrid support with an API backend
  identity {
    type = "SystemAssigned"
  }

  # Edge server configuration
  app_settings = {
    ENABLE_EDGE_SERVER                    = "true"
    EDGE_SERVER_REGION                    = "westeurope"
    APPLICATIONINSIGHTS_CONNECTION_STRING = azurerm_application_insights.app_insights.connection_string
    APPINSIGHTS_INSTRUMENTATIONKEY        = azurerm_application_insights.app_insights.instrumentation_key
  }
  lifecycle {
    ignore_changes = [app_settings]
  }
}


