{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env", ".env"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_CLARITY_ID", "NEXT_PUBLIC_GA_TRACKING_ID", "NEXTAUTH_SECRET", "NEXT_PUBLIC_API_BASE_URL", "NEXT_PUBLIC_MAIL_API_KEY", "NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING", "NEXT_PUBLIC_SEND_EMAIL_FROM", "APP_INSIGHTS_CONNECTION_STRING", "NEXT_ENCRYPTION_CLOAK_KEY", "NEXT_ENCRYPTION_CLOAK_KEYCHAIN", "NEXT_PUBLIC_GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "AZURE_STORAGE_CONTAINER_NAME", "NEXT_PUBLIC_API_URL", "NEXT_PUBLIC_DOCS_URL", "AZURE_STORAGE_CONNECTION_STRING", "NEXT_PUBLIC_DATADOG_ENABLED", "NEXT_PUBLIC_DATADOG_APPLICATION_ID", "NEXT_PUBLIC_DATADOG_CLIENT_TOKEN", "NEXT_PUBLIC_DATADOG_SITE", "NEXT_PUBLIC_APP_VERSION", "DATADOG_API_KEY", "DATADOG_ENABLED", "DD_ENV", "DD_SERVICE", "DD_VERSION", "DD_LOGS_INJECTION", "DD_TRACE_SAMPLE_RATE", "DD_PROFILING_ENABLED", "NEXT_PUBLIC_WS_URL"], "globalDotEnv": [".env", ".env.local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_OPTIONS"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}}}