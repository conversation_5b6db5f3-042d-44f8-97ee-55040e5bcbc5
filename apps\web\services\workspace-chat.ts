// If the import above doesn't work, you may need to adjust the path based on your project structure

import { addAuthHeaders } from "@/lib/api/auth-token";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

export const workspaceChatService = {
  async chat(query: any) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/chat?current_user=${query.userId}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in chat:", error);
      throw error;
    }
  },
  // Helper function to detect if file is video/audio based on document_path
  isVideoAudioFile(documentPath: string): boolean {
    const audioVideoExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac', '.mkv', '.flv', '.wmv', '.m4v', '.3gp'];
    return audioVideoExtensions.some(ext => documentPath.toLowerCase().includes(ext));
  },

  async uploadForIndexing(query: any, headers: HeadersInit | null = null) {
    try {
      // Check if this is a video/audio file
      const isVideoAudio = this.isVideoAudioFile(query.document_path || '');

      // Update file status to PROCESSING before starting vectorization
      try {
        const updateResponse = await fetch(`/api/files`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "x-user-id": query.userId,
            "x-tenant-id": query.tenantId,
          },
          body: JSON.stringify({
            id: query.file_id,
            vectorizationStatus: "PROCESSING",
          }),
        });

        if (!updateResponse.ok) {
          console.warn("Failed to update file status to PROCESSING");
        }
      } catch (updateError) {
        console.error("Error updating file status to PROCESSING:", updateError);
        // Continue with vectorization even if status update fails
      }

      // Get authentication headers
      if (!headers) {
        headers = await addAuthHeaders({
          "Content-Type": "application/json",
        });
      }

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/index?current_user=${query.userId}&tenant_id=${query.tenantId}&file_id=${query.file_id}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        // Update file status to FAILED if vectorization fails
        try {
          await fetch(`/api/files`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              "x-user-id": query.userId,
              "x-tenant-id": query.tenantId,
            },
            body: JSON.stringify({
              id: query.file_id,
              vectorizationStatus: "FAILED",
            }),
          });
        } catch (updateError) {
          console.error("Error updating file status to FAILED:", updateError);
        }

        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle completion status based on file type
      if (isVideoAudio) {
        // For video/audio files, keep status as PROCESSING
        // Azure Video Indexer will process in background and status will be updated via polling
        const responseData = await response.json();
        const jobId = responseData.job_id;

        if (jobId) {
          this.startVideoProcessingStatusLogging(jobId, query.file_id, query.userId, query.tenantId,);
        } else {
          console.error("No job ID received for video processing - status monitoring disabled");
        }
      } else {
        // For non-video/audio files, update status to COMPLETED immediately
        try {
          await fetch(`/api/files`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              "x-user-id": query.userId,
              "x-tenant-id": query.tenantId,
            },
            body: JSON.stringify({
              id: query.file_id,
              vectorizationStatus: "COMPLETED",
              vectorizedAt: new Date().toISOString(),
            }),
          });
        } catch (updateError) {
          console.error("Error updating file status to COMPLETED:", updateError);
        }
      }

      return await response.json();
    } catch (error) {
      console.error("Error in uploadForIndexing:", error);
      throw error;
    }
  },

  async checkAccess() {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/check-access`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in checkAccess:", error);
      throw error;
    }
  },
  async generateTitle(query: string, tenantId: string) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/generate-title?query=${query}&tenant_id=${tenantId}`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in generateTitle:", error);
      throw error;
    }
  },
  async deleteFile(query: {
    fileId: string;
    tenantId: string;
    workspaceSlug: string;
  }) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });
      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/delete-file?file_id=${query?.fileId}&tenant_id=${query?.tenantId}&workspaceSlug=${query?.workspaceSlug}`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in deleteFile:", error);
      throw error;
    }
  },

  async getVideoProcessingStatus(jobId: string) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/video-analysis/${jobId}/status`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in getVideoProcessingStatus:", error);
      throw error;
    }
  },

  startVideoProcessingStatusLogging(jobId: string, _fileId: string, userId: string, tenantId: string) {
    let checkCount = 0;
    const maxChecks = 20; // Monitor for 20 minutes

    const checkStatus = async () => {
      checkCount++;

      try {
        const status = await this.getVideoProcessingStatus(jobId);

        if (status.status === 'completed') {
          return; // Stop monitoring
        } else if (status.status === 'failed') {
          console.error(`Video processing failed for job ${jobId}`);
          return; // Stop monitoring
        }
        
        // Continue monitoring if still processing and haven't reached max checks
        if (checkCount < maxChecks) {
          setTimeout(checkStatus, 60000); // Check again in 1 minute
        }
      } catch (error) {
        console.error(`Error checking video processing status for job ${jobId}:`, error);

        if (error instanceof Error && error.message.includes('500')) {
          // Stop monitoring
          try {
            await fetch(`/api/files`, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                "x-user-id": userId,
                "x-tenant-id": tenantId,
              },
              body: JSON.stringify({
                id: _fileId,
                vectorizationStatus: "FAILED",
              }),
            });
          } catch (err) {
            console.error('Failed to update vectorizationStatus to FAILED:', err);
          }
          return;
        }

        // Continue monitoring even on error (might be temporary)
        if (checkCount < maxChecks) {
          setTimeout(checkStatus, 60000); // Check again in 1 minute
        }
      }
    };

    // Start first check after 30 seconds
    setTimeout(checkStatus, 30000);
  },
};
