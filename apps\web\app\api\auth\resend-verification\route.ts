import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";
import Mailer from "@/lib/email/send-email";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    // Find the user by email
    const user = await db.user.findUnique({
      where: { emailHash },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if email is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        { error: "Email is already verified" },
        { status: 400 }
      );
    }

    // Delete any existing verification tokens for this user
    await db.verificationToken.deleteMany({
      where: { identifier: email },
    });

    // Generate verification token
    const token = crypto.randomBytes(32).toString("hex");
    const expires = new Date();
    expires.setHours(expires.getHours() + 24); // Token expires in 24 hours

    // In development mode, use a simpler token for easier testing
    const finalToken =
      process.env.NODE_ENV === "development"
        ? `verify-${Math.floor(100000 + Math.random() * 900000)}`
        : token;

    // Store token in database
    await db.verificationToken.create({
      data: {
        identifier: email,
        token: finalToken,
        expires,
      },
    });

    if (process.env.NODE_ENV === "development") {
      console.log("Development verification token created:", finalToken);
    }

    // Build verification URL
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      (process.env.NODE_ENV === "development" ? "http://localhost:3000" : "");
    const verificationUrl = `${baseUrl}/verify-email/${finalToken}`;

    // Create email template (simplified for this example)
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Verify Your Email</h2>
        <p>Thank you for registering with Swiss Knowledge Hub. Please click the link below to verify your email address:</p>
        <p>
          <a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px;">
            Verify Email
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
      </div>
    `;

    // Send email (with fallback for development)
    try {
      await Mailer.send({
        from:
          process.env.NEXT_PUBLIC_SEND_EMAIL_FROM ||
          "<EMAIL>",
        to: email,
        subject: "Verify Your Swiss Knowledge Hub Email",
        html,
      });
    } catch (emailError) {
      // In development, log the error but don't fail
      console.error("Failed to send verification email:", emailError);

      if (process.env.NODE_ENV === "production") {
        return NextResponse.json(
          { error: "Failed to send verification email. Please try again." },
          { status: 500 }
        );
      }

      // In development, continue despite email error
      console.log("Continuing despite email error in development environment");
    }

    return NextResponse.json({
      message: "Verification email sent successfully",
    });
  } catch (error) {
    console.error("Resend verification error:", error);
    return NextResponse.json(
      { error: "Something went wrong. Please try again." },
      { status: 500 }
    );
  }
}
