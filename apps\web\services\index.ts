import axios from "axios";
export * from "./src/auth";
export * from "./src/tenant";
export * from "./src/workspace";
export * from "./src/pages";
export * from "./src/folders";
export * from "./src/files";
export * from "./src/embedded";
export * from "./src/vectordb";
export * from "./src/llm-settings";
export * from "./src/chat";
export * from "./src/chat-group";
export * from "./src/message";
export * from "./src/integration/google";
export * from "./src/integration/microsoft";
export * from "./src/user";
export * from "./src/users";
export * from "./src/plans";
export * from "./src/subscriptions";
export * from "./src/vector-store-usage";
export * from "./src/stripe";
export * from "./src/roles";
export * from "./src/group";
export * from "./src/notifications";

export const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`;

export const fetchJson = async (
  url: string,
  userId?: string | null,
  tenantId?: string | null,
  workspaceSlug?: string | null
) => {
  try {
    const headers: Record<string, string> = {
      cache: "no-store",
    };

    // Add userId, tenantId, and workspaceSlug to headers if provided
    if (userId) {
      headers["x-user-id"] = userId;
    }

    if (tenantId) {
      headers["x-tenant-id"] = tenantId;
    }

    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await axios.get(url, { headers });

    if (!response.data) {
      throw new Error("Network error: Unable to fetch data.");
    }

    return response.data;
  } catch (error) {
    console.log(`error fetching data in ${url}`, { error });
    return null;
  }
};
