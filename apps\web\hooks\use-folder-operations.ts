import { createFolder, deleteFolder, updateFolder } from "@/services";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";

interface FolderOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
  onFolderCreated?: (folder: any) => void; // Callback for when folder is created
  onFolderRenamed?: (folderId: string, newName: string) => void; // Callback for when folder is renamed
  onFolderDeleted?: (folderId: string) => void; // Callback for when folder is deleted
}

export const useFolderOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
  onFolderCreated,
  onFolderRenamed,
  onFolderDeleted,
}: FolderOperationsProps) => {
  const userId = getCookie("userId") || "";
  const handleCreateFolder = async (
    newFolderName: string,
    parentFolderId?: string
  ) => {
    if (newFolderName.trim()) {
      try {
        toast.loading("Creating folder...");
        setIsLoading(true);

        const parentIds = parentFolderId ? [parentFolderId] : [];

        const data = {
          name: newFolderName.trim(),
          workspaceSlug,
          tenantId,
          pageId,
          parentIds,
        };

        // Pass workspaceSlug as part of data object, it will be added to headers in the service
        const newFolder = await createFolder(data, tenantId, userId);
        toast.remove();
        setIsLoading(false);
        if (newFolder?.folder?.id) {
          toast.success(newFolder?.message ?? "Folder created successfully!");

          // Call the callback to update UI state instead of reloading
          if (onFolderCreated) {
            onFolderCreated(newFolder.folder);
          }

          return true;
        } else {
          toast.error(
            newFolder?.error ?? "Failed to create folder. Please try again."
          );
          return false;
        }
      } catch (error) {
        console.error("Error creating folder:", error);
        return false;
      }
    }
    toast.error("Folder name cannot be empty.");
    return false;
  };

  const handleRenameFolder = async (folderId: string, newName: string) => {
    if (!newName.trim()) {
      toast.error("Folder name cannot be empty.");
      return false;
    }
    setIsLoading(true);
    toast.loading("Renaming folder...");
    const updatedFolder = await updateFolder(
      {
        id: folderId,
        name: newName,
        tenantId,
        workspaceSlug, // Add workspaceSlug to be used in headers
      },
      tenantId,
      userId
    );
    toast.remove();
    setIsLoading(false);
    if (updatedFolder?.message) {
      toast.success(updatedFolder?.message ?? "Folder renamed successfully!");

      // Call the callback to update UI state instead of reloading
      if (onFolderRenamed) {
        onFolderRenamed(folderId, newName);
      }

      return true;
    } else {
      toast.error(
        updatedFolder?.error ?? "Failed to rename folder. Please try again."
      );
      return false;
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    toast.loading("Deleting folder...");
    setIsLoading(true);
    // Pass workspaceSlug as the fourth parameter to be used in headers
    const deletedFolder = await deleteFolder(
      folderId,
      tenantId,
      userId,
      workspaceSlug as any // Use type assertion to avoid type error
    );
    toast.remove();
    setIsLoading(false);
    if (deletedFolder?.message) {
      toast.success(deletedFolder?.message ?? "Folder deleted successfully!");

      // Call the callback to update UI state instead of reloading
      if (onFolderDeleted) {
        onFolderDeleted(folderId);
      }

      return true;
    } else {
      toast.error(
        deletedFolder?.error ?? "Failed to delete folder. Please try again."
      );
      return false;
    }
  };

  return {
    handleCreateFolder,
    handleRenameFolder,
    handleDeleteFolder,
  };
};
