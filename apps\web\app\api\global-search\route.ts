import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import axios from "axios";
import jwt from "jsonwebtoken";

const PYTHON_API_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query");
    const tenantId = searchParams.get("tenantId");
    const types = searchParams.get("types");
    const workspaceIds = searchParams.get("workspaceIds");
    const limit = searchParams.get("limit") || "20";
    const skip = searchParams.get("skip") || "0";

    if (!query) {
      return NextResponse.json(
        { error: "Query parameter is required" },
        { status: 400 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Prepare the request to Python API
    const params = new URLSearchParams({
      query,
      tenant_id: tenantId,
      user_id: session.userId,
      limit,
      skip,
    });

    if (types) {
      params.append("types", types);
    }

    if (workspaceIds) {
      params.append("workspace_ids", workspaceIds);
    }
    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    // Make request to Python API
    const response = await axios.get(
      `${PYTHON_API_URL}/api/v1/global-search/search?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        timeout: 30000, // 30 seconds timeout
      }
    );

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Error in global search API:", error);

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      return NextResponse.json(
        {
          error: error.response.data?.detail || "Search failed",
          status: error.response.status,
        },
        { status: error.response.status }
      );
    } else if (error.request) {
      // The request was made but no response was received
      return NextResponse.json(
        { error: "Search service unavailable" },
        { status: 503 }
      );
    } else {
      // Something happened in setting up the request that triggered an Error
      return NextResponse.json(
        { error: "Failed to perform search" },
        { status: 500 }
      );
    }
  }
}
