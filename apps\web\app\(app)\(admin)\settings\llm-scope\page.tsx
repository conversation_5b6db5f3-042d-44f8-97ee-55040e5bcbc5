import LLMScopeSettingsPage from "@/components/wrapper-screens/organization-settings/llm-scope/llm-scope-settings-page";
import { LLMScopeProvider } from "@/lib/llm-scope-context";
import { cookies } from "next/headers";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";

import crypto from "crypto";

// Server-side function to get LLM scope settings directly from database
async function getServerSideLLMScopeSettings(tenantId: string, userId: string) {
  try {
    // Check if user has access to this tenant using userId directly
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        tenant: true,
        user: {
          select: {
            id: true,
            email: true,
          },
        },
      },
    });

    if (!membership) {
      console.error("User does not have access to tenant:", {
        userId,
        tenantId,
      });
      return null;
    }

    // Get tenant with LLM scope settings
    const tenant = await db.tenant.findUnique({
      where: {
        id: tenantId,
      },
      select: {
        id: true,
        llmScope: true,
        updatedAt: true,
      },
    });

    if (!tenant) {
      console.error("Tenant not found:", tenantId);
      return null;
    }

    return {
      id: tenant.id,
      llmScope: tenant.llmScope || ["INTERNAL_ONLY"],
      updatedAt: tenant.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error("Error fetching LLM scope settings server-side:", error);
    return null;
  }
}

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";
  const session: any = await getServerSession(authOptions);

  if (!tenantId || !session?.user?.email || !session?.userId) {
    return redirect("/sign-in");
  }

  try {
    // Get user role for the current tenant
    const userRole = (session as any).memberships?.find(
      (membership: any) => membership.tenant.id === tenantId
    )?.role;

    // Get LLM scope settings using server-side database query with userId
    const llmScopeSettings = await getServerSideLLMScopeSettings(
      tenantId,
      session.userId
    );

    return (
      <LLMScopeProvider tenantId={tenantId} initialSettings={llmScopeSettings}>
        <LLMScopeSettingsPage
          settings={llmScopeSettings}
          tenantId={tenantId}
          userRole={userRole}
        />
      </LLMScopeProvider>
    );
  } catch (error) {
    console.error("Error loading LLM scope settings:", error);
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">
          Failed to load LLM scope settings
        </p>
      </div>
    );
  }
}
