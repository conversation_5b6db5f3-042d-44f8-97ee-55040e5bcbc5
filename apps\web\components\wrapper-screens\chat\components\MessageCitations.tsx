"use client";

import React from "react";
import { FileText, Globe } from "lucide-react";
import { Message, Source } from "../types";
import { Button } from "@/components/ui/button";

// Utility function to extract used citations from content
function extractUsedCitations(content: string): {
  documentCitations: number[];
  webCitations: number[];
} {
  const documentCitations = new Set<number>();
  const webCitations = new Set<number>();

  // Match all bracketed groups like [D1, D2], [W3], etc.
  const matches = content.match(/\[[^\]]+\]/g);

  if (matches) {
    matches.forEach((group) => {
      // Remove brackets and split by comma
      const items = group.slice(1, -1).split(",");

      items.forEach((item) => {
        const trimmed = item.trim();
        const match = trimmed.match(/^([DW])(\d+)$/);
        if (match) {
          const type = match[1];
          const num = parseInt(match[2], 10);
          if (type === "D") {
            documentCitations.add(num);
          } else if (type === "W") {
            webCitations.add(num);
          }
        }
      });
    });
  }

  return {
    documentCitations: Array.from(documentCitations).sort((a, b) => a - b),
    webCitations: Array.from(webCitations).sort((a, b) => a - b),
  };
}

interface MessageCitationsProps {
  displayedMessage: Message;
  index: number;
  toggleCitationAccordion?: (messageId: string) => void; // Made optional
  setSelectedSource: (source: Source) => void;
  setIsCitationModalOpen: (isOpen: boolean) => void;
}

export const MessageCitations: React.FC<MessageCitationsProps> = ({
  displayedMessage,
  setSelectedSource,
  setIsCitationModalOpen,
}) => {
  if (!displayedMessage.sources || displayedMessage.sources.length === 0) {
    return null;
  }

  // Extract used citations from the message content
  const usedCitations = extractUsedCitations(displayedMessage.content);

  // If no citations are found in content, show all sources (fallback behavior)
  const shouldShowAllSources =
    usedCitations.documentCitations.length === 0 &&
    usedCitations.webCitations.length === 0;

  // Separate all document and web sources
  const allDocumentSources = displayedMessage.sources.filter(
    (source) =>
      source.metadata?.source !== "web" &&
      source.metadata?.type !== "web_search"
  );

  const allWebSources = displayedMessage.sources.filter(
    (source) =>
      source.metadata?.source === "web" ||
      source.metadata?.type === "web_search"
  );

  // Filter sources to only include those that are actually cited
  // If no citations found, show all sources as fallback
  const documentSources = shouldShowAllSources
    ? allDocumentSources
    : usedCitations.documentCitations
        .map((citationNum) => allDocumentSources[citationNum - 1]) // Convert 1-based to 0-based index
        .filter((source) => source !== undefined);

  const webSources = shouldShowAllSources
    ? allWebSources
    : usedCitations.webCitations
        .map((citationNum) => allWebSources[citationNum - 1]) // Convert 1-based to 0-based index
        .filter((source) => source !== undefined);

  const hasDocumentSources = documentSources.length > 0;
  const hasWebSources = webSources.length > 0;

  return (
    <div className="mt-3">
      {/* Compact inline sources display */}
      <div className="space-y-2">
        {/* Document sources section */}
        {hasDocumentSources && (
          <div className="flex flex-wrap gap-2">
            {documentSources.map((source, idx) => {
              const name =
                source?.metadata?.file_name ??
                source?.metadata?.source?.split("/").pop() ??
                source?.metadata?.title;
              const fileName = decodeURI(name?.split("-").pop() ?? "Document");
              const relevanceScore =
                source?.metadata?.relevanceScore ||
                source?.metadata?.relevance_score ||
                0;

              return (
                <Button
                  key={`doc-${idx}`}
                  variant="outline"
                  size="sm"
                  className="h-8 px-3 py-1 rounded-full border border-blue-200 bg-blue-50 hover:bg-blue-100 dark:border-blue-700 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-all duration-200 group"
                  onClick={() => {
                    setSelectedSource(source);
                    setIsCitationModalOpen(true);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-4 h-4 bg-blue-600 text-white rounded-full text-xs font-semibold">
                      {idx + 1}
                    </div>
                    <FileText className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    <span className="text-xs font-medium text-blue-700 dark:text-blue-300 max-w-[150px] truncate">
                      {fileName}
                    </span>
                    {relevanceScore > 0 && (
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                        {Math.round(relevanceScore * 100)}%
                      </span>
                    )}
                  </div>
                </Button>
              );
            })}
          </div>
        )}

        {/* Web sources section */}
        {hasWebSources && (
          <div className="flex flex-wrap gap-2">
            {webSources.map((source, idx) => {
              const displayTitle = source.metadata?.title || "Web Result";
              const displayLink =
                source.metadata?.displayLink ||
                (source.metadata?.link
                  ? new URL(source.metadata.link).hostname
                  : "web");

              return (
                <Button
                  key={`web-${idx}`}
                  variant="outline"
                  size="sm"
                  className="h-8 px-3 py-1 rounded-full border border-green-200 bg-green-50 hover:bg-green-100 dark:border-green-700 dark:bg-green-900/20 dark:hover:bg-green-900/30 transition-all duration-200 group"
                  onClick={() => {
                    setSelectedSource(source);
                    setIsCitationModalOpen(true);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-4 h-4 bg-green-600 text-white rounded-full text-xs font-semibold">
                      {documentSources.length + idx + 1}
                    </div>
                    <Globe className="h-3 w-3 text-green-600 dark:text-green-400" />
                    <span className="text-xs text-green-500 dark:text-green-400 opacity-75">
                      {displayLink}
                    </span>
                  </div>
                </Button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
