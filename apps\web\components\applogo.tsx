import React, { useState, useEffect } from "react";
import { useTheme } from "next-themes";

export const AppLogo = () => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering theme-dependent content after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR and before hydration, always render the light theme version
  if (!mounted) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 46 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          data-figma-bg-blur-radius="40"
          d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z"
          fill="#2D3691"
        />
        <path
          d="M41.5772 30.2777C43.0332 30.9947 43.1189 31.777 41.4059 32.6244L22.9914 41.0336C21.6484 41.6085 21.3748 41.7452 19.9937 41.132L0.808388 32.3637C0.123203 32.1029 -0.476336 31.3207 0.551441 30.734L5.77603 28.3344L19.2229 34.7104C21.5354 35.7534 21.9636 35.2971 23.6766 34.7104L37.3804 28.3583L41.5772 30.2777Z"
          fill="#2D3691"
          fillOpacity="0.75"
        />
        <path
          d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
          fill="#2D3691"
          fillOpacity="0.3"
        />
        <path
          d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
          fill="#2D3691"
        />
        <path
          d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
          fill="#2D3691"
        />
      </svg>
    );
  }

  const isDarkTheme = resolvedTheme === "dark";
  if (isDarkTheme) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 46 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <foreignObject
          x="-40"
          y="-28.6348"
          width="122.68"
          height="99.8832"
        ></foreignObject>
        <path
          data-figma-bg-blur-radius="40"
          d="M20.422 11.6952L0.551441 20.6512C-0.476336 21.2269 0.123203 21.9945 0.808388 22.2504L19.9937 30.8552C21.3748 31.4569 21.6484 31.3228 22.9914 30.7585L41.4059 22.5063C43.1189 21.6747 43.0332 20.907 41.5772 20.2034L22.4775 11.6313C21.7356 11.2541 21.283 11.2803 20.422 11.6952Z"
          fill="white"
        />
        <path
          d="M41.5772 30.2777C43.0332 30.9948 43.1189 31.777 41.4059 32.6245L22.9914 41.0336C21.6484 41.6086 21.3748 41.7453 19.9937 41.1321L0.808388 32.3637C0.123203 32.103 -0.476336 31.3207 0.551441 30.734L5.77603 28.3345L19.2229 34.7105C21.5354 35.7535 21.9636 35.2971 23.6766 34.7105L37.3804 28.3584L41.5772 30.2777Z"
          fill="white"
          fillOpacity="0.75"
        />
        <path
          d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
          fill="white"
          fillOpacity="0.3"
        />
        <path
          d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
          fill="white"
        />
        <path
          d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
          fill="white"
        />
        <defs>
          <clipPath
            id="bgblur_0_180_17_clip_path"
            transform="translate(40 28.6348)"
          >
            <path d="M20.422 11.6952L0.551441 20.6512C-0.476336 21.2269 0.123203 21.9945 0.808388 22.2504L19.9937 30.8552C21.3748 31.4569 21.6484 31.3228 22.9914 30.7585L41.4059 22.5063C43.1189 21.6747 43.0332 20.907 41.5772 20.2034L22.4775 11.6313C21.7356 11.2541 21.283 11.2803 20.422 11.6952Z" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 46 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        data-figma-bg-blur-radius="40"
        d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z"
        fill="#2D3691"
      />
      <path
        d="M41.5772 30.2777C43.0332 30.9947 43.1189 31.777 41.4059 32.6244L22.9914 41.0336C21.6484 41.6085 21.3748 41.7452 19.9937 41.132L0.808388 32.3637C0.123203 32.1029 -0.476336 31.3207 0.551441 30.734L5.77603 28.3344L19.2229 34.7104C21.5354 35.7534 21.9636 35.2971 23.6766 34.7104L37.3804 28.3583L41.5772 30.2777Z"
        fill="#2D3691"
        fillOpacity="0.75"
      />
      <path
        d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
        fill="#2D3691"
        fillOpacity="0.3"
      />
      <path
        d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
        fill="url(#paint0_linear_166_2)"
      />
      <path
        d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
        fill="url(#paint1_linear_166_2)"
      />
      <defs>
        <clipPath
          id="bgblur_0_166_2_clip_path"
          transform="translate(40 28.6348)"
        >
          <path d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z" />
        </clipPath>
        <linearGradient
          id="paint0_linear_166_2"
          x1="37.9743"
          y1="11.8575"
          x2="34.9451"
          y2="-0.691668"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#C4CAFF" />
          <stop offset="0.465" stop-color="#2D3691" stop-opacity="0.75" />
          <stop offset="1" stop-color="#2D3691" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_166_2"
          x1="43.4893"
          y1="14.3898"
          x2="42.0028"
          y2="8.74469"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#C4CAFF" />
          <stop offset="0.465" stop-color="#2D3691" stop-opacity="0.75" />
          <stop offset="1" stop-color="#2D3691" />
        </linearGradient>
      </defs>
    </svg>
  );
};
