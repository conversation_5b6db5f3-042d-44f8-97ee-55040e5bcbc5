resource "azurerm_service_plan" "app_plan" {
  name                = var.app_service_plan_name
  location            = var.location
  resource_group_name = var.resource_group_name
  os_type             = "Linux"

  sku_name = var.app_service_sku_name
}

resource "azurerm_application_insights" "app_insights" {
  name                = "${var.app_service_name}-insights"
  location            = var.location
  resource_group_name = var.resource_group_name
  application_type    = "web"
}

resource "azurerm_linux_web_app" "app" {
  name                = var.app_service_name
  location            = var.location
  resource_group_name = var.resource_group_name
  service_plan_id     = azurerm_service_plan.app_plan.id


  site_config {
    app_command_line = "uvicorn src.main:app --host 0.0.0.0 --port 8000"

    # Health check settings
    health_check_path                 = "/"
    health_check_eviction_time_in_min = 10
  }


  logs {
    detailed_error_messages = true
    failed_request_tracing  = true

    http_logs {
      file_system {
        retention_in_days = 30
        retention_in_mb   = 35
      }
    }
  }



  app_settings = {
    APPLICATIONINSIGHTS_CONNECTION_STRING = azurerm_application_insights.app_insights.connection_string
    APPINSIGHTS_INSTRUMENTATIONKEY        = azurerm_application_insights.app_insights.instrumentation_key
  }

  lifecycle {
    ignore_changes = [app_settings]
  }
}
