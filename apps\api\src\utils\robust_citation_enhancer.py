"""
Robust Citation Enhancer Module

This module provides a more sophisticated approach to enhancing citations with relevance scores
and relevant text. It uses advanced text matching and semantic similarity to ensure only truly
relevant documents are shown as citations.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
import math
from collections import Counter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RobustCitationEnhancer:
    """
    A more robust utility class to enhance citations with relevance scores and relevant text.
    """

    # Optimized threshold for minimum relevance - lowered for better semantic search
    MIN_RELEVANCE_THRESHOLD = 0.15  # Lowered from 0.32 to allow more semantic matches

    # Threshold for substantial relevance relative to top source
    SUBSTANTIAL_RELEVANCE_RATIO = 0.4  # Lowered from 0.6 to be more inclusive

    # Common stopwords to ignore in relevance calculations
    STOPWORDS = {
        'a', 'an', 'the', 'and', 'or', 'but', 'if', 'because', 'as', 'what',
        'which', 'this', 'that', 'these', 'those', 'then', 'just', 'so', 'than',
        'such', 'both', 'through', 'about', 'for', 'is', 'of', 'while', 'during',
        'to', 'from', 'in', 'on', 'by', 'with', 'at', 'into'
    }

    # Common greeting patterns
    GREETING_PATTERNS = [
        r'^hi\b',
        r'^hello\b',
        r'^hey\b',
        r'^greetings\b',
        r'^good\s+(morning|afternoon|evening)\b',
        r'^howdy\b',
        r'^what\'?s\s+up\b',
        r'^how\s+are\s+you\b',
        r'^how\'?s\s+it\s+going\b',
    ]

    @staticmethod
    def enhance_sources(sources: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Enhance sources with relevance scores and relevant text using a more robust approach.

        Args:
            sources: List of source dictionaries with content and metadata
            query: The original query used to retrieve the sources

        Returns:
            Enhanced list of sources with relevance scores and relevant text
        """
        # Handle None or empty query
        if not query:
            logger.info("Empty or None query provided. Filtering out all sources.")
            return []

        # Log the original query and number of sources
        logger.info(f"Enhancing sources for query: '{query}' with {len(sources)} sources")

        # Check if this is a simple greeting or very short query
        if RobustCitationEnhancer._is_simple_greeting(query):
            logger.info(f"Simple greeting detected: '{query}'. Filtering out all sources.")
            return []

        # Extract key terms from the query for better matching
        query_terms = RobustCitationEnhancer._extract_key_terms(query)
        logger.info(f"Extracted key terms: {query_terms}")

        if not query_terms:
            logger.info("No meaningful terms found in query. Filtering out all sources.")
            return []

        enhanced_sources = []

        # Process each source
        for idx, source in enumerate(sources):
            # Skip empty or very short content
            if not source.get("content") or len(source["content"]) < 50:
                logger.info(f"Skipping source {idx} due to insufficient content")
                continue

            # Create a copy of the source to avoid modifying the original
            enhanced_source = {
                "content": source["content"],
                "metadata": source["metadata"].copy() if source.get("metadata") else {}
            }

            # Calculate comprehensive relevance score (including metadata)
            relevance_score, term_matches = RobustCitationEnhancer._calculate_comprehensive_relevance(
                source["content"],
                query,
                query_terms,
                idx,
                source.get("metadata", {})
            )

            # Skip sources with very low relevance scores
            if relevance_score < RobustCitationEnhancer.MIN_RELEVANCE_THRESHOLD:
                logger.info(f"Skipping source {idx} with low relevance score: {relevance_score:.4f}")
                continue

            # Find the most relevant text segment based on term matches
            relevant_text = RobustCitationEnhancer._extract_best_text_segment(
                source["content"],
                query,
                term_matches
            )

            # Add enhancements to metadata
            enhanced_source["metadata"]["relevanceScore"] = relevance_score
            enhanced_source["metadata"]["relevantText"] = relevant_text
            enhanced_source["metadata"]["matchedTerms"] = list(term_matches.keys())

            # Perform content quality assessment
            quality_score = RobustCitationEnhancer._assess_content_quality(
                source["content"],
                query,
                relevant_text
            )

            # Apply quality threshold - more lenient for semantic search
            if quality_score < 0.15:  # Lowered threshold to allow more semantic matches
                logger.info(f"Skipping source {idx} with low content quality score: {quality_score:.4f}")
                continue

            # Add quality score to metadata
            enhanced_source["metadata"]["contentQuality"] = quality_score

            # Combine relevance and quality for final score
            combined_score = (relevance_score * 0.7) + (quality_score * 0.3)
            enhanced_source["metadata"]["combinedScore"] = combined_score

            enhanced_sources.append(enhanced_source)

        # Sort sources by combined score (highest first)
        enhanced_sources.sort(key=lambda s: s["metadata"]["combinedScore"], reverse=True)

        # Apply additional filtering to ensure substantial quality and relevance
        # Calculate the top combined score for relative filtering
        if enhanced_sources:
            top_combined_score = enhanced_sources[0]["metadata"]["combinedScore"] if enhanced_sources else 0
            top_relevance = enhanced_sources[0]["metadata"]["relevanceScore"] if enhanced_sources else 0

            # Filter out sources with combined scores significantly lower than the top source
            # Enhanced filtering using both relevance and quality metrics
            substantial_sources = [
                s for s in enhanced_sources
                if (s["metadata"]["relevanceScore"] >= RobustCitationEnhancer.MIN_RELEVANCE_THRESHOLD and
                    s["metadata"]["combinedScore"] >= max(
                        0.20,  # Lowered threshold for better semantic search coverage
                        top_combined_score * RobustCitationEnhancer.SUBSTANTIAL_RELEVANCE_RATIO
                    ))
            ]

            if len(substantial_sources) < len(enhanced_sources):
                logger.info(f"Filtered out {len(enhanced_sources) - len(substantial_sources)} sources with insufficient relative relevance")
                enhanced_sources = substantial_sources

        # Limit the number of sources based on query complexity
        max_sources = RobustCitationEnhancer._get_max_sources_for_query(query)
        if len(enhanced_sources) > max_sources:
            logger.info(f"Limiting sources from {len(enhanced_sources)} to {max_sources} based on query complexity")
            enhanced_sources = enhanced_sources[:max_sources]

        # Log the final number of sources after filtering
        logger.info(f"Final number of sources after filtering: {len(enhanced_sources)}")

        # If we have multiple sources, ensure they're sufficiently different from each other
        if len(enhanced_sources) > 1:
            unique_sources = [enhanced_sources[0]]  # Always include the top source

            for source in enhanced_sources[1:]:
                # Check if this source is sufficiently different from already included sources
                is_unique = True
                for included_source in unique_sources:
                    # Compare matched terms - if they're too similar, skip this source
                    source_terms = set(source["metadata"].get("matchedTerms", []))
                    included_terms = set(included_source["metadata"].get("matchedTerms", []))

                    # If 90% or more of the terms overlap, consider it redundant (was 80%)
                    if source_terms and included_terms:
                        overlap = len(source_terms.intersection(included_terms))
                        smaller_set_size = min(len(source_terms), len(included_terms))
                        # Only consider it redundant if there are multiple terms and high overlap
                        if smaller_set_size > 2 and overlap / smaller_set_size > 0.9:
                            is_unique = False
                            break

                if is_unique:
                    unique_sources.append(source)

            if len(unique_sources) < len(enhanced_sources):
                logger.info(f"Filtered out {len(enhanced_sources) - len(unique_sources)} redundant sources")
                enhanced_sources = unique_sources

        # Log quality metrics for monitoring
        if enhanced_sources:
            avg_relevance = sum(s["metadata"]["relevanceScore"] for s in enhanced_sources) / len(enhanced_sources)
            avg_quality = sum(s["metadata"]["contentQuality"] for s in enhanced_sources) / len(enhanced_sources)
            avg_combined = sum(s["metadata"]["combinedScore"] for s in enhanced_sources) / len(enhanced_sources)

            logger.info(f"Enhanced sources: {len(enhanced_sources)} sources passed quality filtering")
            logger.info(f"Quality metrics - Avg Relevance: {avg_relevance:.3f}, Avg Quality: {avg_quality:.3f}, Avg Combined: {avg_combined:.3f}")
        else:
            logger.info("No sources passed quality filtering")

        return enhanced_sources

    @staticmethod
    def enhance_web_sources(web_sources: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Apply the same quality filtering to web search results.

        Args:
            web_sources: List of web source dictionaries
            query: The original query

        Returns:
            Enhanced and filtered web sources
        """
        if not web_sources:
            return []

        # Handle None or empty query
        if not query:
            logger.info("Empty or None query provided for web sources. Filtering out all sources.")
            return []

        logger.info(f"Enhancing {len(web_sources)} web sources for query: '{query}'")

        # Check if this is a simple greeting
        if RobustCitationEnhancer._is_simple_greeting(query):
            logger.info(f"Simple greeting detected: '{query}'. Filtering out web sources.")
            return []

        query_terms = RobustCitationEnhancer._extract_key_terms(query)
        if not query_terms:
            logger.info("No meaningful query terms found. Filtering out web sources.")
            return []

        enhanced_web_sources = []

        for idx, source in enumerate(web_sources):
            # Web sources typically have shorter content (snippets)
            content = source.get("content", "")
            if not content or len(content) < 20:  # Lower threshold for web snippets
                logger.info(f"Skipping web source {idx} due to insufficient content")
                continue

            # Create enhanced source
            enhanced_source = {
                "content": content,
                "metadata": source["metadata"].copy() if source.get("metadata") else {}
            }

            # Calculate relevance score (adapted for shorter web content)
            relevance_score, term_matches = RobustCitationEnhancer._calculate_web_relevance(
                content, query, query_terms, idx
            )

            # Apply threshold for web sources - balanced for snippet content
            if relevance_score < 0.23:  # Balanced threshold for web content
                logger.info(f"Skipping web source {idx} with low relevance score: {relevance_score:.4f}")
                continue

            # Extract relevant text (entire snippet for web sources)
            relevant_text = content[:200] if len(content) > 200 else content

            # Assess content quality (adapted for web snippets)
            quality_score = RobustCitationEnhancer._assess_web_content_quality(
                content, query, relevant_text
            )

            if quality_score < 0.23:  # Balanced threshold for web content quality
                logger.info(f"Skipping web source {idx} with low content quality: {quality_score:.4f}")
                continue

            # Add enhancements to metadata
            enhanced_source["metadata"]["relevanceScore"] = relevance_score
            enhanced_source["metadata"]["relevantText"] = relevant_text
            enhanced_source["metadata"]["matchedTerms"] = list(term_matches.keys())
            enhanced_source["metadata"]["contentQuality"] = quality_score

            # Combined score for web sources
            combined_score = (relevance_score * 0.6) + (quality_score * 0.4)
            enhanced_source["metadata"]["combinedScore"] = combined_score

            enhanced_web_sources.append(enhanced_source)

        # Sort by combined score
        enhanced_web_sources.sort(key=lambda s: s["metadata"]["combinedScore"], reverse=True)

        # Limit web sources to avoid overwhelming users
        max_web_sources = min(3, len(enhanced_web_sources))  # Balanced limit for web sources
        enhanced_web_sources = enhanced_web_sources[:max_web_sources]

        logger.info(f"Enhanced web sources: {len(enhanced_web_sources)} sources passed quality filtering")
        return enhanced_web_sources

    @staticmethod
    def format_web_sources_without_filtering(web_sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format web sources exactly as returned from the web search API without any quality filtering.
        This preserves the original web search results for display as citations.

        Args:
            web_sources: List of web source dictionaries from the search API

        Returns:
            Formatted web sources with web indicator and original content preserved
        """
        if not web_sources:
            return []

        logger.info(f"Formatting {len(web_sources)} web sources without quality filtering")

        formatted_sources = []
        for idx, source in enumerate(web_sources):
            # Preserve the original source structure but ensure consistent format
            formatted_source = {
                "content": source.get("content", ""),
                "metadata": source.get("metadata", {}).copy()
            }

            # Ensure web source indicators are present
            if "source" not in formatted_source["metadata"]:
                formatted_source["metadata"]["source"] = "web"
            if "type" not in formatted_source["metadata"]:
                formatted_source["metadata"]["type"] = "web_search"

            # Add web indicator for UI display
            formatted_source["metadata"]["isWebSource"] = True
            formatted_source["metadata"]["webSourceIndex"] = idx + 1

            # Preserve original order and content
            formatted_source["metadata"]["originalOrder"] = idx

            formatted_sources.append(formatted_source)

        logger.info(f"Formatted {len(formatted_sources)} web sources without filtering")
        return formatted_sources

    @staticmethod
    def _is_simple_greeting(query: str) -> bool:
        """
        Check if the query is a simple greeting that doesn't need citations.
        Uses regex patterns for more accurate detection.
        """
        # Handle None or empty query
        if not query:
            return True

        query_lower = query.lower().strip()

        # Check if query is very short
        if len(query_lower) < 10:
            # Check against greeting patterns
            for pattern in RobustCitationEnhancer.GREETING_PATTERNS:
                if re.search(pattern, query_lower):
                    return True

            # Check if it's just 1-2 words
            if len(query_lower.split()) <= 2:
                return True

        return False

    @staticmethod
    def _extract_key_terms(query: str) -> List[str]:
        """
        Extract meaningful key terms from the query, filtering out stopwords.
        Improved to better identify important terms and phrases.
        """
        # Handle None or empty query
        if not query:
            return []

        # Normalize and tokenize
        query_lower = query.lower().strip()

        # First try to extract multi-word phrases (2-3 words)
        phrases = []
        words = query_lower.split()

        # Extract 2-word and 3-word phrases
        if len(words) >= 2:
            for i in range(len(words) - 1):
                phrase = f"{words[i]} {words[i+1]}"
                if not all(word in RobustCitationEnhancer.STOPWORDS for word in phrase.split()):
                    phrases.append(phrase)

        if len(words) >= 3:
            for i in range(len(words) - 2):
                phrase = f"{words[i]} {words[i+1]} {words[i+2]}"
                if not all(word in RobustCitationEnhancer.STOPWORDS for word in phrase.split()):
                    phrases.append(phrase)

        # Extract single words
        single_words = re.findall(r'\b\w+\b', query_lower)

        # Filter out stopwords but include shorter words (minimum 2 chars)
        key_terms = [word for word in single_words if word not in RobustCitationEnhancer.STOPWORDS and len(word) >= 2]

        # Combine phrases and single words, with phrases first (they're more specific)
        combined_terms = phrases + key_terms

        # Remove duplicates while preserving order
        seen = set()
        unique_terms = []
        for term in combined_terms:
            if term not in seen:
                seen.add(term)
                unique_terms.append(term)

        return unique_terms

    @staticmethod
    def _calculate_comprehensive_relevance(
        content: str,
        _query: str,  # Prefixed with underscore to indicate it's not used
        query_terms: List[str],
        position: int = 0,
        metadata: Dict[str, Any] = None
    ) -> Tuple[float, Dict[str, int]]:
        """
        Calculate a comprehensive relevance score using multiple factors.
        Improved to require substantial relevance and better evaluate term importance.
        Now includes metadata matching for better filename and source matching.

        Returns:
            Tuple of (relevance_score, term_matches_dict)
        """
        content_lower = content.lower()

        # Prepare metadata for searching (if available)
        metadata = metadata or {}
        metadata_text = ""
        if metadata:
            # Include filename, title, and other searchable metadata
            searchable_fields = ["filename", "title", "name", "source", "file_name"]
            metadata_parts = []
            for field in searchable_fields:
                if field in metadata and metadata[field]:
                    metadata_parts.append(str(metadata[field]).lower())
            metadata_text = " ".join(metadata_parts)

        # Count term occurrences with context awareness (content + metadata)
        term_matches = {}
        for term in query_terms:
            content_count = 0
            metadata_count = 0

            # For multi-word terms (phrases), do exact matching
            if ' ' in term:
                content_count = content_lower.count(term)
                if metadata_text:
                    metadata_count = metadata_text.count(term)

                total_count = content_count + (metadata_count * 2)  # Weight metadata matches higher
                if total_count > 0:
                    # Phrases are more important, so give them higher weight
                    term_matches[term] = total_count * 2
            else:
                # For single words, ensure they're actual words (not substrings)
                content_count = len(re.findall(r'\b' + re.escape(term) + r'\b', content_lower))
                if metadata_text:
                    metadata_count = len(re.findall(r'\b' + re.escape(term) + r'\b', metadata_text))

                total_count = content_count + (metadata_count * 2)  # Weight metadata matches higher
                if total_count > 0:
                    term_matches[term] = total_count

        # If no terms matched, return zero relevance
        if not term_matches:
            return 0.0, {}

        # Enhanced term matching requirements for better semantic search
        # More flexible requirements to allow semantic relevance
        if len(query_terms) >= 7:
            min_required_terms = max(1, len(query_terms) // 5)  # At least 1/5 of terms for complex queries
        elif len(query_terms) >= 4:
            min_required_terms = max(1, len(query_terms) // 4)  # At least 1/4 of terms for medium queries
        elif len(query_terms) >= 2:
            min_required_terms = max(1, len(query_terms) // 3)  # At least 1/3 of terms for short queries
        else:
            min_required_terms = 1  # Only require 1 match for very short queries

        if len(term_matches) < min_required_terms:
            # Multi-word phrase matches can compensate for fewer total matches
            multi_word_matches = sum(1 for term in term_matches.keys() if ' ' in term)
            # More lenient: allow documents with strong phrase matches or partial coverage
            if multi_word_matches >= 1 or len(term_matches) >= max(1, min_required_terms // 2):
                # At least one substantial phrase match or partial term coverage
                pass
            else:
                # Not enough meaningful term coverage
                return 0.0, {}

        # Calculate TF-IDF style score
        content_length = len(content)
        term_score = 0

        for term, count in term_matches.items():
            # Term frequency normalized by content length
            tf = count / (content_length / 100)
            # Inverse document frequency approximation (longer terms are more specific)
            idf = math.log(2 + len(term))
            # Multi-word terms get higher weight
            term_weight = 1.5 if ' ' in term else 1.0
            term_score += tf * idf * term_weight

        # Normalize term score to 0-1 range
        max_possible_score = len(query_terms) * 0.5  # Reasonable upper bound
        normalized_term_score = min(1.0, term_score / max_possible_score)

        # Calculate coverage score (what percentage of query terms are found)
        # Weight coverage more heavily for multi-word terms
        multi_word_terms = sum(1 for term in query_terms if ' ' in term)
        single_word_terms = len(query_terms) - multi_word_terms

        # Calculate weighted coverage
        if multi_word_terms > 0:
            multi_word_matches = sum(1 for term in term_matches if ' ' in term)
            multi_word_coverage = multi_word_matches / multi_word_terms if multi_word_terms > 0 else 0
        else:
            multi_word_coverage = 0

        if single_word_terms > 0:
            single_word_matches = sum(1 for term in term_matches if ' ' not in term)
            single_word_coverage = single_word_matches / single_word_terms if single_word_terms > 0 else 0
        else:
            single_word_coverage = 0

        # Weight multi-word coverage more heavily
        if multi_word_terms > 0 and single_word_terms > 0:
            coverage_score = (multi_word_coverage * 0.7) + (single_word_coverage * 0.3)
        else:
            coverage_score = len(term_matches) / len(query_terms)

        # Calculate term density (how concentrated the matches are)
        # Find the densest region of matches
        term_positions = []
        for term in term_matches:
            start = 0
            while True:
                pos = content_lower.find(term, start)
                if pos == -1:
                    break
                term_positions.append((pos, pos + len(term)))
                start = pos + 1

        term_positions.sort()

        # Calculate density score
        density_score = 0
        if term_positions:
            # Find the window with the most matches
            window_size = min(500, content_length)  # 500 char window
            best_count = 0

            for i in range(len(term_positions)):
                window_start = term_positions[i][0]
                window_end = window_start + window_size
                matches_in_window = sum(1 for pos in term_positions if window_start <= pos[0] < window_end)
                best_count = max(best_count, matches_in_window)

            density_score = best_count / len(term_positions)

        # Apply position penalty (earlier results are assumed more relevant)
        position_factor = max(0.8, 1.0 - (position * 0.03))

        # Add query focus factor - penalize sources that don't focus on the query topic
        query_focus = min(len(term_matches) / max(1, len(query_terms)), 1.0)

        # Combine factors with appropriate weights - emphasize relevance and focus
        final_score = (normalized_term_score * 0.35) + (coverage_score * 0.25) + (query_focus * 0.25) + (density_score * 0.1) + (position_factor * 0.05)

        return final_score, term_matches

    @staticmethod
    def _extract_best_text_segment(content: str, _query: str, term_matches: Dict[str, int]) -> str:
        """
        Extract the most relevant text segment based on term matches.
        Improved to better identify contextually relevant passages.
        """
        if not term_matches:
            return content[:300]  # Default to first 300 chars if no matches

        # Prepare for term matching
        content_lower = content.lower()

        # Weight terms by importance (multi-word terms and less common terms are more important)
        term_weights = {}
        for term in term_matches.keys():
            # Multi-word terms get higher weight
            weight = 2.0 if ' ' in term else 1.0
            # Longer terms get higher weight
            weight *= (1.0 + (len(term) / 20))
            term_weights[term] = weight

        # Find all occurrences of each term with their weights
        weighted_positions = []
        for term, weight in term_weights.items():
            start = 0
            while True:
                pos = content_lower.find(term, start)
                if pos == -1:
                    break
                weighted_positions.append((pos, pos + len(term), weight))
                start = pos + 1

        if not weighted_positions:
            return content[:300]

        # Sort positions
        weighted_positions.sort()

        # Find the best cluster of term positions using a sliding window approach
        window_size = min(600, len(content) // 2)  # Use a larger window for better context
        best_start = 0
        best_end = min(window_size, len(content))
        best_score = 0

        # Try different window sizes for better flexibility
        for size_factor in [0.8, 1.0, 1.2]:
            current_window_size = int(window_size * size_factor)

            # Slide the window through the content
            for i in range(0, len(content) - current_window_size + 1, 50):  # Step by 50 chars for efficiency
                window_start = i
                window_end = i + current_window_size

                # Calculate weighted score for this window
                window_score = 0
                unique_terms_in_window = set()

                for pos, end, weight in weighted_positions:
                    # Check if the term is completely within the window
                    if window_start <= pos and end <= window_end:
                        window_score += weight
                        # Find which term this position belongs to
                        for term in term_matches.keys():
                            if pos <= content_lower.find(term, max(0, pos-len(term)), min(len(content), pos+len(term))) < end:
                                unique_terms_in_window.add(term)
                                break

                # Bonus for windows with more unique terms
                diversity_bonus = len(unique_terms_in_window) / len(term_matches)
                window_score *= (1 + diversity_bonus)

                if window_score > best_score:
                    best_score = window_score
                    best_start = window_start
                    best_end = window_end

        # Expand to sentence boundaries if possible
        final_start = best_start
        final_end = best_end

        # Find sentence boundary before start
        for i in range(best_start - 1, max(0, best_start - 150), -1):
            if i < len(content) and content[i] in ['.', '!', '?', '\n'] and i + 1 < len(content) and content[i + 1].isspace():
                final_start = i + 2
                break

        # Find sentence boundary after end
        for i in range(best_end, min(len(content), best_end + 150)):
            if i < len(content) and content[i] in ['.', '!', '?', '\n']:
                final_end = i + 1
                break

        # Ensure the segment isn't too long
        max_length = 800
        if final_end - final_start > max_length:
            # If too long, prioritize the beginning which often contains more context
            final_end = final_start + max_length

            # Try to end at a sentence boundary
            for i in range(final_end, max(final_start, final_end - 150), -1):
                if i < len(content) and content[i] in ['.', '!', '?', '\n']:
                    final_end = i + 1
                    break

        return content[final_start:final_end].strip()

    @staticmethod
    def _assess_content_quality(content: str, query: str, relevant_text: str) -> float:
        """
        Assess the quality of content for citation purposes.

        Args:
            content: The full content of the source
            query: The original query
            relevant_text: The most relevant text segment

        Returns:
            Quality score between 0 and 1
        """
        quality_score = 0.0

        # Factor 1: Content length and substance (20% weight)
        content_length = len(content.strip())
        if content_length >= 500:
            length_score = 1.0
        elif content_length >= 200:
            length_score = 0.8
        elif content_length >= 100:
            length_score = 0.6
        elif content_length >= 50:
            length_score = 0.4
        else:
            length_score = 0.2

        quality_score += length_score * 0.2

        # Factor 2: Relevant text quality (30% weight)
        relevant_length = len(relevant_text.strip())
        if relevant_length >= 200:
            relevant_score = 1.0
        elif relevant_length >= 100:
            relevant_score = 0.8
        elif relevant_length >= 50:
            relevant_score = 0.6
        else:
            relevant_score = 0.3

        quality_score += relevant_score * 0.3

        # Factor 3: Information density (25% weight)
        # Check for informative patterns (numbers, specific terms, structured content)
        info_patterns = [
            r'\d+',  # Numbers
            r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # Proper nouns
            r'[a-zA-Z]+\s*:\s*[a-zA-Z]',  # Key-value patterns
            r'\b(?:according|research|study|analysis|data|evidence|report)\b',  # Authority indicators
            r'\b(?:because|therefore|however|furthermore|moreover|additionally)\b',  # Logical connectors
        ]

        info_density = 0
        for pattern in info_patterns:
            matches = len(re.findall(pattern, content, re.IGNORECASE))
            info_density += min(matches / 10, 1.0)  # Normalize to max 1.0 per pattern

        density_score = min(info_density / len(info_patterns), 1.0)
        quality_score += density_score * 0.25

        # Factor 4: Query-content alignment (25% weight)
        # Check if the content appears to directly address the query topic
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        # Remove stopwords for better alignment assessment
        query_words = query_words - RobustCitationEnhancer.STOPWORDS
        content_words = content_words - RobustCitationEnhancer.STOPWORDS

        if query_words:
            alignment = len(query_words.intersection(content_words)) / len(query_words)
            quality_score += alignment * 0.25

        return min(quality_score, 1.0)

    @staticmethod
    def _calculate_web_relevance(
        content: str,
        query: str,
        query_terms: List[str],
        position: int = 0
    ) -> Tuple[float, Dict[str, int]]:
        """
        Calculate relevance score for web content (adapted for shorter snippets).
        """
        content_lower = content.lower()

        # Count term occurrences
        term_matches = {}
        for term in query_terms:
            if ' ' in term:
                count = content_lower.count(term)
                if count > 0:
                    term_matches[term] = count * 2  # Phrases are important
            else:
                count = len(re.findall(r'\b' + re.escape(term) + r'\b', content_lower))
                if count > 0:
                    term_matches[term] = count

        if not term_matches:
            return 0.0, {}

        # For web content, be more lenient with term requirements
        if len(query_terms) >= 3:
            min_required_terms = max(1, len(query_terms) // 4)
        else:
            min_required_terms = 1

        if len(term_matches) < min_required_terms:
            # Check for multi-word matches
            if not any(' ' in term for term in term_matches.keys()):
                return 0.0, {}

        # Calculate score (adapted for shorter content)
        content_length = len(content)
        term_score = 0

        for term, count in term_matches.items():
            # Higher weight for web content due to shorter length
            tf = count / max(1, content_length / 50)  # Adjusted for shorter content
            idf = math.log(2 + len(term))
            term_weight = 1.5 if ' ' in term else 1.0
            term_score += tf * idf * term_weight

        # Normalize term score
        max_possible_score = len(query_terms) * 2
        normalized_term_score = min(term_score / max_possible_score, 1.0)

        # Coverage score
        coverage_score = len(term_matches) / len(query_terms)

        # Position factor (less important for web results)
        position_factor = max(0.9, 1.0 - (position * 0.02))

        # Combine factors
        final_score = (normalized_term_score * 0.5) + (coverage_score * 0.4) + (position_factor * 0.1)

        return final_score, term_matches

    @staticmethod
    def _assess_web_content_quality(content: str, query: str, relevant_text: str) -> float:
        """
        Assess content quality for web snippets (adapted for shorter content).
        """
        quality_score = 0.0

        # Factor 1: Content length (adapted for snippets)
        content_length = len(content.strip())
        if content_length >= 150:
            length_score = 1.0
        elif content_length >= 100:
            length_score = 0.8
        elif content_length >= 50:
            length_score = 0.6
        elif content_length >= 20:
            length_score = 0.4
        else:
            length_score = 0.2

        quality_score += length_score * 0.3

        # Factor 2: Information density (adapted for web content)
        info_patterns = [
            r'\d+',  # Numbers
            r'\b(?:according|research|study|analysis|data|report|news)\b',  # Authority
            r'\b(?:said|announced|revealed|confirmed|stated)\b',  # News indicators
        ]

        info_density = 0
        for pattern in info_patterns:
            matches = len(re.findall(pattern, content, re.IGNORECASE))
            info_density += min(matches / 5, 1.0)  # Lower threshold for web content

        density_score = min(info_density / len(info_patterns), 1.0)
        quality_score += density_score * 0.4

        # Factor 3: Query alignment
        query_words = set(query.lower().split()) - RobustCitationEnhancer.STOPWORDS
        content_words = set(content.lower().split()) - RobustCitationEnhancer.STOPWORDS

        if query_words:
            alignment = len(query_words.intersection(content_words)) / len(query_words)
            quality_score += alignment * 0.3

        return min(quality_score, 1.0)

    @staticmethod
    def _get_max_sources_for_query(query: str) -> int:
        """
        Determine the maximum number of sources to include based on query complexity.
        Enhanced to balance quality and quantity for better citation experience.
        """
        # Handle None or empty query
        if not query:
            return 2  # Default to minimal sources for empty queries

        query_lower = query.lower().strip()
        query_terms = RobustCitationEnhancer._extract_key_terms(query_lower)

        # Count multi-word terms (they indicate more specific queries)
        multi_word_terms = sum(1 for term in query_terms if ' ' in term)

        # Balanced source limits based on query complexity
        if len(query_terms) <= 1 and multi_word_terms == 0:
            return 2  # Simple queries get fewer sources

        # For simple queries, moderate number of sources
        if (len(query_terms) <= 3 and multi_word_terms == 0) or len(query_lower) < 30:
            return 3

        # For medium complexity queries, allow more sources
        if (len(query_terms) <= 5 and multi_word_terms <= 1) or len(query_lower) < 60:
            return 4

        # For complex queries with specific multi-word terms, allow more sources
        if multi_word_terms >= 2 or len(query_terms) > 7:
            return 5

        # Default for other queries
        return 4
